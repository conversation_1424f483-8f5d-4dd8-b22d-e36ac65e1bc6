{"admin": {"title": "Admin Dashboard", "dashboard": "Dashboard", "orders": "Orders", "products": "Products", "categories": "Categories", "categoryManagement": "Category Management", "createCategory": "Create Category", "backToProducts": "Back to Products", "hidden": "Hidden", "visible": "Visible", "featured": "Featured", "notFeatured": "Not Featured", "translations": "Translations", "images": "Images", "variants": "Variants", "attributes": "Attributes", "attributeKey": "Attribute Key", "attributeValue": "Attribute Value", "original": "Original", "offers": "Offers", "productAttributes": "Product Attributes", "addAttribute": "Add Attribute", "noAttributes": "No attributes found", "pricingOffers": "Pricing Offers", "addOffer": "<PERSON>d Offer", "minimumQuantity": "Minimum Quantity", "currency": "<PERSON><PERSON><PERSON><PERSON>", "quantityInfo": "Quantity Info", "productImages": "Product Images", "addImage": "Add Image", "confirmDeleteImage": "Are you sure you want to delete this image?", "deleting": "Deleting...", "productVariants": "Product Variants", "addVariant": "<PERSON><PERSON>", "variantName": "Variant Name", "variantType": "Variant Type", "price": "Price", "basicInformation": "Basic Information", "originalName": "Original Name", "marketplace": "Marketplace", "productUrl": "Product URL", "visibility": "Visibility", "manage": "Manage", "productTranslations": "Product Translations", "addTranslation": "Add Translation", "name": "Name", "slug": "Slug", "customers": "Customers", "pricing": "Pricing", "settings": "Settings", "logout": "Logout", "welcome": "Welcome back", "overview": "Overview", "stats": {"totalRevenue": "Total Revenue", "totalOrders": "Total Orders", "pendingOrders": "Pending Orders", "processingOrders": "Processing Orders", "totalCustomers": "Total Customers", "totalProducts": "Total Products"}, "recentOrders": "Recent Orders", "viewAllOrders": "View All Orders", "orderManagement": "Order Management", "orderDetails": "Order Details", "updateStatus": "Update Status", "trackingNumber": "Tracking Number", "trackingNumberPlaceholder": "Enter tracking number", "updateOrder": "Update Order", "updating": "Updating...", "orderUpdated": "Order updated successfully", "updateError": "Failed to update order", "searchOrders": "Search orders...", "filterByStatus": "Filter by status", "allStatuses": "All Statuses", "productManagement": "Product Management", "createProduct": "Create Product", "editProduct": "Edit Product", "productDetails": "Product Details", "searchProducts": "Search products...", "customerManagement": "Customer Management", "customerDetails": "Customer Details", "searchCustomers": "Search customers...", "assignRoles": "Assign Roles", "roles": "Roles", "permissions": "Permissions", "pricingManagement": "Pricing Management", "pricingRulePreview": "Pricing Rule Preview", "createRule": "Create Rule", "editRule": "Edit Rule", "ruleDetails": "Rule Details", "noData": "No data available", "noOrders": "No orders found", "noProducts": "No products found", "noCustomers": "No customers found", "noRules": "No pricing rules found", "search": "Search", "view": "View", "confirmDelete": "Are you sure you want to delete this item?", "deleteError": "Failed to delete item", "actions": "Actions", "edit": "Edit", "delete": "Delete", "deleteSuccess": "Item deleted successfully", "insufficientPermissions": "You don't have permission to perform this action", "warehouse": "Warehouse", "warehouseManagement": "Warehouse Management", "createReceipt": "Create Receipt", "receipt": "Receipt", "receiptDetails": "Receipt Details", "marketplaceOrderId": "Marketplace Order ID", "packageWeight": "Package Weight", "packageWeightUnit": "Weight Unit", "packageInformation": "Package Information", "trackingInformation": "Tracking Information", "carrier": "Carrier", "consolidation": "Consolidation", "consolidationStatus": "Consolidation Status", "noReceipts": "No receipts found", "backToWarehouse": "Back to Warehouse", "received": "Received", "receivedAt": "Received At", "addTracking": "Add Tracking", "markAsShipped": "<PERSON> as Shipped", "shippingLabel": "Shipping Label", "shippingLabelUrl": "Shipping Label URL", "viewLabel": "View Label", "searchMarketplaceOrderId": "Search by marketplace order ID...", "marketplaceOrderIdHelp": "Enter the order ID from Taobao, Pinduoduo, or Alibaba", "payments": "Payments", "paymentManagement": "Payment Management", "payment": "Payment", "paymentDetails": "Payment Details", "paymentInformation": "Payment Information", "transactionId": "Transaction ID", "paymentMethod": "Payment Method", "amount": "Amount", "method": "Method", "date": "Date", "refund": "Refund", "processRefund": "Process Refund", "refundDescription": "Process a refund for this payment", "noPayments": "No payments found", "backToPayments": "Back to Payments", "newStatus": "New Status", "orderInformation": "Order Information", "orderTotal": "Order Total", "orderStatus": "Order Status", "searchOrderId": "Search by order ID...", "recordPayment": "Record Payment", "recordPaymentDescription": "Record a payment received for this order", "recording": "Recording...", "recordPaymentError": "Failed to record payment", "transactionIdRequired": "Transaction ID is required", "paymentMethodRequired": "Payment method is required", "amountRequired": "Valid amount is required", "transactionIdPlaceholder": "e.g., TXN123456789", "transactionIdHelp": "Enter the unique transaction ID from your payment processor", "selectPaymentMethod": "Select payment method", "amountHelp": "Enter the payment amount in the order currency", "paymentMethodBankTransfer": "Bank Transfer", "paymentMethodCreditCard": "Credit Card", "paymentMethodPaypal": "PayPal", "paymentMethodCash": "Cash", "paymentMethodOther": "Other", "backToOrder": "Back to Order", "paymentAlreadyExists": "Payment Already Recorded", "paymentAlreadyExistsDescription": "This order already has a payment recorded. You can view the existing payment details.", "viewExistingPayment": "View Existing Payment", "unit": "Unit", "creating": "Creating...", "processing": "Processing...", "optional": "Optional", "weight": "Weight", "status": "Status", "created": "Created", "orderId": "Order ID", "customer": "Customer", "product": "Product", "quantity": "Quantity", "orderItemDetails": "Order Item Details", "showing": "Showing", "of": "of", "cancel": "Cancel", "statusPending": "Pending", "statusMatched": "Matched", "statusShipped": "Shipped", "statusSucceeded": "Succeeded", "statusFailed": "Failed", "statusRefunded": "Refunded", "warehouseDescription": "Manage warehouse receipts and package tracking", "newReceiptDescription": "Record a new package arrival at the warehouse", "paymentsDescription": "Manage payments and transaction records", "notAvailable": "N/A", "trackingNumberExample": "e.g., 1Z999AA10123456784", "carrierPlaceholder": "e.g., DHL, FedEx, UPS", "urlPlaceholder": "https://...", "marketplaceOrderIdPlaceholder": "e.g., TB123456789", "weightPlaceholder": "0.00", "unitGrams": "g (grams)", "unitKilograms": "kg (kilograms)", "unitPounds": "lb (pounds)", "profile": "Profile", "create": "Create", "createOrder": "Create Order", "addCustomer": "Add Customer", "manageCategoriesDescription": "Manage and organize your product categories", "expandAll": "Expand All", "collapseAll": "Collapse All", "addSubcategory": "Add Subcategory", "viewDetails": "View Details", "totalCategories": "Total Categories", "rootCategories": "Root Categories", "totalProducts": "Total Products", "addProduct": "Add Product", "attributeKeyPlaceholder": "Enter attribute key", "attributeValuePlaceholder": "Enter attribute value", "availableQuantity": "Available Quantity", "confirmDeleteAttribute": "Are you sure you want to delete this attribute?", "confirmDeleteOffer": "Are you sure you want to delete this offer?", "confirmDeleteVariant": "Are you sure you want to delete this variant?", "imageType": "Image Type", "imageUrl": "Image URL", "language": "Language", "manageCategories": "Manage Categories", "manageTranslations": "Manage Translations", "minPrice": "Minimum Price", "minQuantity": "Minimum Quantity", "noCategories": "No categories assigned", "noCategoriesFound": "No categories found", "noImages": "No images found", "noOffers": "No offers found", "noTranslations": "No translations found", "noVariants": "No variants found", "notSet": "Not Set", "notSpecified": "Not Specified", "priceFrom": "Price From", "priceTo": "Price To", "productCategories": "Product Categories", "productNotFound": "Product Not Found", "quantityInfoPlaceholder": "Enter quantity information", "saveCategories": "Save Categories", "saveTranslations": "Save Translations", "searchCategories": "Search categories...", "selectCategories": "Select Categories", "translatedKey": "Translated Key", "translatedValue": "Translated Value", "unnamedProduct": "Unnamed Product", "unlimited": "Unlimited", "orderSummary": "Order Summary", "total": "Total", "@": "@", "/": "/", "/en/admin": "/en/admin", "atLeastOneCompleteTranslationRequired": "At least one complete translation is required", "atLeastOneTranslationRequired": "At least one translation is required", "attribute": "Attribute", "awaitingPayment": "Awaiting Payment", "backToCategories": "Back to Categories", "backToCategory": "Back to Category", "backToProduct": "Back to Product", "basicInfo": "Basic Info", "cannotDeleteCategoryWithChildren": "Cannot delete category with children", "cannotDeleteCategoryWithProducts": "Cannot delete category with products", "category": "Category", "categoryDescription": "Category Description", "categoryDetails": "Category Details", "categoryId": "Category ID", "categoryName": "Category Name", "categoryNotFound": "Category Not Found", "categoryTranslations": "Category Translations", "confirm": "Confirm", "confirmDeleteCategory": "Are you sure you want to delete this category?", "confirmDeleteCategoryDescription": "This action cannot be undone. This will permanently delete the category and remove it from our servers.", "createCategoryDescription": "Create a new product category", "createFirstCategory": "Create First Category", "createProductDescription": "Add a new product to your catalog", "createSubcategory": "Create Subcategory", "createSubcategoryDescription": "Create a subcategory under this category", "description": "Description", "editCategory": "Edit Category", "editing": "Editing...", "Failed to delete category": "Failed to delete category", "hierarchy": "Hierarchy", "next": "Next", "offer": "Offer", "order": "Order", "originalNamePlaceholder": "Enter original name", "page": "Page", "parentCategory": "Parent Category", "paymentStatus": "Payment Status", "previous": "Previous", "productName": "Product Name", "save": "Save", "rootCategory": "Root Category", "selectParentCategory": "Select Parent Category", "subcategories": "Subcategories", "subcategoryDetails": "Subcategory Details", "type": "Type", "unnamedCategory": "Unnamed Category", "updateCategory": "Update Category", "updateProduct": "Update Product", "variant": "<PERSON><PERSON><PERSON>", "variantNamePlaceholder": "Enter variant name", "variantTypePlaceholder": "Enter variant type", "visibleToCustomers": "Visible to Customers", "weightUnit": "Weight Unit", "confirmToggleVisibility": "Confirm Visibility Change", "confirmToggleVisibilityDescription": "Are you sure you want to change the visibility of this product?", "confirmToggleVisibilityToHidden": "Are you sure you want to hide this product? It will no longer be visible to customers.", "confirmToggleVisibilityToVisible": "Are you sure you want to make this product visible? It will be available for customers to purchase."}}