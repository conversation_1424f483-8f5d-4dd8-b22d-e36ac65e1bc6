{"pricing": {"rules": "Pricing Rules", "rulesDescription": "Pricing rules are applied in order of priority (higher number = higher priority). Rules can be based on quantity, price ranges, or apply globally.", "createRule": "Create Rule", "ruleName": "Rule Name", "conditionType": "Condition Type", "conditionValue": "Condition Value", "global": "Global", "category": "Category", "productId": "Product ID", "marketplace": "Marketplace", "markupType": "Markup Type", "percentage": "Percentage", "fixedAmountAdd": "Fixed Amount Add", "fixedAmountSet": "Fixed Amount Set", "markupValue": "Markup Value", "priority": "Priority", "active": "Active", "inactive": "Inactive", "rulesApplied": "Pricing rules are applied to product base prices before currency conversion.", "noRules": "No pricing rules found", "previewRule": "Preview Rule", "previewDescription": "See how this pricing rule affects product prices", "noAffectedProducts": "No products are affected by this rule", "affectedProducts": "Affected Products", "avgPriceChange": "Avg Price Change", "maxIncrease": "Max Increase", "maxDecrease": "Max Decrease", "product": "Product", "originalPrice": "Original Price", "adjustedPrice": "Adjusted Price", "priceDifference": "Price Difference", "percentageChange": "Change %", "previewNote": "This preview shows the first 50 affected products. Prices are calculated in CNY before currency conversion.", "previous": "Previous", "next": "Next", "showingPartial": "Showing partial results", "showingAll": "Showing all results", "profile": "Profile", "create": "Create", "createOrder": "Create Order", "addCustomer": "Add Customer", "manageCategoriesDescription": "Manage product categories and organize them", "expandAll": "Expand All", "collapseAll": "Collapse All", "addSubcategory": "Add Subcategory", "viewDetails": "View Details", "totalCategories": "Total Categories", "rootCategories": "Root Categories", "totalProducts": "Total Products", "addProduct": "Add Product"}}