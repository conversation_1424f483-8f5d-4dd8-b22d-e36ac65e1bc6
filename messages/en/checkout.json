{"checkout": {"title": "Checkout", "shippingAddress": "Shipping Address", "selectShippingAddress": "Select your shipping address", "selectAddress": "Select an address", "selectAddressError": "Please select a shipping address", "emptyCartError": "Your cart is empty", "orderError": "Failed to place order. Please try again.", "noAddressesFound": "No addresses found", "addAddressInProfile": "Please add an address in your profile first", "addAddressToContinue": "Please add an address to continue with your order", "addNewAddress": "Add New Address", "useThisAddress": "Use This Address", "paymentMethod": "Payment Method", "orderSummary": "Order Summary", "placeOrder": "Place Order", "processing": "Processing...", "placingOrder": "Placing order...", "loading": "Loading...", "orderSuccess": "Order Placed Successfully!", "orderPlaced": "Order Placed Successfully!", "orderNumber": "Order Number", "thankYou": "Thank you for your order", "thankYouCustomer": "Thank you {name} for your order", "orderPlacedSuccessfully": "Your order has been placed successfully", "paymentInstructions": "Payment Instructions", "paymentInstructionsText": "Please transfer the total amount to our bank account. Your order will be processed once we confirm your payment.", "addressLine1": "Address Line 1", "addressLine2": "Address Line 2 (Optional)", "city": "City", "state": "State/Province", "postalCode": "Postal Code", "country": "Country", "saveAddress": "Save Address", "setAsDefault": "Set as default address", "items": "Items", "orderItems": "Order Items", "itemsInYourOrder": "item(s) in your order", "quantity": "Quantity", "total": "Total", "variant": "<PERSON><PERSON><PERSON>", "notSpecified": "Not specified", "variantNotSpecified": "Variant not specified", "subtotal": "Subtotal", "shipping": "Shipping", "bankTransferDetails": "Bank Transfer Details", "bankName": "Bank Name", "accountName": "Account Name", "accountNumber": "Account Number", "swiftCode": "SWIFT Code", "reference": "Reference", "paymentNote": "Please include your order number in the payment reference for faster processing.", "bankNameValue": "Example Bank", "accountNameValue": "MaoMao Trading Co.", "accountNumberValue": "**********", "swiftCodeValue": "EXAMPLSW", "whatNext": "What happens next?", "step1Title": "Payment Confirmation", "step1Description": "We'll send you payment instructions via email", "step2Title": "Order Processing", "step2Description": "Once payment is confirmed, we'll start processing your order", "step3Title": "Shipping", "step3Description": "Your order will be shipped and you'll receive tracking information", "viewOrder": "View Order Details", "continueShopping": "Continue Shopping", "selectShippingMethod": "Select Shipping Method", "shippingInformation": "Shipping Information", "shippingMethod": "Shipping Method", "shippingCost": "Shipping Cost", "shippingCostMessage": "Shipping cost will be calculated and updated once your order is ready for shipping", "shippingBoat": "Sea Shipping", "shippingPlane": "Air Shipping", "shippingBoatTime": "~2 months", "shippingBoatDescription": "Economical option with longer delivery time", "shippingPlaneTime": "~2 weeks", "shippingPlaneDescription": "Faster delivery with higher cost", "continueToConfirmation": "Continue to Order Confirmation", "confirmOrder": "Confirm Your Order", "reviewOrderDetails": "Please review your order details before proceeding to payment", "confirmAndPlaceOrder": "Proceed to Payment", "back": "Back", "streetAddress": "Street Address", "apartmentSuite": "Apartment, Suite, etc.", "setAsDefaultAddress": "Set as default address", "cancel": "Cancel", "saving": "Saving...", "addAddress": "Add Address", "deleteAddress": "Delete Address", "confirmDeleteAddress": "Are you sure you want to delete this address?", "deleteAddressTitle": "Delete Address", "deleteAddressDescription": "This action cannot be undone. This will permanently delete the selected address from your account.", "keepAddress": "Keep Address", "yesDelete": "Yes, Delete Address", "shippingNote": "Shipping cost will be calculated and updated once your order is ready for shipping", "orderNotFound": "Order with ID {orderId} not found.", "unauthorizedAccess": "Unauthorized attempt to access order.", "failedFetchOrder": "Failed to fetch and process order:", "selectPaymentMethod": "Please select your preferred payment method.", "mobileMoney": "Mobile Money", "mobileMoneyDesc": "Pay instantly with MTN or Orange.", "bankTransfer": "Bank Transfer", "bankTransferDesc": "Pay via SEPA or Global wire transfer.", "cardPayment": "Card Payment", "cardPaymentDesc": "Visa, MasterCard, etc. (Coming soon)", "cardPaymentComingSoon": "Card payment will be available soon.", "payWithMobileMoney": "Pay with Mobile Money", "enterPhoneNumber": "Please enter the phone number you wish to use for the payment. A prompt will be sent to this number.", "phoneNumber": "Phone Number", "payingAs": "Paying as:", "confirmPayment": "Confirm Payment", "checkYourPhone": "Check Your Phone", "paymentPromptSent": "We've sent a payment prompt to your mobile phone. Please approve the transaction to complete your order.", "paymentMethodLabel": "Payment Method:", "mobileMoneyLabel": "Mobile Money", "instructions": "Instructions:", "instructions1": "Follow the instructions sent via SMS.", "instructions2": "Once you have approved the payment, click the button below.", "instructions3": "If you don't receive a prompt, please try changing the number.", "confirming": "Confirming...", "iHaveCompletedPayment": "I Have Completed the Payment", "changePhoneNumber": "Change Phone Number", "cancelPayment": "Cancel Payment", "offlineBankTransfer": "Offline Bank Transfer", "bankTransferInstructions": "Please transfer the total amount to one of the accounts below. Your order will be processed once payment is confirmed.", "sepaForEU": "SEPA (for EU)", "global": "Global", "beneficiary": "Beneficiary", "iban": "IBAN", "swiftBic": "SWIFT/BIC", "processingFee": "Processing Fee:", "accountNo": "Account No.", "bankCode": "Bank Code", "branchCode": "Branch Code", "currencies": "Currencies", "iHaveTransferredMoney": "I Have Transferred the Money", "showOrderSummary": "Show order summary", "free": "Free", "taxes": "Taxes", "calculatedAtNextStep": "Calculated at next step", "qty": "Qty:", "paymentFailed": "Payment failed", "missingOrderInfo": "Missing required order information.", "reorderAll": "Reorder All", "reorder": "Reorder"}}