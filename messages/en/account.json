{"account": {"title": "My Account", "myAccount": "My Account", "dashboard": "Dashboard", "dashboardOverview": "Overview of your account dashboard", "orders": "Orders", "orderHistory": "Order History", "profile": "Profile", "profileInformation": "Profile Information", "addresses": "Addresses", "savedAddresses": "Saved Addresses", "manageAddresses": "Manage Addresses", "manageAddressesDescription": "Add, edit, or remove your saved addresses", "settings": "Settings", "welcomeBack": "Welcome back, {name}", "welcome": "Welcome", "recentOrders": "Recent Orders", "recentOrdersDescription": "View your most recent orders", "viewAll": "View All", "viewAllOrders": "View All Orders", "noOrders": "You haven't placed any orders yet", "startShopping": "Start Shopping", "orderNumber": "Order #{id}", "orderDate": "Ordered on {date}", "orderStatus": "Status", "orderTotal": "Total", "viewOrder": "View Order", "viewDetails": "View Details", "trackOrder": "Track Order", "updateProfile": "Update Profile", "profileUpdated": "Profile updated successfully", "updateError": "Failed to update profile", "totalOrders": "Total Orders", "accountStatus": "Account Status", "active": "Active", "items": "items", "placed": "Placed", "previous": "Previous", "next": "Next", "errorLoadingOrders": "Error loading orders", "retry": "Retry", "email": "Email", "emailCannotChange": "Email cannot be changed", "fullName": "Full Name", "phone": "Phone Number", "preferredCurrency": "Preferred Currency", "saving": "Saving...", "saveChanges": "Save Changes", "noAddresses": "No saved addresses", "addNewAddress": "Add New Address", "editAddress": "Edit Address", "confirmDeleteAddress": "Are you sure you want to delete this address?", "deleteError": "Failed to delete address", "addressAdded": "Address added successfully", "addressUpdated": "Address updated successfully", "saveError": "Failed to save address", "cancel": "Cancel", "addressLine1": "Address Line 1", "addressLine2": "Address Line 2", "streetAddress": "Street address, P.O. box, company name, c/o", "apartmentSuite": "Apartment, suite, unit, building, floor, etc.", "city": "City", "state": "State/Province", "postalCode": "Postal Code", "country": "Country", "setAsDefaultAddress": "Set as default address", "default": "<PERSON><PERSON><PERSON>", "addAddress": "Add Address", "manageProfileDescription": "Update your personal information and preferences", "manageProfile": "Manage Profile", "editProfile": "Edit Profile", "viewOrdersDescription": "View and track your order history", "viewOrders": "View Orders", "favorites": "Favorites", "favoritesDescription": "Save products you love and track their prices", "wishlist": "Wishlist", "wishlistItems": "Wishlist Items", "viewWishlistDescription": "View and manage your wishlist", "quickActions": "Quick Actions", "quickActionsDescription": "Common actions you can take", "viewWishlist": "View Wishlist", "previouslyBought": "Previously Bought", "noPreviousOrders": "No previous orders found", "totalItems": "Total Items", "totalValue": "Total Value", "avgPrice": "Average Price", "readyToOrder": "Ready to Order", "shareWishlist": "Share Wishlist", "addAllToCart": "Add All to Cart", "sortNewest": "Newest First", "sortOldest": "Oldest First", "sortPriceLow": "Price: Low to High", "sortPriceHigh": "Price: High to Low", "sortName": "Name A-Z", "addedOn": "Added on", "adding": "Adding...", "loading": "Loading...", "loadMore": "Load More", "bought": "Bought", "reorder": "Reorder", "noImage": "No Image", "continueShopping": "Continue Shopping", "viewCart": "View Cart", "orderPromotionText": "You have {count} items ready to order. Complete your purchase today!", "emptyWishlistTitle": "Your wishlist is empty", "emptyWishlistDescription": "Start adding products to your wishlist to keep track of items you love and get notified about price changes.", "saveForLater": "Save for Later", "saveForLaterDesc": "Keep track of products you're interested in", "trackPrices": "Track Prices", "trackPricesDesc": "Get notified when prices drop", "quickReorder": "Quick Reorder", "quickReorderDesc": "Easily reorder items you've bought before", "howToUseWishlist": "How to use your wishlist", "step1Title": "Browse products", "step1Desc": "Find products you like while shopping", "step2Title": "Click the heart icon", "step2Desc": "Add items to your wishlist for later", "step3Title": "Track price changes", "step3Desc": "We'll notify you about price drops", "step4Title": "Order when ready", "step4Desc": "Add to cart or buy now when you're ready", "somethingWentWrong": "Something went wrong", "dashboardDataError": "We couldn't load your dashboard data. Please try again.", "tryAgain": "Try Again", "unpaidOrders": "Unpaid Orders", "unpaidOrdersReminder": "You have {count} unpaid orders", "viewUnpaidOrders": "View Unpaid Orders", "continuePayment": "Continue Payment"}}