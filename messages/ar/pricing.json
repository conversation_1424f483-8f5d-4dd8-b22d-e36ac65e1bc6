{"pricing": {"rules": "قواعد التسعير", "rulesDescription": "يتم تطبيق قواعد التسعير حسب أولوية (الرقم الأعلى = أولوية أعلى). يمكن أن تكون القواعد مبنية على الكمية أو نطاقات الأسعار أو تطبيق عام.", "createRule": "إنشاء قاعدة", "ruleName": "اسم القاعدة", "conditionType": "نوع الشرط", "conditionValue": "قيمة الشرط", "global": "عام", "category": "الفئة", "productId": "معر<PERSON> المنتج", "marketplace": "السوق", "markupType": "نوع التسعير", "percentage": "النسبة المئوية", "fixedAmountAdd": "إضافة مبلغ ثابت", "fixedAmountSet": "تحديد مبلغ ثابت", "markupValue": "قيمة التسعير", "priority": "الأولوية", "active": "نشط", "inactive": "غير نشط", "rulesApplied": "يتم تطبيق قواعد التسعير على أسعار المنتجات الأساسية قبل تحويل العملة.", "noRules": "لم يتم العثور على قواعد تسعير", "previewRule": "معاينة القاعدة", "previewDescription": "رؤية كيف تؤثر هذه القاعدة على أسعار المنتجات", "noAffectedProducts": "لا توجد منتجات متأثرة بهذه القاعدة", "affectedProducts": "المنتجات المتأثرة", "avgPriceChange": "متوسط التغيير", "maxIncrease": "أقصى زيادة", "maxDecrease": "أقصى انخفاض", "product": "المنتج", "originalPrice": "السعر الأصلي", "adjustedPrice": "السعر المعدل", "priceDifference": "الفرق", "percentageChange": "التغيير %", "previewNote": "يعرض هذا المعاينة أول 50 منتج متأثر. يتم حساب الأسعار باليوان الصيني قبل تحويل العملة.", "previous": "السابق", "next": "التالي", "showingPartial": "عرض جزئي", "showingAll": "عر<PERSON> الكل", "profile": "الملف الشخصي", "create": "إنشاء", "createOrder": "إنشاء طلب", "addCustomer": "إضافة عميل", "manageCategoriesDescription": "إدارة وتنظيم فئات المنتجات", "expandAll": "توسيع الكل", "collapseAll": "طي الكل", "addSubcategory": "إضافة فئة فرعية", "viewDetails": "عرض التفاصيل", "totalCategories": "إجمالي الفئات", "rootCategories": "الفئات الجذرية", "totalProducts": "إجمالي المنتجات", "addProduct": "إضافة منتج"}}