{"admin": {"title": "Tableau de bord administrateur", "dashboard": "Tableau de bord", "orders": "Commandes", "products": "Produits", "categories": "Catégories", "categoryManagement": "Gestion des catégories", "createCategory": "<PERSON><PERSON>er une catégorie", "backToProducts": "Retour aux produits", "hidden": "<PERSON><PERSON><PERSON><PERSON>", "visible": "Visible", "featured": "En vedette", "notFeatured": "Pas en vedette", "translations": "Traductions", "images": "Images", "variants": "<PERSON><PERSON><PERSON>", "attributes": "Attributs", "attributeKey": "Clé d'attribut", "attributeValue": "Valeur d'attribut", "original": "Original", "offers": "Offres", "productAttributes": "Attributs du produit", "addAttribute": "Ajouter un attribut", "noAttributes": "Aucun attribut trouvé", "pricingOffers": "Offres de tarification", "addOffer": "Ajouter une offre", "minimumQuantity": "Quantité minimale", "currency": "<PERSON><PERSON>", "quantityInfo": "Informations sur la quantité", "productImages": "Images du produit", "addImage": "Ajouter une image", "confirmDeleteImage": "Êtes-vous sûr de vouloir supprimer cette image?", "deleting": "Suppression...", "productVariants": "Variantes du produit", "addVariant": "Ajouter une variante", "variantName": "Nom de la variante", "variantType": "Type de variante", "price": "Prix", "basicInformation": "Informations de base", "originalName": "Nom original", "marketplace": "<PERSON><PERSON>", "productUrl": "URL du produit", "visibility": "Visibilité", "manage": "<PERSON><PERSON><PERSON>", "productTranslations": "Traductions du produit", "addTranslation": "Ajouter une traduction", "name": "Nom", "slug": "Slug", "customers": "Clients", "pricing": "Tarification", "settings": "Paramètres", "logout": "Déconnexion", "welcome": "Bon retour", "overview": "<PERSON><PERSON><PERSON><PERSON>", "stats": {"totalRevenue": "Revenu total", "totalOrders": "Total des commandes", "pendingOrders": "Commandes en attente", "processingOrders": "Commandes en cours", "totalCustomers": "Total des clients", "totalProducts": "Total des produits"}, "recentOrders": "Commandes récentes", "viewAllOrders": "Voir toutes les commandes", "orderManagement": "Gestion des commandes", "orderDetails": "<PERSON><PERSON><PERSON> de la commande", "updateStatus": "Mettre à jour le statut", "trackingNumber": "Numéro de suivi", "trackingNumberPlaceholder": "Entrer le numéro de suivi", "updateOrder": "Mettre à jour la commande", "updating": "Mise à jour...", "orderUpdated": "Commande mise à jour avec succès", "updateError": "Échec de la mise à jour de la commande", "searchOrders": "Rechercher des commandes...", "filterByStatus": "Filtrer par statut", "allStatuses": "Tous les statuts", "productManagement": "Gestion des produits", "createProduct": "Créer un produit", "editProduct": "Modifier le produit", "productDetails": "Détails du produit", "searchProducts": "Rechercher des produits...", "customerManagement": "Gestion des clients", "customerDetails": "Détails du client", "searchCustomers": "Rechercher des clients...", "assignRoles": "Attribuer des rôles", "roles": "<PERSON><PERSON><PERSON>", "permissions": "Permissions", "pricingManagement": "Gestion de la tarification", "pricingRulePreview": "Aperçu de la règle de tarification", "createRule": "<PERSON><PERSON><PERSON> une règle", "editRule": "Modifier la règle", "ruleDetails": "<PERSON><PERSON><PERSON> de la règle", "noData": "<PERSON><PERSON><PERSON> donnée disponible", "noOrders": "Aucune commande trouvée", "noProducts": "Aucun produit trouvé", "noCustomers": "Aucun client trouvé", "noRules": "Aucune règle de tarification trouvée", "search": "<PERSON><PERSON><PERSON>", "view": "Voir", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "actions": "Actions", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cet élément?", "deleteSuccess": "Élément supprimé avec succès", "deleteError": "Échec de la suppression de l'élément", "insufficientPermissions": "Vous n'avez pas la permission d'effectuer cette action", "warehouse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warehouseManagement": "Gestion d'en<PERSON>pôt", "createReceipt": "C<PERSON>er un reçu", "receipt": "<PERSON><PERSON><PERSON>", "receiptDetails": "<PERSON>é<PERSON> du reçu", "marketplaceOrderId": "ID de commande marketplace", "packageWeight": "Poids du colis", "packageWeightUnit": "Unité de poids", "packageInformation": "Informations sur le colis", "trackingInformation": "Informations de suivi", "carrier": "Transporteur", "consolidation": "Consolidation", "consolidationStatus": "Statut de consolidation", "noReceipts": "<PERSON><PERSON>n reçu trouvé", "backToWarehouse": "Retour à l'entrepôt", "received": "<PERSON><PERSON><PERSON>", "receivedAt": "<PERSON><PERSON><PERSON> le", "addTracking": "A<PERSON>ter le suivi", "markAsShipped": "Marquer comme expédié", "shippingLabel": "Étiquette d'expédition", "shippingLabelUrl": "URL de l'étiquette d'expédition", "viewLabel": "Voir l'étiquette", "searchMarketplaceOrderId": "Rechercher par ID de commande marketplace...", "marketplaceOrderIdHelp": "Entrez l'ID de commande de Taobao, <PERSON><PERSON><PERSON><PERSON><PERSON> ou Ali<PERSON>ba", "payments": "Paiements", "paymentManagement": "Gestion des paiements", "payment": "Paiement", "paymentDetails": "Détails du paiement", "paymentInformation": "Informations de paiement", "transactionId": "ID de transaction", "paymentMethod": "Mode de paiement", "amount": "<PERSON><PERSON>", "method": "Méthode", "date": "Date", "refund": "Remboursement", "processRefund": "Trai<PERSON> le remboursement", "refundDescription": "Traiter un remboursement pour ce paiement", "noPayments": "Aucun paiement trouvé", "backToPayments": "Retour aux paiements", "newStatus": "Nouveau statut", "orderInformation": "Informations sur la commande", "orderTotal": "Total de la commande", "orderStatus": "Statut de la commande", "searchOrderId": "Rechercher par ID de commande...", "unit": "Unité", "creating": "Création...", "processing": "Traitement...", "optional": "Optionnel", "weight": "Poids", "status": "Statut", "created": "<PERSON><PERSON><PERSON>", "orderId": "ID de commande", "customer": "Client", "product": "Produit", "quantity": "Quantité", "orderItemDetails": "<PERSON><PERSON><PERSON> de l'article de commande", "showing": "Affichage", "of": "de", "cancel": "Annuler", "statusPending": "En attente", "statusMatched": "<PERSON><PERSON><PERSON><PERSON>", "statusShipped": "Expédié", "statusSucceeded": "<PERSON><PERSON><PERSON><PERSON>", "statusFailed": "<PERSON><PERSON><PERSON>", "statusRefunded": "Re<PERSON><PERSON><PERSON>", "warehouseDescription": "<PERSON><PERSON><PERSON> les reçus d'entrepôt et le suivi des colis", "newReceiptDescription": "Enregistrer l'arrivée d'un nouveau colis à l'entrepôt", "paymentsDescription": "Gérer les paiements et les enregistrements de transactions", "notAvailable": "N/D", "trackingNumberExample": "ex: 1Z999AA10123456784", "carrierPlaceholder": "ex: DHL, FedEx, UPS", "urlPlaceholder": "https://...", "marketplaceOrderIdPlaceholder": "ex: TB123456789", "weightPlaceholder": "0.00", "unitGrams": "g (grammes)", "unitKilograms": "kg (kilogrammes)", "unitPounds": "lb (livres)", "recordPayment": "Enregistrer le paiement", "recordPaymentDescription": "Enregistrer un nouveau paiement pour cette commande", "recording": "Enregistrement...", "recordPaymentError": "Échec de l'enregistrement du paiement", "transactionIdRequired": "ID de transaction requis", "paymentMethodRequired": "Méthode de paiement requise", "amountRequired": "Montant requis", "transactionIdPlaceholder": "Entrez l'ID de transaction", "transactionIdHelp": "ID de transaction du fournisseur de paiement", "selectPaymentMethod": "Sélectionnez la méthode de paiement", "amountHelp": "Montant payé dans la devise spécifiée", "paymentMethodBankTransfer": "Virement bancaire", "paymentMethodCreditCard": "Carte de <PERSON>", "paymentMethodPaypal": "PayPal", "paymentMethodCash": "Espèces", "paymentMethodOther": "<PERSON><PERSON>", "backToOrder": "Retour à la commande", "paymentAlreadyExists": "Le paiement existe déjà", "paymentAlreadyExistsDescription": "Un paiement est déjà enregistré pour cette commande", "viewExistingPayment": "Voir le paiement existant", "profile": "Profil", "create": "<PERSON><PERSON><PERSON>", "createOrder": "<PERSON><PERSON><PERSON> une commande", "addCustomer": "Ajouter un client", "manageCategoriesDescription": "<PERSON><PERSON>rer les catégories de produits et les organiser", "expandAll": "<PERSON><PERSON> d<PERSON>vel<PERSON>per", "collapseAll": "<PERSON><PERSON> r<PERSON>", "addSubcategory": "Ajouter une sous-catégorie", "viewDetails": "Voir les détails", "totalCategories": "Total des catégories", "rootCategories": "Catégories racines", "totalProducts": "Total des produits", "addProduct": "Ajouter un produit", "attributeKeyPlaceholder": "Entrez la clé d'attribut", "attributeValuePlaceholder": "Entrez la valeur d'attribut", "availableQuantity": "Quantité disponible", "confirmDeleteAttribute": "Êtes-vous sûr de vouloir supprimer cet attribut ?", "confirmDeleteOffer": "Êtes-vous sûr de vouloir supprimer cette offre ?", "confirmDeleteVariant": "Êtes-vous sûr de vouloir supprimer cette variante ?", "imageType": "Type d'image", "imageUrl": "URL de l'image", "language": "<PERSON><PERSON>", "manageCategories": "<PERSON><PERSON><PERSON> les catégories", "manageTranslations": "<PERSON><PERSON><PERSON> les traductions", "minPrice": "Prix minimum", "minQuantity": "Quantité minimale", "noCategories": "Aucune catégorie <PERSON>", "noCategoriesFound": "<PERSON><PERSON>ne catégorie trouvée", "noImages": "Aucune image trouvée", "noOffers": "<PERSON><PERSON>ne offre trouvée", "noTranslations": "Aucune traduction trouvée", "noVariants": "Aucune variante trouvée", "notSet": "Non défini", "notSpecified": "Non spécifié", "priceFrom": "Prix de", "priceTo": "Prix à", "productCategories": "Catégories de produit", "productNotFound": "Produit non trouvé", "quantityInfoPlaceholder": "Entrez les informations sur la quantité", "saveCategories": "Enregistrer les catégories", "saveTranslations": "Enregistrer les traductions", "searchCategories": "Rechercher des catégories...", "selectCategories": "Sélectionner les catégories", "translatedKey": "<PERSON><PERSON> traduite", "translatedValue": "<PERSON><PERSON> traduite", "unnamedProduct": "Produit sans nom", "unlimited": "Illimité", "orderSummary": "<PERSON><PERSON><PERSON><PERSON> de la commande", "total": "Total", "@": "@", "/": "/", "/en/admin": "/en/admin", "atLeastOneCompleteTranslationRequired": "Au moins une traduction complète est requise", "atLeastOneTranslationRequired": "Au moins une traduction est requise", "attribute": "Attribut", "awaitingPayment": "En attente de paiement", "backToCategories": "Retour aux catégories", "backToCategory": "Retour à la catégorie", "backToProduct": "Retour au produit", "basicInfo": "Informations de base", "cannotDeleteCategoryWithChildren": "Impossible de supprimer une catégorie avec des enfants", "cannotDeleteCategoryWithProducts": "Impossible de supprimer une catégorie avec des produits", "category": "<PERSON><PERSON><PERSON><PERSON>", "categoryDescription": "Description de la catégorie", "categoryDetails": "<PERSON>é<PERSON> de la catégorie", "categoryId": "ID de catégorie", "categoryName": "Nom de la catégorie", "categoryNotFound": "Catégorie non trouvée", "categoryTranslations": "Traductions de la catégorie", "confirmDeleteCategory": "Êtes-vous sûr de vouloir supprimer cette catégorie ?", "confirmDeleteCategoryDescription": "Cette action ne peut pas être annulée. Cela supprimera définitivement la catégorie et l'éliminera de nos serveurs.", "createCategoryDescription": "<PERSON><PERSON>er une nouvelle catégorie de produit", "createFirstCategory": "Créer la première catégorie", "createProductDescription": "Ajouter un nouveau produit à votre catalogue", "createSubcategory": "<PERSON><PERSON><PERSON> une sous-catégorie", "createSubcategoryDescription": "<PERSON><PERSON><PERSON> une sous-catégorie sous cette catégorie", "description": "Description", "editCategory": "Modifier la catégorie", "editing": "Modification...", "Failed to delete category": "Échec de la suppression de la catégorie", "hierarchy": "Hiéra<PERSON>ie", "next": "Suivant", "offer": "Offre", "order": "Commande", "originalNamePlaceholder": "Entrez le nom original", "page": "Page", "parentCategory": "Catégorie parente", "paymentStatus": "Statut du paiement", "previous": "Précédent", "productName": "Nom du produit", "save": "Enregistrer", "rootCategory": "<PERSON><PERSON><PERSON><PERSON> rac<PERSON>", "selectParentCategory": "Sélectionner la catégorie parente", "subcategories": "Sous-catégories", "subcategoryDetails": "<PERSON><PERSON><PERSON> de la sous-catégorie", "type": "Type", "unnamedCategory": "Catégorie sans nom", "updateCategory": "Mettre à jour la catégorie", "updateProduct": "Mettre à jour le produit", "variant": "<PERSON><PERSON><PERSON>", "variantNamePlaceholder": "Entrez le nom de la variante", "variantTypePlaceholder": "Entrez le type de variante", "visibleToCustomers": "Visible aux clients", "confirm": "Confirmer", "confirmToggleVisibility": "Confirmer le changement de visibilité", "confirmToggleVisibilityDescription": "Êtes-vous sûr de vouloir changer la visibilité de ce produit ?", "confirmToggleVisibilityToHidden": "Êtes-vous sûr de vouloir masquer ce produit ? Il ne sera plus visible pour les clients.", "confirmToggleVisibilityToVisible": "Êtes-vous sûr de vouloir rendre ce produit visible ? Il sera disponible à l'achat pour les clients.", "weightUnit": "Unité de poids"}}