{"pricing": {"rules": "Règles de tarification", "rulesDescription": "Les règles de tarification sont appliquées par ordre de priorité (numéro plus élevé = priorité plus élevée). Les règles peuvent être basées sur la quantité, les plages de prix ou s'appliquer globalement.", "createRule": "<PERSON><PERSON><PERSON> une règle", "ruleName": "Nom de la règle", "conditionType": "Type de condition", "conditionValue": "Valeur de condition", "global": "Global", "category": "<PERSON><PERSON><PERSON><PERSON>", "productId": "ID produit", "marketplace": "<PERSON><PERSON>", "markupType": "Type de majoration", "percentage": "Pourcentage", "fixedAmountAdd": "Ajouter montant fixe", "fixedAmountSet": "Définir montant fixe", "markupValue": "Valeur de majoration", "priority": "Priorité", "active": "Actif", "inactive": "Inactif", "rulesApplied": "Les règles de tarification sont appliquées aux prix de base des produits avant conversion de devise.", "noRules": "Aucune règle de tarification trouvée", "previewRule": "Aperçu de la règle", "previewDescription": "Voir comment cette règle de tarification affecte les prix des produits", "noAffectedProducts": "Aucun produit n'est affecté par cette règle", "affectedProducts": "Produits affectés", "avgPriceChange": "Changement moyen", "maxIncrease": "Augmentation max", "maxDecrease": "Diminution max", "product": "Produit", "originalPrice": "Prix original", "adjustedPrice": "Prix ajusté", "priceDifference": "<PERSON>ff<PERSON><PERSON><PERSON>", "percentageChange": "Changement %", "previewNote": "Cet aperçu montre les 50 premiers produits affectés. Les prix sont calculés en CNY avant conversion de devise.", "previous": "Précédent", "next": "Suivant", "showingPartial": "Affichage partiel", "showingAll": "Affichage complet", "profile": "Profil", "create": "<PERSON><PERSON><PERSON>", "createOrder": "<PERSON><PERSON><PERSON> une commande", "addCustomer": "Ajouter un client", "manageCategoriesDescription": "Gérer et organiser vos catégories de produits", "expandAll": "<PERSON><PERSON> d<PERSON>vel<PERSON>per", "collapseAll": "<PERSON><PERSON> r<PERSON>", "addSubcategory": "Ajouter une sous-catégorie", "viewDetails": "Voir les détails", "totalCategories": "Total des catégories", "rootCategories": "Catégories racines", "totalProducts": "Total des produits", "addProduct": "Ajouter un produit"}}