{"checkout": {"title": "Paiement", "shippingAddress": "<PERSON><PERSON><PERSON>", "selectShippingAddress": "Sélectionnez votre adresse de livraison", "selectAddress": "<PERSON><PERSON><PERSON><PERSON>ner une adresse", "selectAddressError": "Veuillez sélectionner une adresse de livraison", "emptyCartError": "Votre panier est vide", "orderError": "Échec de la commande. Veuillez réessayer.", "noAddressesFound": "<PERSON><PERSON><PERSON> adresse trouvée", "addAddressInProfile": "Veuillez d'abord ajouter une adresse dans votre profil", "addAddressToContinue": "Veuillez ajouter une adresse pour continuer votre commande", "addNewAddress": "Ajouter une nouvelle adresse", "useThisAddress": "Utiliser cette adresse", "paymentMethod": "Mode de paiement", "orderSummary": "<PERSON><PERSON><PERSON><PERSON> de la commande", "placeOrder": "Passer la commande", "processing": "Traitement...", "placingOrder": "Commande en cours...", "loading": "Chargement...", "orderSuccess": "Commande passée avec succès!", "orderPlaced": "Commande passée avec succès!", "orderNumber": "<PERSON><PERSON><PERSON><PERSON> de commande", "thankYou": "Merci pour votre commande", "thankYouCustomer": "<PERSON><PERSON><PERSON> {name} pour votre commande", "orderPlacedSuccessfully": "Votre commande a été passée avec succès", "paymentInstructions": "Instructions de paiement", "paymentInstructionsText": "Veuillez transférer le montant total sur notre compte bancaire. Votre commande sera traitée une fois que nous aurons confirmé votre paiement.", "addressLine1": "Adresse ligne 1", "addressLine2": "Adresse ligne 2 (Optionnel)", "city": "Ville", "state": "État/Province", "postalCode": "Code postal", "country": "Pays", "saveAddress": "Enregistrer l'adresse", "setAsDefault": "Définir comme adresse par défaut", "items": "Articles", "orderItems": "Articles de commande", "itemsInYourOrder": "article(s) dans votre commande", "quantity": "Quantité", "total": "Total", "variant": "<PERSON><PERSON><PERSON>", "notSpecified": "Non spécifié", "variantNotSpecified": "Variante non spécifiée", "subtotal": "Sous-total", "shipping": "<PERSON><PERSON><PERSON>", "bankTransferDetails": "Détails du virement bancaire", "bankName": "Nom de la banque", "accountName": "Nom du compte", "accountNumber": "Numéro de compte", "swiftCode": "Code SWIFT", "reference": "Référence", "bankNameValue": "Banque Exemple", "accountNameValue": "MaoMao Trading Co.", "accountNumberValue": "**********", "swiftCodeValue": "EXAMPLSW", "paymentNote": "Veuillez inclure votre numéro de commande dans la référence de paiement pour un traitement plus rapide.", "whatNext": "Que se passe-t-il ensuite?", "step1Title": "Confirmation de paiement", "step1Description": "Nous vous enverrons les instructions de paiement par email", "step2Title": "Traitement de la commande", "step2Description": "Une fois le paiement confirmé, nous commencerons à traiter votre commande", "step3Title": "Expédition", "step3Description": "Votre commande sera expédiée et vous recevrez les informations de suivi", "viewOrder": "Voir les détails de la commande", "continueShopping": "Continuer les achats", "selectShippingMethod": "Sélectionner le mode de livraison", "shippingInformation": "Informations de livraison", "shippingMethod": "Mode de livraison", "shippingCost": "Frais de livraison", "shippingCostMessage": "Les frais de livraison seront calculés et mis à jour une fois que votre commande sera prête à être expédiée", "shippingBoat": "Transport maritime", "shippingPlane": "Transport aérien", "shippingBoatTime": "~2 mois", "shippingBoatDescription": "Option économique avec délai de livraison plus long", "shippingPlaneTime": "~2 semaines", "shippingPlaneDescription": "Livraison plus rapide avec coût plus élevé", "continueToConfirmation": "Continuer vers la confirmation de commande", "confirmOrder": "Confirmer votre commande", "reviewOrderDetails": "Veuillez vérifier les détails de votre commande avant de procéder au paiement", "confirmAndPlaceOrder": "Procéder au paiement", "back": "Retour", "streetAddress": "<PERSON><PERSON><PERSON>", "apartmentSuite": "Appartement, suite, etc.", "setAsDefaultAddress": "Définir comme adresse par défaut", "cancel": "Annuler", "saving": "Enregistrement...", "addAddress": "Ajouter une adresse", "deleteAddress": "Supp<PERSON>er l'adresse", "confirmDeleteAddress": "Êtes-vous sûr de vouloir supprimer cette adresse ?", "deleteAddressTitle": "Supp<PERSON>er l'adresse", "deleteAddressDescription": "Cette action ne peut pas être annulée. Cela supprimera définitivement l'adresse sélectionnée de votre compte.", "keepAddress": "Garder l'adresse", "yesDelete": "<PERSON><PERSON>, supprimer l'adresse", "shippingNote": "Les frais de livraison seront calculés et mis à jour une fois que votre commande sera prête à être expédiée", "orderNotFound": "Commande avec l'ID {orderId} introuvable.", "unauthorizedAccess": "Tentative d'accès non autorisée à la commande.", "failedFetchOrder": "Échec de la récupération et du traitement de la commande :", "selectPaymentMethod": "Veuillez sélectionner votre mode de paiement préféré.", "mobileMoney": "Argent mobile", "mobileMoneyDesc": "Payez instantanément avec MTN ou Orange.", "bankTransfer": "Virement bancaire", "bankTransferDesc": "Payez via SEPA ou virement global.", "cardPayment": "Paiement par carte", "cardPaymentDesc": "Visa, MasterCard, etc. (Bientôt disponible)", "cardPaymentComingSoon": "Le paiement par carte sera bientôt disponible.", "payWithMobileMoney": "Payer avec de l'argent mobile", "enterPhoneNumber": "Veuillez saisir le numéro de téléphone que vous souhaitez utiliser pour le paiement. Une invite sera envoyée à ce numéro.", "phoneNumber": "Numéro de téléphone", "payingAs": "Paiement en tant que :", "confirmPayment": "Confirmer le paiement", "checkYourPhone": "Vérifiez votre téléphone", "paymentPromptSent": "Nous avons envoyé une invite de paiement à votre téléphone mobile. Veuillez approuver la transaction pour finaliser votre commande.", "paymentMethodLabel": "Mode de paiement :", "mobileMoneyLabel": "Argent mobile", "instructions": "Instructions :", "instructions1": "Su<PERSON>z les instructions envoyées par SMS.", "instructions2": "Une fois que vous avez approuvé le paiement, cliquez sur le bouton ci-dessous.", "instructions3": "Si vous ne recevez pas d'invite, essayez de changer le numéro.", "confirming": "Confirmation...", "iHaveCompletedPayment": "J'ai terminé le paiement", "changePhoneNumber": "Changer le numéro de téléphone", "cancelPayment": "Annuler le paiement", "offlineBankTransfer": "Virement bancaire hors ligne", "bankTransferInstructions": "Veuillez transférer le montant total sur l'un des comptes ci-dessous. Votre commande sera traitée une fois le paiement confirmé.", "sepaForEU": "SEPA (pour l'UE)", "global": "Global", "beneficiary": "Bénéficiaire", "iban": "IBAN", "swiftBic": "SWIFT/BIC", "processingFee": "Frais de traitement :", "accountNo": "N° de compte", "bankCode": "Code bancaire", "branchCode": "Code de la succursale", "currencies": "Devi<PERSON>", "iHaveTransferredMoney": "J'ai transféré l'argent", "showOrderSummary": "<PERSON><PERSON><PERSON><PERSON> le résumé de la commande", "free": "<PERSON><PERSON><PERSON>", "taxes": "Taxes", "calculatedAtNextStep": "Calculé à l'étape suivante", "qty": "Qté :", "paymentFailed": "<PERSON><PERSON><PERSON>", "missingOrderInfo": "Informations de commande requises manquantes.", "reorderAll": "Tout commander à nouveau", "reorder": "Commander à nouveau"}}