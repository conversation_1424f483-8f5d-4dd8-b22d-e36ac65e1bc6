// i18n.ts
// Configuration for next-intl internationalization

import { getRequestConfig } from 'next-intl/server';
import { routing } from './i18n/routing';
import fs from 'fs';
import path from 'path';

export default getRequestConfig(async ({ requestLocale }) => {
  // This typically corresponds to the `[locale]` segment
  let locale = await requestLocale;

  // Ensure that a valid locale is used
  if (!locale || !(routing.locales as readonly string[]).includes(locale)) {
    locale = routing.defaultLocale;
  }

  // Load all JSON files from the locale directory and merge them
  const messagesDir = path.join(process.cwd(), 'messages', locale);
  const messages: Record<string, unknown> = {};

  if (fs.existsSync(messagesDir)) {
    const files = fs.readdirSync(messagesDir).filter(file => file.endsWith('.json'));
    for (const file of files) {
      const filePath = path.join(messagesDir, file);
      const fileContent = JSON.parse(fs.readFileSync(filePath, 'utf8'));
      Object.assign(messages, fileContent);
    }
  }

  return {
    locale,
    messages,
  };
});