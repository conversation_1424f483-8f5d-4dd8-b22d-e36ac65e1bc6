'use client';

// components/admin/AdminHeader.tsx
// Context-aware admin header with breadcrumbs and navigation

import { usePathname, useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import {
  ChevronRight,
  Bell,
  Search
} from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useState, useEffect } from 'react';
import { getNotificationCounts } from '@/lib/actions/admin/order.actions';

interface AdminHeaderProps {
  locale: string;
  userName: string;
}

interface BreadcrumbItem {
  label: string;
  href?: string;
}

export function AdminHeader({ locale }: AdminHeaderProps) {
  const t = useTranslations('admin');
  const pathname = usePathname();
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [notificationCount, setNotificationCount] = useState(0);

  // Generate breadcrumbs based on current path
  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const pathSegments = pathname.split('/').filter(Boolean);
    const breadcrumbs: BreadcrumbItem[] = [];

    // Remove locale from segments
    const segments = pathSegments.slice(1);

    if (segments.length === 0) {
      return [{ label: t('dashboard') }];
    }

    // Build breadcrumb path
    let currentPath = `/${locale}`;
    breadcrumbs.push({ label: t('dashboard'), href: `/${locale}/admin` });

    for (let i = 0; i < segments.length; i++) {
      const segment = segments[i];
      currentPath += `/${segment}`;

      let label = segment;
      const href = currentPath;

      // Customize labels based on segment
      switch (segment) {
        case 'admin':
          continue; // Skip admin segment
        case 'orders':
          label = t('orders');
          break;
        case 'customers':
          label = t('customers');
          break;
        case 'products':
          label = t('products');
          break;
        case 'warehouse':
          label = t('warehouse');
          break;
        case 'payments':
          label = t('payments');
          break;
        case 'pricing':
          label = t('pricing');
          break;
        default:
          // Check if it's an ID (like orderId, customerId, etc.)
          if (/^[a-f0-9]{8,}$/.test(segment)) {
            // Extract parent segment for context
            const parentSegment = segments[i - 1];
            switch (parentSegment) {
              case 'orders':
                label = `${t('order')} #${segment.slice(0, 8)}`;
                break;
              case 'customers':
                label = `${t('customer')} #${segment.slice(0, 8)}`;
                break;
              case 'products':
                label = `${t('product')} #${segment.slice(0, 8)}`;
                break;
              default:
                label = segment.slice(0, 8);
            }
          } else if (segment === 'new') {
            label = t('create');
          } else if (segment === 'payment') {
            label = t('recordPayment');
          }
          break;
      }

      breadcrumbs.push({ label, href: href === pathname ? undefined : href });
    }

    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs();

  // Quick search handler
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // Navigate to appropriate search page based on context
      if (pathname.includes('/orders')) {
        router.push(`/${locale}/admin/orders?search=${encodeURIComponent(searchQuery)}`);
      } else if (pathname.includes('/customers')) {
        router.push(`/${locale}/admin/customers?search=${encodeURIComponent(searchQuery)}`);
      } else if (pathname.includes('/products')) {
        router.push(`/${locale}/admin/products?search=${encodeURIComponent(searchQuery)}`);
      } else {
        router.push(`/${locale}/admin/orders?search=${encodeURIComponent(searchQuery)}`);
      }
      setSearchQuery('');
    }
  };


  // Fetch notification count on mount
  useEffect(() => {
    const fetchNotificationCount = async () => {
      try {
        const counts = await getNotificationCounts();
        if (!('error' in counts)) {
          setNotificationCount(counts.total);
        }
      } catch (error) {
        console.error('Error fetching notification count:', error);
      }
    };

    fetchNotificationCount();
  }, []);

  return (
    <header className="bg-background border-b shadow-sm">
      {/* Top bar with user info and actions */}
      <div className="flex items-center justify-between px-6 py-3 border-b">
        <div className="flex items-center space-x-4">
          {/* Quick Search */}
          <form onSubmit={handleSearch} className="hidden md:flex">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder={t('search')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 w-64"
              />
            </div>
          </form>
        </div>

        <div className="flex items-center space-x-3">
          {/* Notifications */}
          <Button variant="ghost" size="icon" className="relative">
            <Bell className="h-5 w-5" />
            {notificationCount > 0 && (
              <Badge
                variant="destructive"
                className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs font-medium min-w-[20px]"
              >
                {notificationCount > 99 ? '99+' : notificationCount}
              </Badge>
            )}
          </Button>
        </div>
      </div>

      {/* Breadcrumbs and Context Bar */}
      <div className="flex items-center justify-between px-6 py-3 bg-muted/30">
        {/* Breadcrumbs */}
        <nav className="flex items-center space-x-2 text-sm">
          {breadcrumbs.map((crumb, index) => (
            <div key={index} className="flex items-center">
              {index > 0 && <ChevronRight className="h-4 w-4 text-muted-foreground mx-2" />}
              {crumb.href ? (
                <Link
                  href={crumb.href}
                  className="text-muted-foreground hover:text-foreground transition-colors"
                >
                  {crumb.label}
                </Link>
              ) : (
                <span className="font-medium text-foreground">{crumb.label}</span>
              )}
            </div>
          ))}
        </nav>

        {/* Context Actions - can be customized based on current page */}
        <div className="flex items-center space-x-2">
          {/* Page-specific actions can be added here */}
          {pathname.includes('/orders/') && !pathname.includes('/payment') && (
            <Button size="sm" variant="outline" asChild>
              <Link href={`${pathname}/payment`}>
                {t('recordPayment')}
              </Link>
            </Button>
          )}
        </div>
      </div>
    </header>
  );
}

