'use client';

// components/admin/PricingRulePreview.tsx
// Component for displaying pricing rule preview with affected products

import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowUpIcon, ArrowDownIcon, MinusIcon, ChevronLeftIcon, ChevronRightIcon } from 'lucide-react';

interface AffectedProduct {
  id: number;
  product_url: string;
  marketplace: string;
  original_name: string | null;
  min_price_cny: number | null;
  categoryIds: number[];
  originalPrice: number;
  adjustedPrice: number;
  priceDifference: number;
  percentageChange: number;
  created: Date;
}

interface PaginationData {
  hasMore: boolean;
  nextCursor?: string;
  prevCursor?: string;
}

interface PricingRulePreviewProps {
  affectedProducts: AffectedProduct[];
  pagination: PaginationData;
  ruleId: string;
  locale: string;
}

export function PricingRulePreview({ affectedProducts, pagination, ruleId, locale }: PricingRulePreviewProps) {
  // const tAdmin = useTranslations('admin');
  const tPricing = useTranslations('pricing');
  const router = useRouter();

  if (affectedProducts.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <p className="text-muted-foreground">{tPricing('noAffectedProducts')}</p>
        </CardContent>
      </Card>
    );
  }

  // Calculate summary statistics
  const totalProducts = affectedProducts.length;
  const avgPercentageChange = affectedProducts.reduce((sum, p) => sum + p.percentageChange, 0) / totalProducts;
  const maxIncrease = Math.max(...affectedProducts.map(p => p.percentageChange));
  const maxDecrease = Math.min(...affectedProducts.map(p => p.percentageChange));

  return (
    <div className="space-y-6">
      {/* Summary Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold">{totalProducts}</div>
            <p className="text-sm text-muted-foreground">{tPricing('affectedProducts')}</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold">{avgPercentageChange.toFixed(1)}%</div>
            <p className="text-sm text-muted-foreground">{tPricing('avgPriceChange')}</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">+{maxIncrease.toFixed(1)}%</div>
            <p className="text-sm text-muted-foreground">{tPricing('maxIncrease')}</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">{maxDecrease.toFixed(1)}%</div>
            <p className="text-sm text-muted-foreground">{tPricing('maxDecrease')}</p>
          </CardContent>
        </Card>
      </div>

      {/* Products Table */}
      <Card>
        <CardHeader>
          <CardTitle>{tPricing('affectedProducts')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-muted">
                <tr>
                  <th className="text-left p-3 font-medium">{tPricing('product')}</th>
                  <th className="text-left p-3 font-medium">{tPricing('marketplace')}</th>
                  <th className="text-right p-3 font-medium">{tPricing('originalPrice')}</th>
                  <th className="text-right p-3 font-medium">{tPricing('adjustedPrice')}</th>
                  <th className="text-right p-3 font-medium">{tPricing('priceDifference')}</th>
                  <th className="text-right p-3 font-medium">{tPricing('percentageChange')}</th>
                </tr>
              </thead>
              <tbody>
                {affectedProducts.map((product) => (
                  <tr key={product.id} className="border-t hover:bg-accent/50">
                    <td className="p-3">
                      <div className="max-w-xs">
                        <p className="font-medium truncate" title={product.original_name || product.product_url}>
                          {product.original_name || product.product_url.split('/').pop() || 'Unknown Product'}
                        </p>
                        <p className="text-xs text-muted-foreground truncate" title={product.product_url}>
                          {product.product_url}
                        </p>
                      </div>
                    </td>
                    <td className="p-3">
                      <Badge variant="outline">{product.marketplace}</Badge>
                    </td>
                    <td className="p-3 text-right font-mono">
                      ¥{product.originalPrice.toFixed(2)}
                    </td>
                    <td className="p-3 text-right font-mono">
                      ¥{product.adjustedPrice.toFixed(2)}
                    </td>
                    <td className="p-3 text-right">
                      <div className="flex items-center justify-end gap-1">
                        {product.priceDifference > 0 ? (
                          <ArrowUpIcon className="h-4 w-4 text-green-600" />
                        ) : product.priceDifference < 0 ? (
                          <ArrowDownIcon className="h-4 w-4 text-red-600" />
                        ) : (
                          <MinusIcon className="h-4 w-4 text-gray-600" />
                        )}
                        <span className={`font-mono ${
                          product.priceDifference > 0 ? 'text-green-600' :
                          product.priceDifference < 0 ? 'text-red-600' : 'text-gray-600'
                        }`}>
                          {product.priceDifference > 0 ? '+' : ''}¥{Math.abs(product.priceDifference).toFixed(2)}
                        </span>
                      </div>
                    </td>
                    <td className="p-3 text-right">
                      <Badge
                        variant={
                          product.percentageChange > 0 ? 'default' :
                          product.percentageChange < 0 ? 'destructive' : 'secondary'
                        }
                        className={
                          product.percentageChange > 0 ? 'bg-green-100 text-green-800 hover:bg-green-100' :
                          product.percentageChange < 0 ? 'bg-red-100 text-red-800 hover:bg-red-100' : ''
                        }
                      >
                        {product.percentageChange > 0 ? '+' : ''}{product.percentageChange.toFixed(1)}%
                      </Badge>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Pagination Controls */}
      {(pagination.prevCursor || pagination.hasMore) && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex justify-between items-center">
              <Button
                variant="outline"
                disabled={!pagination.prevCursor}
                onClick={() => {
                  if (pagination.prevCursor) {
                    router.push(`/${locale}/admin/pricing/${ruleId}/preview?cursor=${encodeURIComponent(pagination.prevCursor)}`);
                  }
                }}
              >
                <ChevronLeftIcon className="h-4 w-4 mr-2" />
                {tPricing('previous')}
              </Button>

              <span className="text-sm text-muted-foreground">
                {pagination.hasMore ? tPricing('showingPartial') : tPricing('showingAll')}
              </span>

              <Button
                variant="outline"
                disabled={!pagination.hasMore}
                onClick={() => {
                  if (pagination.nextCursor) {
                    router.push(`/${locale}/admin/pricing/${ruleId}/preview?cursor=${encodeURIComponent(pagination.nextCursor)}`);
                  }
                }}
              >
                {tPricing('next')}
                <ChevronRightIcon className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Note */}
      <Card>
        <CardContent className="pt-6">
          <p className="text-sm text-muted-foreground">
            {tPricing('previewNote')}
          </p>
        </CardContent>
      </Card>
    </div>
  );
}