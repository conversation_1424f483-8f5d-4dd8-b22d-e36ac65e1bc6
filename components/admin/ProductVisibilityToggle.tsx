'use client';

// components/admin/ProductVisibilityToggle.tsx
// Toggle product visibility with confirmation dialog

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Eye, EyeOff } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { updateProductVisibility } from '@/lib/actions/admin/product.actions';

interface ProductVisibilityToggleProps {
  productId: number;
  currentVisibility: boolean;
  locale: string;
}

export function ProductVisibilityToggle({
  productId,
  currentVisibility,
  locale
}: ProductVisibilityToggleProps) {
  const t = useTranslations('admin');
  const router = useRouter();
  const [isVisible, setIsVisible] = useState(currentVisibility);
  const [loading, setLoading] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);

  const handleToggle = async () => {
    setLoading(true);
    setDialogOpen(false);

    // Optimistic update
    const newVisibility = !isVisible;
    setIsVisible(newVisibility);

    try {
      const result = await updateProductVisibility(productId, newVisibility);

      if (!result.success) {
        // Revert on error
        setIsVisible(isVisible);
        console.error('Failed to update visibility:', result.error);
      } else {
        // Refresh the page to show updated data
        router.refresh();
      }
    } catch (error) {
      // Revert on error
      setIsVisible(isVisible);
      console.error('Error updating visibility:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <AlertDialog open={dialogOpen} onOpenChange={setDialogOpen}>
      <AlertDialogTrigger asChild>
        <Button
          variant={isVisible ? 'default' : 'outline'}
          size="sm"
          disabled={loading}
        >
          {isVisible ? (
            <>
              <Eye className="h-4 w-4 mr-2" />
              {t('visible')}
            </>
          ) : (
            <>
              <EyeOff className="h-4 w-4 mr-2" />
              {t('hidden')}
            </>
          )}
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{t('confirmToggleVisibility')}</AlertDialogTitle>
          <AlertDialogDescription>
            {isVisible
              ? t('confirmToggleVisibilityToHidden')
              : t('confirmToggleVisibilityToVisible')
            }
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>{t('cancel')}</AlertDialogCancel>
          <AlertDialogAction onClick={handleToggle} disabled={loading}>
            {loading ? t('processing') : t('confirm')}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
