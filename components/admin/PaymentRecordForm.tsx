'use client';

// components/admin/PaymentRecordForm.tsx
// Form for recording payments for orders

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { recordPayment } from '@/lib/actions/admin/payment.actions';
import { formatCurrency } from '@/lib/utils';

interface PaymentRecordFormProps {
  orderId: string;
  orderTotal: number;
  orderCurrency: string;
  locale: string;
}

const PAYMENT_METHODS = [
  'bank_transfer',
  'credit_card',
  'paypal',
  'cash',
  'other'
];

export function PaymentRecordForm({
  orderId,
  orderTotal,
  orderCurrency,
  locale
}: PaymentRecordFormProps) {
  const t = useTranslations('admin');
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [formData, setFormData] = useState({
    transaction_id: '',
    payment_method: '',
    amount: orderTotal.toString(),
    currency: orderCurrency,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    // Validation
    if (!formData.transaction_id.trim()) {
      setError(t('transactionIdRequired'));
      setLoading(false);
      return;
    }

    if (!formData.payment_method) {
      setError(t('paymentMethodRequired'));
      setLoading(false);
      return;
    }

    const amount = parseFloat(formData.amount);
    if (isNaN(amount) || amount <= 0) {
      setError(t('amountRequired'));
      setLoading(false);
      return;
    }

    try {
      const result = await recordPayment({
        order_id: orderId,
        amount,
        currency: formData.currency,
        payment_method: formData.payment_method,
        transaction_id: formData.transaction_id.trim(),
      });

      if (result.success) {
        // Redirect to payment details or order details
        router.push(`/${locale}/admin/payments/${result.paymentId}`);
      } else {
        setError(result.error || t('recordPaymentError'));
      }
    } catch (err) {
      console.error('Error recording payment:', err);
      setError(t('recordPaymentError'));
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('recordPayment')}</CardTitle>
        <p className="text-sm text-muted-foreground">
          {t('recordPaymentDescription')}
        </p>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <div className="p-3 text-sm text-destructive bg-destructive/10 rounded-md">
              {error}
            </div>
          )}

          {/* Order Information Summary */}
          <div className="bg-muted p-4 rounded-lg">
            <h3 className="font-medium mb-2">{t('orderSummary')}</h3>
            <div className="text-sm">
              <p><strong>{t('orderId')}:</strong> #{orderId.slice(0, 8)}</p>
              <p><strong>{t('total')}:</strong> {formatCurrency(orderTotal, orderCurrency)}</p>
            </div>
          </div>

          {/* Transaction ID */}
          <div className="space-y-2">
            <Label htmlFor="transaction_id">
              {t('transactionId')} *
            </Label>
            <Input
              id="transaction_id"
              value={formData.transaction_id}
              onChange={(e) => handleInputChange('transaction_id', e.target.value)}
              placeholder={t('transactionIdPlaceholder')}
              required
            />
            <p className="text-xs text-muted-foreground">
              {t('transactionIdHelp')}
            </p>
          </div>

          {/* Payment Method */}
          <div className="space-y-2">
            <Label htmlFor="payment_method">
              {t('paymentMethod')} *
            </Label>
            <Select
              value={formData.payment_method}
              onValueChange={(value) => handleInputChange('payment_method', value)}
            >
              <SelectTrigger id="payment_method">
                <SelectValue placeholder={t('selectPaymentMethod')} />
              </SelectTrigger>
              <SelectContent>
                {PAYMENT_METHODS.map((method) => (
                  <SelectItem key={method} value={method}>
                    {t(`paymentMethod${method.charAt(0).toUpperCase() + method.slice(1)}`)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Amount */}
          <div className="space-y-2">
            <Label htmlFor="amount">
              {t('amount')} *
            </Label>
            <Input
              id="amount"
              type="number"
              step="0.01"
              min="0"
              value={formData.amount}
              onChange={(e) => handleInputChange('amount', e.target.value)}
              placeholder="0.00"
              required
            />
            <p className="text-xs text-muted-foreground">
              {t('amountHelp')} ({orderCurrency})
            </p>
          </div>

          {/* Actions */}
          <div className="flex gap-4 pt-4">
            <Link href={`/${locale}/admin/orders/${orderId}`}>
              <Button type="button" variant="outline">
                {t('cancel')}
              </Button>
            </Link>
            <Button type="submit" disabled={loading}>
              {loading ? t('recording') : t('recordPayment')}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}