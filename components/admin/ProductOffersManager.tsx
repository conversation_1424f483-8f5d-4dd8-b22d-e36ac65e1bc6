'use client';

// components/admin/ProductOffersManager.tsx
// Manage product pricing offers

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Plus, Edit, Trash2, Save, X } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { formatCurrency } from '@/lib/utils';
import { deleteProductOffer, updateProductOffer } from '@/lib/actions/admin/product.actions';
import type { SerializedProductOffer } from '@/lib/types/admin';

interface ProductOffersManagerProps {
  productId: number;
  offers: SerializedProductOffer[];
  locale: string;
}

interface EditingOffer {
  id: number;
  min_quantity: string;
  price_low: string;
  price_high: string;
  currency: string;
  quantity_info: string;
}

const CURRENCIES = [
  { value: 'CNY', label: 'Chinese Yuan (CNY)' },
  { value: 'USD', label: 'US Dollar (USD)' },
  { value: 'EUR', label: 'Euro (EUR)' },
];

export function ProductOffersManager({
  productId,
  offers,
  locale
}: ProductOffersManagerProps) {
  const t = useTranslations('admin');
  const router = useRouter();
  const [loading, setLoading] = useState<string | null>(null);
  const [editingOffer, setEditingOffer] = useState<EditingOffer | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newOffer, setNewOffer] = useState({
    min_quantity: '1',
    price_low: '',
    price_high: '',
    currency: 'CNY',
    quantity_info: '',
  });

  const handleDeleteOffer = async (offerId: number) => {
    if (!confirm(t('confirmDeleteOffer'))) return;

    setLoading(offerId.toString());
    
    try {
      const result = await deleteProductOffer(Number(offerId));
      
      if (result.success) {
        router.refresh();
      } else {
        console.error('Failed to delete offer:', result.error);
      }
    } catch (error) {
      console.error('Error deleting offer:', error);
    } finally {
      setLoading(null);
    }
  };

  const handleEditOffer = (offer: SerializedProductOffer) => {
    setEditingOffer({
      id: offer.id,
      min_quantity: offer.min_quantity.toString(),
      price_low: offer.price_low.toString(),
      price_high: offer.price_high?.toString() || '',
      currency: offer.currency,
      quantity_info: offer.quantity_info || '',
    });
  };

  const handleSaveOffer = async () => {
    if (!editingOffer) return;

    setLoading(editingOffer.id.toString());
    
    try {
      const result = await updateProductOffer(Number(editingOffer.id), {
        min_quantity: parseFloat(editingOffer.min_quantity),
        price_low: parseFloat(editingOffer.price_low),
        price_high: editingOffer.price_high ? parseFloat(editingOffer.price_high) : null,
        currency: editingOffer.currency,
        quantity_info: editingOffer.quantity_info || null,
      });
      
      if (result.success) {
        setEditingOffer(null);
        router.refresh();
      } else {
        console.error('Failed to update offer:', result.error);
      }
    } catch (error) {
      console.error('Error updating offer:', error);
    } finally {
      setLoading(null);
    }
  };

  const handleAddOffer = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading('add');

    try {
      const result = await fetch('/api/admin/products/offers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          productId,
          min_quantity: parseFloat(newOffer.min_quantity),
          price_low: parseFloat(newOffer.price_low),
          price_high: newOffer.price_high ? parseFloat(newOffer.price_high) : null,
          currency: newOffer.currency,
          quantity_info: newOffer.quantity_info || null,
        }),
      });

      if (result.ok) {
        setShowAddForm(false);
        setNewOffer({
          min_quantity: '1',
          price_low: '',
          price_high: '',
          currency: 'CNY',
          quantity_info: '',
        });
        router.refresh();
      } else {
        console.error('Failed to add offer');
      }
    } catch (error) {
      console.error('Error adding offer:', error);
    } finally {
      setLoading(null);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>{t('pricingOffers')}</CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAddForm(!showAddForm)}
          >
            <Plus className="h-4 w-4 mr-2" />
            {t('addOffer')}
          </Button>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Add Form */}
          {showAddForm && (
            <form onSubmit={handleAddOffer} className="border rounded-lg p-4 space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="min_quantity">{t('minimumQuantity')}</Label>
                  <Input
                    id="min_quantity"
                    type="number"
                    min="1"
                    value={newOffer.min_quantity}
                    onChange={(e) => setNewOffer({ ...newOffer, min_quantity: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="currency">{t('currency')}</Label>
                  <Select
                    value={newOffer.currency}
                    onValueChange={(value) => setNewOffer({ ...newOffer, currency: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {CURRENCIES.map((currency) => (
                        <SelectItem key={currency.value} value={currency.value}>
                          {currency.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="price_low">{t('priceFrom')}</Label>
                  <Input
                    id="price_low"
                    type="number"
                    step="0.01"
                    min="0"
                    value={newOffer.price_low}
                    onChange={(e) => setNewOffer({ ...newOffer, price_low: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="price_high">{t('priceTo')}</Label>
                  <Input
                    id="price_high"
                    type="number"
                    step="0.01"
                    min="0"
                    value={newOffer.price_high}
                    onChange={(e) => setNewOffer({ ...newOffer, price_high: e.target.value })}
                    placeholder={t('optional')}
                  />
                </div>
                <div className="col-span-2 space-y-2">
                  <Label htmlFor="quantity_info">{t('quantityInfo')}</Label>
                  <Input
                    id="quantity_info"
                    value={newOffer.quantity_info}
                    onChange={(e) => setNewOffer({ ...newOffer, quantity_info: e.target.value })}
                    placeholder={t('quantityInfoPlaceholder')}
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Button type="submit" disabled={loading === 'add'}>
                  <Save className="h-4 w-4 mr-2" />
                  {loading === 'add' ? t('adding') : t('addOffer')}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowAddForm(false)}
                >
                  {t('cancel')}
                </Button>
              </div>
            </form>
          )}

          {/* Offers List */}
          {offers.length === 0 ? (
            <p className="text-muted-foreground text-center py-8">{t('noOffers')}</p>
          ) : (
            <div className="space-y-4">
              {offers.map((offer) => {
                const isEditing = editingOffer?.id === offer.id;

                return (
                  <div key={offer.id.toString()} className="border rounded-lg p-4">
                    {isEditing ? (
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label>{t('minimumQuantity')}</Label>
                            <Input
                              type="number"
                              min="1"
                              value={editingOffer.min_quantity}
                              onChange={(e) => setEditingOffer({ ...editingOffer, min_quantity: e.target.value })}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label>{t('currency')}</Label>
                            <Select
                              value={editingOffer.currency}
                              onValueChange={(value) => setEditingOffer({ ...editingOffer, currency: value })}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {CURRENCIES.map((currency) => (
                                  <SelectItem key={currency.value} value={currency.value}>
                                    {currency.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label>{t('priceFrom')}</Label>
                            <Input
                              type="number"
                              step="0.01"
                              min="0"
                              value={editingOffer.price_low}
                              onChange={(e) => setEditingOffer({ ...editingOffer, price_low: e.target.value })}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label>{t('priceTo')}</Label>
                            <Input
                              type="number"
                              step="0.01"
                              min="0"
                              value={editingOffer.price_high}
                              onChange={(e) => setEditingOffer({ ...editingOffer, price_high: e.target.value })}
                            />
                          </div>
                          <div className="col-span-2 space-y-2">
                            <Label>{t('quantityInfo')}</Label>
                            <Input
                              value={editingOffer.quantity_info}
                              onChange={(e) => setEditingOffer({ ...editingOffer, quantity_info: e.target.value })}
                            />
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            onClick={handleSaveOffer}
                            disabled={loading === offer.id.toString()}
                          >
                            <Save className="h-4 w-4 mr-2" />
                            {loading === offer.id.toString() ? t('saving') : t('save')}
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setEditingOffer(null)}
                          >
                            <X className="h-4 w-4 mr-2" />
                            {t('cancel')}
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center justify-between">
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 flex-1">
                          <div>
                            <p className="text-sm text-muted-foreground">{t('minimumQuantity')}</p>
                            <p className="font-medium">{Number(offer.min_quantity)}</p>
                          </div>
                          <div>
                            <p className="text-sm text-muted-foreground">{t('price')}</p>
                            <p className="font-medium">
                              {formatCurrency(Number(offer.price_low), offer.currency)}
                              {offer.price_high && Number(offer.price_high) !== Number(offer.price_low) && (
                                <span> - {formatCurrency(Number(offer.price_high), offer.currency)}</span>
                              )}
                            </p>
                          </div>
                          <div>
                            <p className="text-sm text-muted-foreground">{t('currency')}</p>
                            <Badge variant="outline">{offer.currency}</Badge>
                          </div>
                          <div>
                            <p className="text-sm text-muted-foreground">{t('quantityInfo')}</p>
                            <p className="text-sm">{offer.quantity_info || t('notSpecified')}</p>
                          </div>
                        </div>
                        <div className="flex gap-2 ml-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditOffer(offer)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => handleDeleteOffer(offer.id)}
                            disabled={loading === offer.id.toString()}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
