'use client';

// components/admin/PaymentsTable.tsx
// Payments table with filters and pagination

import { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { formatCurrency, formatDate } from '@/lib/utils';
import type { PaymentListItem } from '@/lib/types/admin';

interface PaymentsTableProps {
  payments: PaymentListItem[];
  total: number;
  page: number;
  totalPages: number;
  locale: string;
}

const STATUS_VARIANTS = {
  pending: 'secondary' as const,
  succeeded: 'default' as const,
  failed: 'destructive' as const,
  refunded: 'outline' as const,
};

export function PaymentsTable({
  payments,
  total,
  page,
  totalPages,
  locale,
}: PaymentsTableProps) {
  const t = useTranslations('admin');
  const router = useRouter();
  const searchParams = useSearchParams();
  const [search, setSearch] = useState(searchParams.get('orderId') || '');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const params = new URLSearchParams(searchParams.toString());
    if (search) {
      params.set('orderId', search);
    } else {
      params.delete('orderId');
    }
    params.delete('page');
    router.push(`/${locale}/admin/payments?${params.toString()}`);
  };

  const handleStatusFilter = (status: string) => {
    const params = new URLSearchParams(searchParams.toString());
    if (status && status !== 'all') {
      params.set('status', status);
    } else {
      params.delete('status');
    }
    params.delete('page');
    router.push(`/${locale}/admin/payments?${params.toString()}`);
  };

  const handlePageChange = (newPage: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('page', newPage.toString());
    router.push(`/${locale}/admin/payments?${params.toString()}`);
  };

  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="flex gap-4">
        <form onSubmit={handleSearch} className="flex-1 flex gap-2">
          <Input
            placeholder={t('searchOrderId')}
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="max-w-sm"
          />
          <Button type="submit" variant="secondary">
            <Search className="h-4 w-4 mr-2" />
            {t('search')}
          </Button>
        </form>

        <Select
          value={searchParams.get('status') || 'all'}
          onValueChange={handleStatusFilter}
        >
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder={t('filterByStatus')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t('allStatuses')}</SelectItem>
            <SelectItem value="pending">{t('statusPending')}</SelectItem>
            <SelectItem value="succeeded">{t('statusSucceeded')}</SelectItem>
            <SelectItem value="failed">{t('statusFailed')}</SelectItem>
            <SelectItem value="refunded">{t('statusRefunded')}</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Table */}
      <div className="border rounded-lg">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-muted">
              <tr>
                <th className="px-4 py-3 text-left text-sm font-medium">
                  {t('transactionId')}
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium">
                  {t('customer')}
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium">
                  {t('amount')}
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium">
                  {t('method')}
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium">
                  {t('date')}
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium">
                  {t('status')}
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium">
                  {t('actions')}
                </th>
              </tr>
            </thead>
            <tbody className="divide-y">
              {payments.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-4 py-8 text-center text-muted-foreground">
                    {t('noPayments')}
                  </td>
                </tr>
              ) : (
                payments.map((payment) => {
                  const statusVariant = STATUS_VARIANTS[payment.status];
                  const statusKey = `status${payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}` as 'statusPending' | 'statusSucceeded' | 'statusFailed' | 'statusRefunded';
                  const statusLabel = t(statusKey);

                  return (
                    <tr key={payment.id} className="hover:bg-muted/50">
                      <td className="px-4 py-3 text-sm font-mono">
                        {payment.transaction_id.slice(0, 16)}...
                      </td>
                      <td className="px-4 py-3 text-sm">
                        <div>
                          <p className="font-medium">
                            {payment.order.customer.full_name}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {payment.order.customer.email}
                          </p>
                        </div>
                      </td>
                      <td className="px-4 py-3 text-sm font-bold">
                        {formatCurrency(Number(payment.amount), payment.currency)}
                      </td>
                      <td className="px-4 py-3 text-sm">
                        {payment.payment_method}
                      </td>
                      <td className="px-4 py-3 text-sm">
                        {formatDate(payment.created)}
                      </td>
                      <td className="px-4 py-3">
                        <Badge variant={statusVariant}>
                          {statusLabel}
                        </Badge>
                      </td>
                      <td className="px-4 py-3">
                        <Link href={`/${locale}/admin/payments/${payment.id}`}>
                          <Button variant="ghost" size="sm">
                            {t('view')}
                          </Button>
                        </Link>
                      </td>
                    </tr>
                  );
                })
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            {t('showing')} {(page - 1) * 20 + 1} - {Math.min(page * 20, total)} {t('of')} {total}
          </p>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(page - 1)}
              disabled={page === 1}
            >
              {t('previous')}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(page + 1)}
              disabled={page === totalPages}
            >
              {t('next')}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}

