'use client';

// components/admin/ProductAttributesManager.tsx
// Manage product attributes with translations

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Plus, Edit, Trash2, Save, X } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';

interface ProductAttributesManagerProps {
  productId: number;
  attributes: ProductAttribute[];
  locale: string;
}

interface ProductAttribute {
  id: number;
  product_id: number;
  original_attr_key: string;
  original_attr_value: string;
  translations: Array<{
    id: number;
    attributes_id: number;
    language_code: string;
    attr_key: string;
    attr_value: string;
  }>;
}

interface AttributeUpdateData {
  original_attr_key?: string;
  original_attr_value?: string;
  translations?: Array<{
    language_code: string;
    attr_key?: string;
    attr_value?: string;
  }>;
}

interface ApiTranslation {
  id: number;
  attributes_id: number;
  language_code: string;
  attr_key: string;
  attr_value: string;
}

export function ProductAttributesManager({
  productId,
  attributes: initialAttributes,
  locale
}: ProductAttributesManagerProps) {
  const t = useTranslations('admin');
  const [loading, setLoading] = useState<string | null>(null);
  const [attributes, setAttributes] = useState<ProductAttribute[]>(initialAttributes);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingAttribute, setEditingAttribute] = useState<string | null>(null);
  const [newAttribute, setNewAttribute] = useState({
    original_attr_key: '',
    original_attr_value: '',
  });

  const handleAddAttribute = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading('add');

    try {
      const response = await fetch(`/api/admin/products/${productId}/attributes`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newAttribute),
      });

      if (response.ok) {
        const newAttr = await response.json();
        // Serialize the new attribute to match our interface
        const serializedAttr: ProductAttribute = {
          ...newAttr,
          id: Number(newAttr.id),
          translations: newAttr.translations.map((t: ApiTranslation) => ({
            ...t,
            id: Number(t.id),
            attributes_id: Number(t.attributes_id),
          })),
        };
        setAttributes([...attributes, serializedAttr]);
        setShowAddForm(false);
        setNewAttribute({ original_attr_key: '', original_attr_value: '' });
      } else {
        console.error('Failed to add attribute');
      }
    } catch (error) {
      console.error('Error adding attribute:', error);
    } finally {
      setLoading(null);
    }
  };

  const handleDeleteAttribute = async (attributeId: number) => {
    if (!confirm(t('confirmDeleteAttribute'))) return;

    setLoading(attributeId.toString());

    try {
      const response = await fetch(`/api/admin/products/attributes/${attributeId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setAttributes(attributes.filter(attr => attr.id !== attributeId));
      } else {
        console.error('Failed to delete attribute');
      }
    } catch (error) {
      console.error('Error deleting attribute:', error);
    } finally {
      setLoading(null);
    }
  };

  const handleUpdateAttribute = async (attributeId: number, data: AttributeUpdateData) => {
    setLoading(attributeId.toString());

    try {
      const response = await fetch(`/api/admin/products/attributes/${attributeId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        const updatedAttr = await response.json();
        // Serialize the updated attribute
        const serializedAttr: ProductAttribute = {
          ...updatedAttr,
          id: Number(updatedAttr.id),
          translations: updatedAttr.translations.map((t: ApiTranslation) => ({
            ...t,
            id: Number(t.id),
            attributes_id: Number(t.attributes_id),
          })),
        };
        setAttributes(attributes.map(attr =>
          attr.id === attributeId ? serializedAttr : attr
        ));
        setEditingAttribute(null);
      } else {
        console.error('Failed to update attribute');
      }
    } catch (error) {
      console.error('Error updating attribute:', error);
    } finally {
      setLoading(null);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>{t('productAttributes')}</CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAddForm(!showAddForm)}
          >
            <Plus className="h-4 w-4 mr-2" />
            {t('addAttribute')}
          </Button>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Add Form */}
          {showAddForm && (
            <form onSubmit={handleAddAttribute} className="border rounded-lg p-4 space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="attr_key">{t('attributeKey')}</Label>
                  <Input
                    id="attr_key"
                    value={newAttribute.original_attr_key}
                    onChange={(e) => setNewAttribute({ ...newAttribute, original_attr_key: e.target.value })}
                    placeholder={t('attributeKeyPlaceholder')}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="attr_value">{t('attributeValue')}</Label>
                  <Input
                    id="attr_value"
                    value={newAttribute.original_attr_value}
                    onChange={(e) => setNewAttribute({ ...newAttribute, original_attr_value: e.target.value })}
                    placeholder={t('attributeValuePlaceholder')}
                    required
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Button type="submit" disabled={loading === 'add'}>
                  <Save className="h-4 w-4 mr-2" />
                  {loading === 'add' ? t('adding') : t('addAttribute')}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowAddForm(false)}
                >
                  {t('cancel')}
                </Button>
              </div>
            </form>
          )}

          {/* Attributes List */}
          {attributes.length === 0 ? (
            <p className="text-muted-foreground text-center py-8">{t('noAttributes')}</p>
          ) : (
            <div className="space-y-4">
              {attributes.map((attribute) => {
                const translation = attribute.translations.find(
                  t => t.language_code === locale
                ) || attribute.translations.find(
                  t => t.language_code === 'en'
                ) || attribute.translations[0];

                const displayKey = translation?.attr_key || attribute.original_attr_key;
                const displayValue = translation?.attr_value || attribute.original_attr_value;

                return (
                  <div key={attribute.id.toString()} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1 grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm text-muted-foreground">{t('attributeKey')}</p>
                          <p className="font-medium">{displayKey}</p>
                          {translation && translation.attr_key !== attribute.original_attr_key && (
                            <p className="text-xs text-muted-foreground">
                              {t('original')}: {attribute.original_attr_key}
                            </p>
                          )}
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">{t('attributeValue')}</p>
                          <p className="font-medium">{displayValue}</p>
                          {translation && translation.attr_value !== attribute.original_attr_value && (
                            <p className="text-xs text-muted-foreground">
                              {t('original')}: {attribute.original_attr_value}
                            </p>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2 ml-4">
                        <Badge variant="outline" className="text-xs">
                          {attribute.translations.length} {t('translations')}
                        </Badge>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setEditingAttribute(attribute.id.toString())}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleDeleteAttribute(attribute.id)}
                          disabled={loading === attribute.id.toString()}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Translation Management */}
                    {editingAttribute === attribute.id.toString() && (
                      <div className="mt-4 pt-4 border-t space-y-4">
                        <h4 className="font-medium">{t('manageTranslations')}</h4>
                        <div className="space-y-3">
                          {['en', 'fr', 'ar'].map((lang) => {
                            const langTranslation = attribute.translations.find(
                              t => t.language_code === lang
                            );
                            
                            return (
                              <div key={lang} className="grid grid-cols-3 gap-4 items-end">
                                <div className="space-y-2">
                                  <Label>{t('language')}</Label>
                                  <Badge variant="outline">{lang}</Badge>
                                </div>
                                <div className="space-y-2">
                                  <Label>{t('translatedKey')}</Label>
                                  <Input
                                    defaultValue={langTranslation?.attr_key || attribute.original_attr_key}
                                    placeholder={attribute.original_attr_key}
                                  />
                                </div>
                                <div className="space-y-2">
                                  <Label>{t('translatedValue')}</Label>
                                  <Input
                                    defaultValue={langTranslation?.attr_value || attribute.original_attr_value}
                                    placeholder={attribute.original_attr_value}
                                  />
                                </div>
                              </div>
                            );
                          })}
                        </div>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            onClick={() => {
                              // Handle save translations
                              setEditingAttribute(null);
                            }}
                          >
                            <Save className="h-4 w-4 mr-2" />
                            {t('saveTranslations')}
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setEditingAttribute(null)}
                          >
                            <X className="h-4 w-4 mr-2" />
                            {t('cancel')}
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
