'use client';

// components/admin/TrackingInfoForm.tsx
// Form for adding tracking information to warehouse receipts

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { updateWarehouseTracking } from '@/lib/actions/admin/warehouse.actions';

interface TrackingInfoFormProps {
  receiptId: string;
  locale: string;
}

export function TrackingInfoForm({ receiptId, locale }: TrackingInfoFormProps) {
  const t = useTranslations('admin');
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [formData, setFormData] = useState({
    tracking_number: '',
    carrier: '',
    shipping_label_url: '',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const result = await updateWarehouseTracking(receiptId, {
        tracking_number: formData.tracking_number,
        carrier: formData.carrier,
        shipping_label_url: formData.shipping_label_url || undefined,
      });

      if (result.success) {
        router.refresh();
      } else {
        setError(result.error || 'Failed to update tracking info');
      }
    } catch (err) {
      console.error('Error updating tracking:', err);
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {error && (
        <div className="p-3 text-sm text-destructive bg-destructive/10 rounded-md">
          {error}
        </div>
      )}

      <div className="space-y-2">
        <Label htmlFor="tracking_number">
          {t('trackingNumber')} *
        </Label>
        <Input
          id="tracking_number"
          value={formData.tracking_number}
          onChange={(e) =>
            setFormData({ ...formData, tracking_number: e.target.value })
          }
          placeholder={t('trackingNumberExample')}
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="carrier">
          {t('carrier')} *
        </Label>
        <Input
          id="carrier"
          value={formData.carrier}
          onChange={(e) =>
            setFormData({ ...formData, carrier: e.target.value })
          }
          placeholder={t('carrierPlaceholder')}
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="shipping_label_url">
          {t('shippingLabelUrl')} ({t('optional')})
        </Label>
        <Input
          id="shipping_label_url"
          type="url"
          value={formData.shipping_label_url}
          onChange={(e) =>
            setFormData({ ...formData, shipping_label_url: e.target.value })
          }
          placeholder={t('urlPlaceholder')}
        />
      </div>

      <Button type="submit" disabled={loading} className="w-full">
        {loading ? t('updating') : t('markAsShipped')}
      </Button>
    </form>
  );
}

