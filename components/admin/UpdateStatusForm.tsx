'use client';

// components/admin/UpdateStatusForm.tsx
// Form to update order status

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { updateOrderStatus } from '@/lib/actions/admin/order.actions';
import { OrderStatus } from '@/app/generated/prisma';

interface UpdateStatusFormProps {
  orderId: string;
  currentStatus: OrderStatus;
  currentTrackingNumber: string;
  locale: string;
}

export function UpdateStatusForm({
  orderId,
  currentStatus,
  currentTrackingNumber,
  locale,
}: UpdateStatusFormProps) {
  const t = useTranslations('admin');
  const router = useRouter();
  const [status, setStatus] = useState<OrderStatus>(currentStatus);
  const [trackingNumber, setTrackingNumber] = useState(currentTrackingNumber);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsSubmitting(true);

    try {
      const result = await updateOrderStatus(orderId, status, trackingNumber || undefined);

      if (result.success) {
        router.refresh();
      } else {
        setError(result.error || t('updateError'));
      }
    } catch (err) {
      console.error('Update error:', err);
      setError(err instanceof Error ? err.message : t('updateError'));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {error && (
        <div className="p-3 text-sm text-destructive bg-destructive/10 rounded-md">
          {error}
        </div>
      )}

      <div className="space-y-2">
        <Label htmlFor="status">{t('updateStatus')}</Label>
        <Select value={status} onValueChange={(value) => setStatus(value as OrderStatus)}>
          <SelectTrigger id="status">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="pending">Pending Payment</SelectItem>
            <SelectItem value="processing">Processing</SelectItem>
            <SelectItem value="shipped">Shipped</SelectItem>
            <SelectItem value="delivered">Delivered</SelectItem>
            <SelectItem value="cancelled">Cancelled</SelectItem>
            <SelectItem value="refunded">Refunded</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="trackingNumber">{t('trackingNumber')}</Label>
        <Input
          id="trackingNumber"
          placeholder={t('trackingNumberPlaceholder')}
          value={trackingNumber}
          onChange={(e) => setTrackingNumber(e.target.value)}
        />
      </div>

      <Button type="submit" disabled={isSubmitting} className="w-full">
        {isSubmitting ? t('updating') : t('updateOrder')}
      </Button>
    </form>
  );
}

