'use client';

// components/admin/WarehouseReceiptForm.tsx
// Form for creating warehouse receipts

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { createWarehouseReceipt } from '@/lib/actions/admin/warehouse.actions';

interface WarehouseReceiptFormProps {
  locale: string;
}

export function WarehouseReceiptForm({ locale }: WarehouseReceiptFormProps) {
  const t = useTranslations('admin');
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [formData, setFormData] = useState({
    marketplace_order_id: '',
    package_weight: '',
    package_weight_unit: 'g',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const result = await createWarehouseReceipt({
        marketplace_order_id: formData.marketplace_order_id,
        package_weight: parseFloat(formData.package_weight),
        package_weight_unit: formData.package_weight_unit,
      });

      if (result.success && result.receiptId) {
        router.push(`/${locale}/admin/warehouse/${result.receiptId}`);
      } else {
        setError(result.error || 'Failed to create receipt');
      }
    } catch (err) {
      console.error('Error creating receipt:', err);
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {error && (
        <div className="p-3 text-sm text-destructive bg-destructive/10 rounded-md">
          {error}
        </div>
      )}

      <div className="space-y-2">
        <Label htmlFor="marketplace_order_id">
          {t('marketplaceOrderId')} *
        </Label>
        <Input
          id="marketplace_order_id"
          value={formData.marketplace_order_id}
          onChange={(e) =>
            setFormData({ ...formData, marketplace_order_id: e.target.value })
          }
          placeholder={t('marketplaceOrderIdPlaceholder')}
          required
        />
        <p className="text-xs text-muted-foreground">
          {t('marketplaceOrderIdHelp')}
        </p>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="package_weight">
            {t('packageWeight')} *
          </Label>
          <Input
            id="package_weight"
            type="number"
            step="0.01"
            min="0"
            value={formData.package_weight}
            onChange={(e) =>
              setFormData({ ...formData, package_weight: e.target.value })
            }
            placeholder={t('weightPlaceholder')}
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="package_weight_unit">
            {t('unit')}
          </Label>
          <Select
            value={formData.package_weight_unit}
            onValueChange={(value) =>
              setFormData({ ...formData, package_weight_unit: value })
            }
          >
            <SelectTrigger id="package_weight_unit">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="g">{t('unitGrams')}</SelectItem>
              <SelectItem value="kg">{t('unitKilograms')}</SelectItem>
              <SelectItem value="lb">{t('unitPounds')}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="flex gap-2 pt-4">
        <Button type="submit" disabled={loading}>
          {loading ? t('creating') : t('createReceipt')}
        </Button>
        <Button
          type="button"
          variant="outline"
          onClick={() => router.back()}
          disabled={loading}
        >
          {t('cancel')}
        </Button>
      </div>
    </form>
  );
}

