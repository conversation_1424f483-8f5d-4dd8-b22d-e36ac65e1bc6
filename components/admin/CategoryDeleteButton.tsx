'use client';

// components/admin/CategoryDeleteButton.tsx
// Delete button for categories with confirmation

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { deleteCategory } from '@/lib/actions/admin/category.actions';

interface CategoryDeleteButtonProps {
  categoryId: number;
  categoryName: string;
  hasChildren: boolean;
  hasProducts: boolean;
  locale: string;
}

export function CategoryDeleteButton({
  categoryId,
  categoryName,
  hasChildren,
  hasProducts,
  locale,
}: CategoryDeleteButtonProps) {
  const t = useTranslations('admin');
  const router = useRouter();
  const [deleting, setDeleting] = useState(false);
  const [open, setOpen] = useState(false);

  const canDelete = !hasChildren && !hasProducts;

  const handleDelete = async () => {
    setDeleting(true);
    
    try {
      const result = await deleteCategory(categoryId);
      
      if (result.success) {
        router.push(`/${locale}/admin/categories`);
      } else {
        alert(result.error);
      }
    } catch (error) {
      console.error('Error deleting category:', error);
      alert('Failed to delete category');
    } finally {
      setDeleting(false);
      setOpen(false);
    }
  };

  if (!canDelete) {
    return (
      <Button variant="outline" disabled title={
        hasChildren 
          ? t('cannotDeleteCategoryWithChildren')
          : t('cannotDeleteCategoryWithProducts')
      }>
        <Trash2 className="h-4 w-4 mr-2" />
        {t('delete')}
      </Button>
    );
  }

  return (
    <AlertDialog open={open} onOpenChange={setOpen}>
      <AlertDialogTrigger asChild>
        <Button variant="outline">
          <Trash2 className="h-4 w-4 mr-2" />
          {t('delete')}
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{t('confirmDeleteCategory')}</AlertDialogTitle>
          <AlertDialogDescription>
            {t('confirmDeleteCategoryDescription', { name: categoryName })}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={deleting}>
            {t('cancel')}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={deleting}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {deleting ? t('deleting') : t('delete')}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
