'use client';

// components/admin/WarehouseReceiptsTable.tsx
// Warehouse receipts table with filters and pagination

import { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { formatDate } from '@/lib/utils';
import type { WarehouseReceiptListItem } from '@/lib/types/admin';

interface WarehouseReceiptsTableProps {
  receipts: WarehouseReceiptListItem[];
  total: number;
  page: number;
  totalPages: number;
  locale: string;
}

const STATUS_VARIANTS = {
  pending: 'secondary' as const,
  matched: 'default' as const,
  shipped: 'outline' as const,
};

export function WarehouseReceiptsTable({
  receipts,
  total,
  page,
  totalPages,
  locale,
}: WarehouseReceiptsTableProps) {
  const t = useTranslations('admin');
  const router = useRouter();
  const searchParams = useSearchParams();
  const [search, setSearch] = useState(searchParams.get('marketplaceOrderId') || '');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const params = new URLSearchParams(searchParams.toString());
    if (search) {
      params.set('marketplaceOrderId', search);
    } else {
      params.delete('marketplaceOrderId');
    }
    params.delete('page');
    router.push(`/${locale}/admin/warehouse?${params.toString()}`);
  };

  const handleStatusFilter = (status: string) => {
    const params = new URLSearchParams(searchParams.toString());
    if (status && status !== 'all') {
      params.set('status', status);
    } else {
      params.delete('status');
    }
    params.delete('page');
    router.push(`/${locale}/admin/warehouse?${params.toString()}`);
  };

  const handlePageChange = (newPage: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('page', newPage.toString());
    router.push(`/${locale}/admin/warehouse?${params.toString()}`);
  };

  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="flex gap-4">
        <form onSubmit={handleSearch} className="flex-1 flex gap-2">
          <Input
            placeholder={t('searchMarketplaceOrderId')}
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="max-w-sm"
          />
          <Button type="submit" variant="secondary">
            <Search className="h-4 w-4 mr-2" />
            {t('search')}
          </Button>
        </form>

        <Select
          value={searchParams.get('status') || 'all'}
          onValueChange={handleStatusFilter}
        >
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder={t('filterByStatus')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t('allStatuses')}</SelectItem>
            <SelectItem value="pending">{t('statusPending')}</SelectItem>
            <SelectItem value="matched">{t('statusMatched')}</SelectItem>
            <SelectItem value="shipped">{t('statusShipped')}</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Table */}
      <div className="border rounded-lg">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-muted">
              <tr>
                <th className="px-4 py-3 text-left text-sm font-medium">
                  {t('marketplaceOrderId')}
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium">
                  {t('product')}
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium">
                  {t('customer')}
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium">
                  {t('weight')}
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium">
                  {t('received')}
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium">
                  {t('status')}
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium">
                  {t('actions')}
                </th>
              </tr>
            </thead>
            <tbody className="divide-y">
              {receipts.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-4 py-8 text-center text-muted-foreground">
                    {t('noReceipts')}
                  </td>
                </tr>
              ) : (
                receipts.map((receipt) => {
                  const productName =
                    receipt.order_item.product.translations[0]?.name ||
                    receipt.order_item.product.original_name;
                  const statusVariant = STATUS_VARIANTS[receipt.status];
                  const statusKey = `status${receipt.status.charAt(0).toUpperCase() + receipt.status.slice(1)}` as 'statusPending' | 'statusMatched' | 'statusShipped';
                  const statusLabel = t(statusKey);

                  return (
                    <tr key={receipt.id} className="hover:bg-muted/50">
                      <td className="px-4 py-3 text-sm font-mono">
                        {receipt.marketplace_order_id}
                      </td>
                      <td className="px-4 py-3 text-sm">
                        {productName}
                      </td>
                      <td className="px-4 py-3 text-sm">
                        <div>
                          <p className="font-medium">
                            {receipt.order_item.order.customer.full_name}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {receipt.order_item.order.customer.email}
                          </p>
                        </div>
                      </td>
                      <td className="px-4 py-3 text-sm">
                        {Number(receipt.package_weight)} {receipt.package_weight_unit}
                      </td>
                      <td className="px-4 py-3 text-sm">
                        {formatDate(receipt.received_at)}
                      </td>
                      <td className="px-4 py-3">
                        <Badge variant={statusVariant}>
                          {statusLabel}
                        </Badge>
                      </td>
                      <td className="px-4 py-3">
                        <Link href={`/${locale}/admin/warehouse/${receipt.id}`}>
                          <Button variant="ghost" size="sm">
                            {t('view')}
                          </Button>
                        </Link>
                      </td>
                    </tr>
                  );
                })
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            {t('showing')} {(page - 1) * 20 + 1} - {Math.min(page * 20, total)} {t('of')} {total}
          </p>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(page - 1)}
              disabled={page === 1}
            >
              {t('previous')}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(page + 1)}
              disabled={page === totalPages}
            >
              {t('next')}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}

