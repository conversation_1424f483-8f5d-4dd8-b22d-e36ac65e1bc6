'use client';

// components/admin/CategoriesTable.tsx
// Categories table with hierarchical display

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { Edit, Trash2, Plus, ChevronRight, ChevronDown, FolderOpen, Folder } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { deleteCategory } from '@/lib/actions/admin/category.actions';
import type { CategoryWithTranslations, CategoryWithTree } from '@/lib/types/admin';

interface CategoriesTableProps {
  categories: CategoryWithTranslations[];
  locale: string;
}

export function CategoriesTable({ categories, locale }: CategoriesTableProps) {
  const t = useTranslations('admin');
  const router = useRouter();
  const [deleting, setDeleting] = useState<number | null>(null);
  const [expandedCategories, setExpandedCategories] = useState<Set<number>>(new Set());

  // Build hierarchical structure with counts
  const buildCategoryTree = (cats: CategoryWithTranslations[]): CategoryWithTree[] => {
    const categoryMap = new Map<number, CategoryWithTree>();
    const rootCategories: CategoryWithTree[] = [];

    // First pass: create all category nodes
    cats.forEach(cat => {
      categoryMap.set(cat.id, {
        ...cat,
        children: [],
        _count: {
          products: 0, // We'll calculate this if needed
          children: 0
        }
      });
    });

    // Second pass: build the tree
    cats.forEach(cat => {
      const node = categoryMap.get(cat.id)!;
      if (cat.parent_id) {
        const parent = categoryMap.get(cat.parent_id);
        if (parent) {
          parent.children.push(node);
          parent._count.children++;
          node.parent = parent;
        }
      } else {
        rootCategories.push(node);
      }
    });

    return rootCategories;
  };

  const categoryTree = buildCategoryTree(categories);
  const categoryMap = new Map(categories.map(cat => [cat.id, cat]));

  const handleDelete = async (categoryId: number) => {
    const category = categoryMap.get(categoryId);
    const categoryName = getCategoryName(category!, locale);
    
    if (!confirm(t('confirmDeleteCategory', { name: categoryName }))) return;

    setDeleting(categoryId);
    
    try {
      const result = await deleteCategory(categoryId);
      
      if (result.success) {
        router.refresh();
      } else {
        alert(result.error);
      }
    } catch (error) {
      console.error('Error deleting category:', error);
      alert('Failed to delete category');
    } finally {
      setDeleting(null);
    }
  };

  const toggleExpanded = (categoryId: number) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId);
    } else {
      newExpanded.add(categoryId);
    }
    setExpandedCategories(newExpanded);
  };

  const getCategoryName = (category: CategoryWithTranslations, locale: string) => {
    const translation = category.translations.find(t => t.language_code === locale) ||
                       category.translations.find(t => t.language_code === 'en') ||
                       category.translations[0];
    return translation?.name || `Category ${category.id}`;
  };

  const getCategoryDescription = (category: CategoryWithTranslations, locale: string) => {
    const translation = category.translations.find(t => t.language_code === locale) ||
                       category.translations.find(t => t.language_code === 'en') ||
                       category.translations[0];
    return translation?.description;
  };

  const renderCategory = (category: CategoryWithTree, level = 0) => {
    const hasChildren = category.children.length > 0;
    const isExpanded = expandedCategories.has(category.id);
    const categoryName = getCategoryName(category, locale);
    const categoryDescription = getCategoryDescription(category, locale);

    return (
      <div key={category.id}>
        {/* Category Row */}
        <div className="border-b">
          <div className="flex items-center p-4" style={{ paddingLeft: `${16 + level * 24}px` }}>
            {/* Expand/Collapse Button */}
            <div className="w-6 h-6 flex items-center justify-center mr-2">
              {hasChildren ? (
                <button
                  onClick={() => toggleExpanded(category.id)}
                  className="hover:bg-muted rounded p-1"
                >
                  {isExpanded ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </button>
              ) : (
                <div className="w-4 h-4" />
              )}
            </div>

            {/* Category Icon */}
            <div className="mr-3">
              {hasChildren ? (
                isExpanded ? (
                  <FolderOpen className="h-5 w-5 text-muted-foreground" />
                ) : (
                  <Folder className="h-5 w-5 text-muted-foreground" />
                )
              ) : (
                <div className="h-5 w-5 border rounded border-muted-foreground/30" />
              )}
            </div>

            {/* Category Info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-3">
                <div className="flex-1 min-w-0">
                  <p className="font-medium truncate">{categoryName}</p>
                  {categoryDescription && (
                    <p className="text-sm text-muted-foreground truncate">
                      {categoryDescription}
                    </p>
                  )}
                </div>
                
                <div className="flex items-center space-x-4">
                  {/* Stats */}
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className="text-xs">
                      {category._count.products} {t('products')}
                    </Badge>
                    {hasChildren && (
                      <Badge variant="outline" className="text-xs">
                        {category._count.children} {t('subcategories')}
                      </Badge>
                    )}
                    <Badge variant="outline" className="text-xs">
                      {category.translations.length} {t('translations')}
                    </Badge>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center space-x-1">
                    <Link href={`/${locale}/admin/categories/${category.id}/new`}>
                      <Button variant="ghost" size="sm" title={t('addSubcategory')}>
                        <Plus className="h-4 w-4" />
                      </Button>
                    </Link>
                    <Link href={`/${locale}/admin/categories/${category.id}`}>
                      <Button variant="ghost" size="sm" title={t('viewDetails')}>
                        <Edit className="h-4 w-4" />
                      </Button>
                    </Link>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(category.id)}
                      disabled={deleting === category.id}
                      title={t('delete')}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Children */}
        {hasChildren && isExpanded && (
          <div>
            {category.children.map(child => renderCategory(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Table Header */}
      <div className="border rounded-lg">
        <div className="bg-muted p-4 border-b">
          <div className="flex items-center justify-between">
            <h3 className="font-medium">{t('categories')}</h3>
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setExpandedCategories(new Set(categories.map(c => c.id)))}
              >
                {t('expandAll')}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setExpandedCategories(new Set())}
              >
                {t('collapseAll')}
              </Button>
            </div>
          </div>
        </div>

        {/* Categories Tree */}
        <div>
          {categoryTree.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-muted-foreground">{t('noCategories')}</p>
              <Link href={`/${locale}/admin/categories/new`}>
                <Button className="mt-4">
                  <Plus className="h-4 w-4 mr-2" />
                  {t('createFirstCategory')}
                </Button>
              </Link>
            </div>
          ) : (
            <div>
              {categoryTree.map(category => renderCategory(category))}
            </div>
          )}
        </div>
      </div>

      {/* Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-card border rounded-lg p-4">
          <div className="text-center">
            <p className="text-2xl font-bold">{categories.length}</p>
            <p className="text-sm text-muted-foreground">{t('totalCategories')}</p>
          </div>
        </div>
        <div className="bg-card border rounded-lg p-4">
          <div className="text-center">
            <p className="text-2xl font-bold">{categoryTree.length}</p>
            <p className="text-sm text-muted-foreground">{t('rootCategories')}</p>
          </div>
        </div>
        <div className="bg-card border rounded-lg p-4">
          <div className="text-center">
            <p className="text-2xl font-bold">
              {categories.length}
            </p>
            <p className="text-sm text-muted-foreground">{t('totalProducts')}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
