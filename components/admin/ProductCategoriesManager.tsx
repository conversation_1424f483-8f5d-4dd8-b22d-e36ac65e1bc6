'use client';

// components/admin/ProductCategoriesManager.tsx
// Manage product categories assignment

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Plus, X, Search } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

interface ProductCategoriesManagerProps {
  productId: number;
  currentCategories: Array<{
    category: {
      id: number;
      translations: Array<{
        language_code: string;
        name: string;
      }>;
    };
  }>;
  locale: string;
}

interface Category {
  id: number;
  parent_id: number | null;
  translations: Array<{
    language_code: string;
    name: string;
    slug: string;
  }>;
  children?: Category[];
}

export function ProductCategoriesManager({
  productId,
  currentCategories,
  locale
}: ProductCategoriesManagerProps) {
  const t = useTranslations('admin');
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategories, setSelectedCategories] = useState<number[]>(
    currentCategories.map(pc => pc.category.id)
  );
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Fetch all categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/admin/categories');
        if (response.ok) {
          const data = await response.json();
          setCategories(data);
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
      }
    };

    fetchCategories();
  }, []);

  const handleRemoveCategory = async (categoryId: number) => {
    setLoading(true);
    
    try {
      const response = await fetch(`/api/admin/products/${productId}/categories/${categoryId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setSelectedCategories(prev => prev.filter(id => id !== categoryId));
        router.refresh();
      } else {
        console.error('Failed to remove category');
      }
    } catch (error) {
      console.error('Error removing category:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddCategories = async () => {
    setLoading(true);
    
    try {
      const newCategoryIds = selectedCategories.filter(
        id => !currentCategories.some(pc => pc.category.id === id)
      );

      if (newCategoryIds.length === 0) {
        setIsDialogOpen(false);
        return;
      }

      const response = await fetch(`/api/admin/products/${productId}/categories`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ categoryIds: newCategoryIds }),
      });

      if (response.ok) {
        setIsDialogOpen(false);
        router.refresh();
      } else {
        console.error('Failed to add categories');
      }
    } catch (error) {
      console.error('Error adding categories:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCategoryToggle = (categoryId: number, checked: boolean) => {
    if (checked) {
      setSelectedCategories(prev => [...prev, categoryId]);
    } else {
      setSelectedCategories(prev => prev.filter(id => id !== categoryId));
    }
  };

  const getCategoryName = (category: Category) => {
    const translation = category.translations.find(t => t.language_code === locale) ||
                       category.translations.find(t => t.language_code === 'en') ||
                       category.translations[0];
    return translation?.name || `Category ${category.id}`;
  };

  const filteredCategories = categories.filter(category => {
    const name = getCategoryName(category);
    return name.toLowerCase().includes(searchTerm.toLowerCase());
  });

  const renderCategoryTree = (cats: Category[], level = 0) => {
    return cats.map(category => (
      <div key={category.id} className={`${level > 0 ? 'ml-6' : ''}`}>
        <div className="flex items-center space-x-2 py-2">
          <Checkbox
            id={`category-${category.id}`}
            checked={selectedCategories.includes(category.id)}
            onCheckedChange={(checked) => handleCategoryToggle(category.id, checked as boolean)}
          />
          <label
            htmlFor={`category-${category.id}`}
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            {getCategoryName(category)}
          </label>
        </div>
        {category.children && category.children.length > 0 && (
          <div className="ml-4">
            {renderCategoryTree(category.children, level + 1)}
          </div>
        )}
      </div>
    ));
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>{t('productCategories')}</CardTitle>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm">
              <Plus className="h-4 w-4 mr-2" />
              {t('manageCategories')}
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>{t('selectCategories')}</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder={t('searchCategories')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Categories Tree */}
              <div className="max-h-96 overflow-y-auto border rounded-lg p-4">
                {filteredCategories.length === 0 ? (
                  <p className="text-muted-foreground text-center py-4">
                    {t('noCategoriesFound')}
                  </p>
                ) : (
                  renderCategoryTree(filteredCategories)
                )}
              </div>

              {/* Actions */}
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => setIsDialogOpen(false)}
                  disabled={loading}
                >
                  {t('cancel')}
                </Button>
                <Button
                  onClick={handleAddCategories}
                  disabled={loading}
                >
                  {loading ? t('saving') : t('saveCategories')}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </CardHeader>
      <CardContent>
        {currentCategories.length === 0 ? (
          <p className="text-muted-foreground text-center py-8">{t('noCategories')}</p>
        ) : (
          <div className="flex flex-wrap gap-2">
            {currentCategories.map((pc) => {
              const categoryTranslation = pc.category.translations.find(
                t => t.language_code === locale
              ) || pc.category.translations.find(
                t => t.language_code === 'en'
              ) || pc.category.translations[0];
              
              return (
                <Badge
                  key={pc.category.id}
                  variant="secondary"
                  className="flex items-center gap-2"
                >
                  {categoryTranslation?.name || `Category ${pc.category.id}`}
                  <button
                    onClick={() => handleRemoveCategory(pc.category.id)}
                    className="ml-1 hover:bg-destructive hover:text-destructive-foreground rounded-full p-0.5"
                    disabled={loading}
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
