'use client';

// components/admin/RoleAssignmentForm.tsx
// Form to assign/remove roles from customers

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { assignRoleToCustomer, removeRoleFromCustomer } from '@/lib/actions/admin/customer.actions';
import type { RoleTransformed } from '@/lib/types/admin';

interface RoleAssignmentFormProps {
  customerId: number;
  currentRoles: RoleTransformed[];
  availableRoles: RoleTransformed[];
  locale: string;
}

export function RoleAssignmentForm({
  customerId,
  currentRoles,
  availableRoles,
  locale,
}: RoleAssignmentFormProps) {
  const t = useTranslations('admin');
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  const currentRoleIds = currentRoles.map((r) => r.id);

  const handleAssignRole = async (roleId: number) => {
    setError('');
    setIsSubmitting(true);

    try {
      const result = await assignRoleToCustomer(customerId, roleId);

      if (result.success) {
        router.refresh();
      } else {
        setError(result.error || 'Failed to assign role');
      }
    } catch (err) {
      console.error('Assign role error:', err);
      setError(err instanceof Error ? err.message : 'Failed to assign role');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRemoveRole = async (roleId: number) => {
    setError('');
    setIsSubmitting(true);

    try {
      const result = await removeRoleFromCustomer(customerId, roleId);

      if (result.success) {
        router.refresh();
      } else {
        setError(result.error || 'Failed to remove role');
      }
    } catch (err) {
      console.error('Remove role error:', err);
      setError(err instanceof Error ? err.message : 'Failed to remove role');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-4">
      {error && (
        <div className="p-3 text-sm text-destructive bg-destructive/10 rounded-md">
          {error}
        </div>
      )}

      {/* Current Roles */}
      <div>
        <h3 className="text-sm font-medium mb-2">Current Roles</h3>
        {currentRoles.length === 0 ? (
          <p className="text-sm text-muted-foreground">No roles assigned</p>
        ) : (
          <div className="space-y-2">
            {currentRoles.map((role) => (
              <div key={role.id} className="flex items-center justify-between p-2 border rounded">
                <div>
                  <p className="font-medium text-sm">{role.name}</p>
                  <p className="text-xs text-muted-foreground">
                    {role.permissions?.length || 0} permissions
                  </p>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleRemoveRole(role.id)}
                  disabled={isSubmitting}
                >
                  Remove
                </Button>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Available Roles */}
      <div>
        <h3 className="text-sm font-medium mb-2">Available Roles</h3>
        {availableRoles.filter((r) => !currentRoleIds.includes(r.id)).length === 0 ? (
          <p className="text-sm text-muted-foreground">All roles assigned</p>
        ) : (
          <div className="space-y-2">
            {availableRoles
              .filter((r) => !currentRoleIds.includes(r.id))
              .map((role) => (
                <div key={role.id} className="flex items-center justify-between p-2 border rounded">
                  <div>
                    <p className="font-medium text-sm">{role.name}</p>
                    <p className="text-xs text-muted-foreground">
                      {role.permissions?.length || 0} permissions
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleAssignRole(role.id)}
                    disabled={isSubmitting}
                  >
                    Assign
                  </Button>
                </div>
              ))}
          </div>
        )}
      </div>
    </div>
  );
}

