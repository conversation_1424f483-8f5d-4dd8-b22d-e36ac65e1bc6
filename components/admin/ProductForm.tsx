'use client';

// components/admin/ProductForm.tsx
// Comprehensive product form for create/edit operations

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { Save, Plus, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import type { ProductAdminDetail } from '@/lib/types/admin';

interface ProductFormProps {
  mode: 'create' | 'edit';
  product?: ProductAdminDetail;
  locale: string;
}

interface ProductFormData {
  // Basic product info
  original_name: string;
  product_url: string;
  marketplace: 'taobao' | 'pinduoduo' | 'alibaba';
  can_show: boolean;
  weight?: number;
  weight_unit: string;
  
  // Translations
  translations: Array<{
    language_code: string;
    name: string;
    slug: string;
  }>;
  
  // Images
  images: Array<{
    image_url: string;
    image_type: 'preview' | 'description' | 'video';
  }>;
  
  // Variants
  variants: Array<{
    original_variant_name: string;
    original_variant_type: string;
    available_quantity?: number;
    min_quantity?: number;
    price_low: number;
    price_high?: number;
    currency: string;
  }>;
  
  // Offers
  offers: Array<{
    min_quantity: number;
    price_low: number;
    price_high?: number;
    currency: string;
    quantity_info?: string;
  }>;
  
  // Attributes
  attributes: Array<{
    original_attr_key: string;
    original_attr_value: string;
  }>;
}

const MARKETPLACES = [
  { value: 'alibaba', label: 'Alibaba' },
  { value: 'taobao', label: 'Taobao' },
  { value: 'pinduoduo', label: 'Pinduoduo' },
];

const LANGUAGES = [
  { code: 'en', name: 'English' },
  { code: 'fr', name: 'Français' },
  { code: 'ar', name: 'العربية' },
];

const IMAGE_TYPES = [
  { value: 'preview', label: 'Preview' },
  { value: 'description', label: 'Description' },
  { value: 'video', label: 'Video' },
];

export function ProductForm({ mode, product, locale }: ProductFormProps) {
  const t = useTranslations('admin');
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const [formData, setFormData] = useState<ProductFormData>(() => {
    if (mode === 'edit' && product) {
      return {
        original_name: product.original_name || '',
        product_url: product.product_url,
        marketplace: product.marketplace as 'taobao' | 'pinduoduo' | 'alibaba',
        can_show: product.can_show || false,
        weight: product.weight ? Number(product.weight) : undefined,
        weight_unit: product.weight_unit || 'g',
        translations: product.translations.map(t => ({
          language_code: t.language_code,
          name: t.name,
          slug: t.slug,
        })),
        images: product.product_images.map(img => ({
          image_url: img.image_url,
          image_type: img.image_type,
        })),
        variants: product.variants.map(v => ({
          original_variant_name: v.original_variant_name,
          original_variant_type: v.original_variant_type,
          available_quantity: v.available_quantity ? Number(v.available_quantity) : undefined,
          min_quantity: v.min_quantity ? Number(v.min_quantity) : undefined,
          price_low: Number(v.price_low),
          price_high: v.price_high ? Number(v.price_high) : undefined,
          currency: v.currency,
        })),
        offers: product.offers.map(o => ({
          min_quantity: Number(o.min_quantity),
          price_low: Number(o.price_low),
          price_high: o.price_high ? Number(o.price_high) : undefined,
          currency: o.currency,
          quantity_info: o.quantity_info || undefined,
        })),
        attributes: [],
      };
    }
    
    return {
      original_name: '',
      product_url: '',
      marketplace: 'alibaba',
      can_show: false,
      weight: undefined,
      weight_unit: 'g',
      translations: LANGUAGES.map(lang => ({
        language_code: lang.code,
        name: '',
        slug: '',
      })),
      images: [],
      variants: [],
      offers: [],
      attributes: [],
    };
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const url = mode === 'create' 
        ? '/api/admin/products'
        : `/api/admin/products/${product?.id}`;
      
      const method = mode === 'create' ? 'POST' : 'PATCH';

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        const result = await response.json();
        if (mode === 'create') {
          router.push(`/${locale}/admin/products/${result.id}`);
        } else {
          router.refresh();
        }
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to save product');
      }
    } catch (err) {
      console.error('Error saving product:', err);
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const addTranslation = () => {
    setFormData(prev => ({
      ...prev,
      translations: [...prev.translations, { language_code: 'en', name: '', slug: '' }]
    }));
  };

  const removeTranslation = (index: number) => {
    setFormData(prev => ({
      ...prev,
      translations: prev.translations.filter((_, i) => i !== index)
    }));
  };

  const updateTranslation = (index: number, field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      translations: prev.translations.map((t, i) => 
        i === index ? { ...t, [field]: value } : t
      )
    }));
  };

  const addImage = () => {
    setFormData(prev => ({
      ...prev,
      images: [...prev.images, { image_url: '', image_type: 'preview' }]
    }));
  };

  const removeImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }));
  };

  const updateImage = (index: number, field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.map((img, i) => 
        i === index ? { ...img, [field]: value } : img
      )
    }));
  };

  const addVariant = () => {
    setFormData(prev => ({
      ...prev,
      variants: [...prev.variants, {
        original_variant_name: '',
        original_variant_type: '',
        price_low: 0,
        currency: 'CNY'
      }]
    }));
  };

  const removeVariant = (index: number) => {
    setFormData(prev => ({
      ...prev,
      variants: prev.variants.filter((_, i) => i !== index)
    }));
  };

  const updateVariant = (index: number, field: string, value: string | number | undefined) => {
    setFormData(prev => ({
      ...prev,
      variants: prev.variants.map((v, i) => 
        i === index ? { ...v, [field]: value } : v
      )
    }));
  };

  const addOffer = () => {
    setFormData(prev => ({
      ...prev,
      offers: [...prev.offers, {
        min_quantity: 1,
        price_low: 0,
        currency: 'CNY'
      }]
    }));
  };

  const removeOffer = (index: number) => {
    setFormData(prev => ({
      ...prev,
      offers: prev.offers.filter((_, i) => i !== index)
    }));
  };

  const updateOffer = (index: number, field: string, value: string | number | undefined) => {
    setFormData(prev => ({
      ...prev,
      offers: prev.offers.map((o, i) => 
        i === index ? { ...o, [field]: value } : o
      )
    }));
  };

  const addAttribute = () => {
    setFormData(prev => ({
      ...prev,
      attributes: [...prev.attributes, {
        original_attr_key: '',
        original_attr_value: ''
      }]
    }));
  };

  const removeAttribute = (index: number) => {
    setFormData(prev => ({
      ...prev,
      attributes: prev.attributes.filter((_, i) => i !== index)
    }));
  };

  const updateAttribute = (index: number, field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      attributes: prev.attributes.map((attr, i) =>
        i === index ? { ...attr, [field]: value } : attr
      )
    }));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <div className="p-3 text-sm text-destructive bg-destructive/10 rounded-md">
          {error}
        </div>
      )}

      <Tabs defaultValue="basic" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="basic">{t('basicInfo')}</TabsTrigger>
          <TabsTrigger value="translations">{t('translations')}</TabsTrigger>
          <TabsTrigger value="images">{t('images')}</TabsTrigger>
          <TabsTrigger value="variants">{t('variants')}</TabsTrigger>
          <TabsTrigger value="pricing">{t('pricing')}</TabsTrigger>
        </TabsList>

        {/* Basic Information Tab */}
        <TabsContent value="basic" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="original_name">{t('originalName')}</Label>
              <Input
                id="original_name"
                value={formData.original_name}
                onChange={(e) => setFormData(prev => ({ ...prev, original_name: e.target.value }))}
                placeholder={t('originalNamePlaceholder')}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="marketplace">{t('marketplace')}</Label>
              <Select
                value={formData.marketplace}
                onValueChange={(value) => setFormData(prev => ({ ...prev, marketplace: value as 'taobao' | 'pinduoduo' | 'alibaba' }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {MARKETPLACES.map((marketplace) => (
                    <SelectItem key={marketplace.value} value={marketplace.value}>
                      {marketplace.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="col-span-2 space-y-2">
              <Label htmlFor="product_url">{t('productUrl')} *</Label>
              <Input
                id="product_url"
                value={formData.product_url}
                onChange={(e) => setFormData(prev => ({ ...prev, product_url: e.target.value }))}
                placeholder="https://example.com/product"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="weight">{t('weight')}</Label>
              <Input
                id="weight"
                type="number"
                step="0.01"
                min="0"
                value={formData.weight || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  weight: e.target.value ? parseFloat(e.target.value) : undefined
                }))}
                placeholder="0.00"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="weight_unit">{t('weightUnit')}</Label>
              <Select
                value={formData.weight_unit}
                onValueChange={(value) => setFormData(prev => ({ ...prev, weight_unit: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="g">Grams (g)</SelectItem>
                  <SelectItem value="kg">Kilograms (kg)</SelectItem>
                  <SelectItem value="lb">Pounds (lb)</SelectItem>
                  <SelectItem value="oz">Ounces (oz)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="col-span-2 flex items-center space-x-2">
              <Checkbox
                id="can_show"
                checked={formData.can_show}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, can_show: Boolean(checked) }))}
              />
              <Label htmlFor="can_show">{t('visibleToCustomers')}</Label>
            </div>
          </div>
        </TabsContent>

        {/* Translations Tab */}
        <TabsContent value="translations" className="space-y-6">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">{t('productTranslations')}</h3>
            <Button type="button" variant="outline" size="sm" onClick={addTranslation}>
              <Plus className="h-4 w-4 mr-2" />
              {t('addTranslation')}
            </Button>
          </div>

          <div className="space-y-4">
            {formData.translations.map((translation, index) => (
              <Card key={index}>
                <CardHeader className="flex flex-row items-center justify-between">
                  <Badge variant="outline">{translation.language_code}</Badge>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeTranslation(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label>{t('language')}</Label>
                      <Select
                        value={translation.language_code}
                        onValueChange={(value) => updateTranslation(index, 'language_code', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {LANGUAGES.map((lang) => (
                            <SelectItem key={lang.code} value={lang.code}>
                              {lang.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label>{t('name')}</Label>
                      <Input
                        value={translation.name}
                        onChange={(e) => updateTranslation(index, 'name', e.target.value)}
                        placeholder={t('productName')}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>{t('slug')}</Label>
                      <Input
                        value={translation.slug}
                        onChange={(e) => updateTranslation(index, 'slug', e.target.value)}
                        placeholder="product-slug"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Images Tab */}
        <TabsContent value="images" className="space-y-6">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">{t('productImages')}</h3>
            <Button type="button" variant="outline" size="sm" onClick={addImage}>
              <Plus className="h-4 w-4 mr-2" />
              {t('addImage')}
            </Button>
          </div>

          <div className="space-y-4">
            {formData.images.map((image, index) => (
              <Card key={index}>
                <CardHeader className="flex flex-row items-center justify-between">
                  <Badge variant="outline">{image.image_type}</Badge>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeImage(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>{t('imageUrl')}</Label>
                      <Input
                        value={image.image_url}
                        onChange={(e) => updateImage(index, 'image_url', e.target.value)}
                        placeholder="https://example.com/image.jpg"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>{t('imageType')}</Label>
                      <Select
                        value={image.image_type}
                        onValueChange={(value) => updateImage(index, 'image_type', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {IMAGE_TYPES.map((type) => (
                            <SelectItem key={type.value} value={type.value}>
                              {type.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  {image.image_url && (
                    <div className="w-32 h-32 rounded-lg overflow-hidden bg-muted relative">
                      <Image
                        src={image.image_url}
                        alt={`Preview ${index + 1}`}
                        fill
                        className="object-cover"
                        sizes="128px"
                      />
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Variants Tab */}
        <TabsContent value="variants" className="space-y-6">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">{t('productVariants')}</h3>
            <Button type="button" variant="outline" size="sm" onClick={addVariant}>
              <Plus className="h-4 w-4 mr-2" />
              {t('addVariant')}
            </Button>
          </div>

          <div className="space-y-4">
            {formData.variants.map((variant, index) => (
              <Card key={index}>
                <CardHeader className="flex flex-row items-center justify-between">
                  <h4 className="font-medium">{t('variant')} {index + 1}</h4>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeVariant(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>{t('variantName')}</Label>
                      <Input
                        value={variant.original_variant_name}
                        onChange={(e) => updateVariant(index, 'original_variant_name', e.target.value)}
                        placeholder={t('variantNamePlaceholder')}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>{t('variantType')}</Label>
                      <Input
                        value={variant.original_variant_type}
                        onChange={(e) => updateVariant(index, 'original_variant_type', e.target.value)}
                        placeholder={t('variantTypePlaceholder')}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>{t('priceFrom')}</Label>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        value={variant.price_low}
                        onChange={(e) => updateVariant(index, 'price_low', parseFloat(e.target.value) || 0)}
                        placeholder="0.00"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>{t('priceTo')}</Label>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        value={variant.price_high || ''}
                        onChange={(e) => updateVariant(index, 'price_high', e.target.value ? parseFloat(e.target.value) : undefined)}
                        placeholder={t('optional')}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>{t('availableQuantity')}</Label>
                      <Input
                        type="number"
                        min="0"
                        value={variant.available_quantity || ''}
                        onChange={(e) => updateVariant(index, 'available_quantity', e.target.value ? parseInt(e.target.value) : undefined)}
                        placeholder={t('unlimited')}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>{t('minQuantity')}</Label>
                      <Input
                        type="number"
                        min="1"
                        value={variant.min_quantity || ''}
                        onChange={(e) => updateVariant(index, 'min_quantity', e.target.value ? parseInt(e.target.value) : undefined)}
                        placeholder="1"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Pricing Tab */}
        <TabsContent value="pricing" className="space-y-6">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">{t('pricingOffers')}</h3>
            <Button type="button" variant="outline" size="sm" onClick={addOffer}>
              <Plus className="h-4 w-4 mr-2" />
              {t('addOffer')}
            </Button>
          </div>

          <div className="space-y-4">
            {formData.offers.map((offer, index) => (
              <Card key={index}>
                <CardHeader className="flex flex-row items-center justify-between">
                  <h4 className="font-medium">{t('offer')} {index + 1}</h4>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeOffer(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>{t('minimumQuantity')}</Label>
                      <Input
                        type="number"
                        min="1"
                        value={offer.min_quantity}
                        onChange={(e) => updateOffer(index, 'min_quantity', parseInt(e.target.value) || 1)}
                        placeholder="1"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>{t('currency')}</Label>
                      <Select
                        value={offer.currency}
                        onValueChange={(value) => updateOffer(index, 'currency', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="CNY">Chinese Yuan (CNY)</SelectItem>
                          <SelectItem value="USD">US Dollar (USD)</SelectItem>
                          <SelectItem value="EUR">Euro (EUR)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label>{t('priceFrom')}</Label>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        value={offer.price_low}
                        onChange={(e) => updateOffer(index, 'price_low', parseFloat(e.target.value) || 0)}
                        placeholder="0.00"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>{t('priceTo')}</Label>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        value={offer.price_high || ''}
                        onChange={(e) => updateOffer(index, 'price_high', e.target.value ? parseFloat(e.target.value) : undefined)}
                        placeholder={t('optional')}
                      />
                    </div>
                    <div className="col-span-2 space-y-2">
                      <Label>{t('quantityInfo')}</Label>
                      <Input
                        value={offer.quantity_info || ''}
                        onChange={(e) => updateOffer(index, 'quantity_info', e.target.value || undefined)}
                        placeholder={t('quantityInfoPlaceholder')}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Attributes Section */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">{t('productAttributes')}</h3>
              <Button type="button" variant="outline" size="sm" onClick={addAttribute}>
                <Plus className="h-4 w-4 mr-2" />
                {t('addAttribute')}
              </Button>
            </div>

            {formData.attributes.map((attribute, index) => (
              <Card key={index}>
                <CardHeader className="flex flex-row items-center justify-between">
                  <h4 className="font-medium">{t('attribute')} {index + 1}</h4>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeAttribute(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>{t('attributeKey')}</Label>
                      <Input
                        value={attribute.original_attr_key}
                        onChange={(e) => updateAttribute(index, 'original_attr_key', e.target.value)}
                        placeholder={t('attributeKeyPlaceholder')}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>{t('attributeValue')}</Label>
                      <Input
                        value={attribute.original_attr_value}
                        onChange={(e) => updateAttribute(index, 'original_attr_value', e.target.value)}
                        placeholder={t('attributeValuePlaceholder')}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {/* Form Actions */}
      <div className="flex gap-4 pt-6 border-t">
        <Button type="submit" disabled={loading}>
          <Save className="h-4 w-4 mr-2" />
          {loading ? t('saving') : mode === 'create' ? t('createProduct') : t('updateProduct')}
        </Button>
        <Button
          type="button"
          variant="outline"
          onClick={() => router.back()}
          disabled={loading}
        >
          {t('cancel')}
        </Button>
      </div>
    </form>
  );
}
