'use client';

// components/admin/ProductMediaDisplay.tsx
// Component to display product media (images or videos) based on type

import { useState } from 'react';
import Image from 'next/image';

interface ProductMediaDisplayProps {
  src: string;
  alt: string;
  type: string;
  className?: string;
  containerClassName?: string;
}

export function ProductMediaDisplay({
  src,
  alt,
  type,
  className = "w-full h-full object-cover"
}: ProductMediaDisplayProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [errorType, setErrorType] = useState<'cors' | 'network' | 'format' | 'unknown'>('unknown');

  const handleVideoError = (e: React.SyntheticEvent<HTMLVideoElement, Event>) => {
    const video = e.currentTarget;
    setIsLoading(false);

    // Check if it's a CORS error
    if (video.error?.code === video.error?.MEDIA_ERR_SRC_NOT_SUPPORTED) {
      setErrorType('cors');
    } else if (video.error?.code === video.error?.MEDIA_ERR_NETWORK) {
      setErrorType('network');
    } else {
      setErrorType('format');
    }

    setHasError(true);
  };

  if (type === 'video') {
    return (
      <div className="relative w-full h-full">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-muted">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        )}
        {hasError ? (
          <div className="absolute inset-0 flex items-center justify-center bg-muted text-muted-foreground">
            <div className="text-center p-4">
              {errorType === 'cors' ? (
                <>
                  <p className="font-medium">External video cannot be embedded</p>
                  <p className="text-sm mt-1">This video is hosted on an external site</p>
                  <a
                    href={src}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary hover:underline text-sm mt-2 inline-block"
                  >
                    View video externally →
                  </a>
                </>
              ) : errorType === 'network' ? (
                <>
                  <p>Video failed to load</p>
                  <p className="text-sm mt-1">Network error - check your connection</p>
                </>
              ) : (
                <>
                  <p>Video format not supported</p>
                  <p className="text-sm mt-1">This video format may not be playable</p>
                </>
              )}
            </div>
          </div>
        ) : (
          <video
            src={src}
            className={className}
            controls
            preload="none"
            playsInline
            crossOrigin="anonymous"
            onLoadStart={() => setIsLoading(true)}
            onLoadedData={() => setIsLoading(false)}
            onError={handleVideoError}
          >
            Your browser does not support the video tag.
          </video>
        )}
      </div>
    );
  }

  // Default to image
  return (
    <Image
      src={src}
      alt={alt}
      fill
      className={className}
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
    />
  );
}