'use client';

// components/admin/PaymentStatusForm.tsx
// Form for updating payment status

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { updatePaymentStatus, processRefund } from '@/lib/actions/admin/payment.actions';
import { PaymentStatus } from '@/app/generated/prisma';

interface PaymentStatusFormProps {
  paymentId: string;
  locale: string;
  isRefund?: boolean;
}

export function PaymentStatusForm({ paymentId, locale, isRefund = false }: PaymentStatusFormProps) {
  const t = useTranslations('admin');
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [status, setStatus] = useState<PaymentStatus>('succeeded');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      let result;
      
      if (isRefund) {
        result = await processRefund(paymentId);
      } else {
        result = await updatePaymentStatus(paymentId, status);
      }

      if (result.success) {
        router.refresh();
      } else {
        setError(result.error || 'Failed to update payment status');
      }
    } catch (err) {
      console.error('Error updating payment:', err);
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  if (isRefund) {
    return (
      <form onSubmit={handleSubmit} className="space-y-4">
        {error && (
          <div className="p-3 text-sm text-destructive bg-destructive/10 rounded-md">
            {error}
          </div>
        )}

        <Button type="submit" disabled={loading} variant="destructive" className="w-full">
          {loading ? t('processing') : t('processRefund')}
        </Button>
      </form>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {error && (
        <div className="p-3 text-sm text-destructive bg-destructive/10 rounded-md">
          {error}
        </div>
      )}

      <div className="space-y-2">
        <Label htmlFor="status">
          {t('newStatus')}
        </Label>
        <Select
          value={status}
          onValueChange={(value) => setStatus(value as PaymentStatus)}
        >
          <SelectTrigger id="status">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="succeeded">{t('statusSucceeded')}</SelectItem>
            <SelectItem value="failed">{t('statusFailed')}</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Button type="submit" disabled={loading} className="w-full">
        {loading ? t('updating') : t('updateStatus')}
      </Button>
    </form>
  );
}

