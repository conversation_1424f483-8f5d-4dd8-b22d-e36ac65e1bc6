'use client';

// components/admin/ProductImagesManager.tsx
// Manage product images with upload, reorder, and delete functionality

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Plus, Trash2, Upload, Eye } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { deleteProductImage, updateProductImageType } from '@/lib/actions/admin/product.actions';
import type { ProductAdminDetail } from '@/lib/types/admin';
import { ProductMediaDisplay } from './ProductMediaDisplay';

interface ProductImagesManagerProps {
  productId: number;
  images: ProductAdminDetail['product_images'];
  locale: string;
}

const IMAGE_TYPES = [
  { value: 'preview', label: 'Preview' },
  { value: 'description', label: 'Description' },
  { value: 'video', label: 'Video' },
];

export function ProductImagesManager({
  productId,
  images,
  locale
}: ProductImagesManagerProps) {
  const t = useTranslations('admin');
  const router = useRouter();
  const [loading, setLoading] = useState<string | null>(null);
  const [showUploadForm, setShowUploadForm] = useState(false);
  const [uploadData, setUploadData] = useState<{
    image_url: string;
    image_type: 'preview' | 'description' | 'video';
  }>({
    image_url: '',
    image_type: 'preview',
  });

  const handleDeleteImage = async (imageId: number) => {
    if (!confirm(t('confirmDeleteImage'))) return;

    setLoading(imageId.toString());
    
    try {
      const result = await deleteProductImage(Number(imageId));
      
      if (result.success) {
        router.refresh();
      } else {
        console.error('Failed to delete image:', result.error);
      }
    } catch (error) {
      console.error('Error deleting image:', error);
    } finally {
      setLoading(null);
    }
  };

  const handleUpdateImageType = async (imageId: number, newType: string) => {
    setLoading(imageId.toString());
    
    try {
      const result = await updateProductImageType(Number(imageId), newType as 'preview' | 'description' | 'video');
      
      if (result.success) {
        router.refresh();
      } else {
        console.error('Failed to update image type:', result.error);
      }
    } catch (error) {
      console.error('Error updating image type:', error);
    } finally {
      setLoading(null);
    }
  };

  const handleAddImage = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading('upload');

    try {
      // This would typically involve uploading to a file storage service
      // For now, we'll assume the URL is already valid
      const result = await fetch('/api/admin/products/images', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          productId,
          image_url: uploadData.image_url,
          image_type: uploadData.image_type,
        }),
      });

      if (result.ok) {
        setShowUploadForm(false);
        setUploadData({ image_url: '', image_type: 'preview' });
        router.refresh();
      } else {
        console.error('Failed to add image');
      }
    } catch (error) {
      console.error('Error adding image:', error);
    } finally {
      setLoading(null);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>{t('productImages')}</CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowUploadForm(!showUploadForm)}
          >
            <Plus className="h-4 w-4 mr-2" />
            {t('addImage')}
          </Button>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Upload Form */}
          {showUploadForm && (
            <form onSubmit={handleAddImage} className="border rounded-lg p-4 space-y-4">
              <div className="space-y-2">
                <Label htmlFor="image_url">{t('imageUrl')}</Label>
                <Input
                  id="image_url"
                  value={uploadData.image_url}
                  onChange={(e) => setUploadData({ ...uploadData, image_url: e.target.value })}
                  placeholder="https://example.com/image.jpg"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="image_type">{t('imageType')}</Label>
                <Select
                  value={uploadData.image_type}
                  onValueChange={(value) => setUploadData({ ...uploadData, image_type: value as typeof uploadData.image_type })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {IMAGE_TYPES.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex gap-2">
                <Button type="submit" disabled={loading === 'upload'}>
                  <Upload className="h-4 w-4 mr-2" />
                  {loading === 'upload' ? t('uploading') : t('addImage')}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowUploadForm(false)}
                >
                  {t('cancel')}
                </Button>
              </div>
            </form>
          )}

          {/* Images Grid */}
          {images.length === 0 ? (
            <p className="text-muted-foreground text-center py-8">{t('noImages')}</p>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {images.map((image) => (
                <div key={image.id.toString()} className="border rounded-lg p-4 space-y-3">
                  <div className={`${image.image_type === 'video' ? 'aspect-video' : 'aspect-square'} rounded-lg overflow-hidden bg-muted`}>
                    <ProductMediaDisplay
                      src={image.image_url}
                      alt={`Product image ${image.id}`}
                      type={image.image_type}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Badge variant="outline">{image.image_type}</Badge>
                      <a
                        href={image.image_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary hover:underline"
                      >
                        <Eye className="h-4 w-4" />
                      </a>
                    </div>
                    
                    <Select
                      value={image.image_type}
                      onValueChange={(value) => handleUpdateImageType(image.id, value)}
                      disabled={loading === image.id.toString()}
                    >
                      <SelectTrigger className="h-8">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {IMAGE_TYPES.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    
                    <Button
                      variant="destructive"
                      size="sm"
                      className="w-full"
                      onClick={() => handleDeleteImage(image.id)}
                      disabled={loading === image.id.toString()}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      {loading === image.id.toString() ? t('deleting') : t('delete')}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
