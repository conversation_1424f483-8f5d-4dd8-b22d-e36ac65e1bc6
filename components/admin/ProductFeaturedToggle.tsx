'use client';

// components/admin/ProductFeaturedToggle.tsx
// Toggle product featured status with optimistic updates

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Star, StarOff } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { updateProductFeatured } from '@/lib/actions/admin/product.actions';

interface ProductFeaturedToggleProps {
  productId: number;
  currentFeatured: boolean;
  locale: string;
}

export function ProductFeaturedToggle({
  productId,
  currentFeatured,
  locale
}: ProductFeaturedToggleProps) {
  const t = useTranslations('admin');
  const router = useRouter();
  const [isFeatured, setIsFeatured] = useState(currentFeatured);
  const [loading, setLoading] = useState(false);

  const handleToggle = async () => {
    setLoading(true);

    // Optimistic update
    const newFeatured = !isFeatured;
    setIsFeatured(newFeatured);

    try {
      const result = await updateProductFeatured(productId, newFeatured);

      if (!result.success) {
        // Revert on error
        setIsFeatured(isFeatured);
        console.error('Failed to update featured status:', result.error);
      } else {
        // Refresh the page to show updated data
        router.refresh();
      }
    } catch (error) {
      // Revert on error
      setIsFeatured(isFeatured);
      console.error('Error updating featured status:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button
      variant={isFeatured ? 'default' : 'outline'}
      size="sm"
      onClick={handleToggle}
      disabled={loading}
    >
      {isFeatured ? (
        <>
          <Star className="h-4 w-4 mr-2 fill-current" />
          {t('featured')}
        </>
      ) : (
        <>
          <StarOff className="h-4 w-4 mr-2" />
          {t('notFeatured')}
        </>
      )}
    </Button>
  );
}