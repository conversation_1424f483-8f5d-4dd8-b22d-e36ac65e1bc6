'use client';

// components/admin/ProductVariantsManager.tsx
// Manage product variants with translations

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Plus, Edit, Trash2, Save, X } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { formatCurrency } from '@/lib/utils';
import { deleteProductVariant, updateProductVariant } from '@/lib/actions/admin/product.actions';
import type { SerializedProductVariant } from '@/lib/types/admin';

interface ProductVariantsManagerProps {
  productId: number;
  variants: SerializedProductVariant[];
  locale: string;
}

interface EditingVariant {
  id: number;
  original_variant_name: string;
  original_variant_type: string;
  available_quantity: string;
  min_quantity: string;
  price_low: string;
  price_high: string;
  currency: string;
}

export function ProductVariantsManager({
  productId,
  variants,
  locale
}: ProductVariantsManagerProps) {
  const t = useTranslations('admin');
  const router = useRouter();
  const [loading, setLoading] = useState<string | null>(null);
  const [editingVariant, setEditingVariant] = useState<EditingVariant | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newVariant, setNewVariant] = useState({
    original_variant_name: '',
    original_variant_type: '',
    available_quantity: '',
    min_quantity: '1',
    price_low: '',
    price_high: '',
    currency: 'CNY',
  });

  const handleDeleteVariant = async (variantId: number) => {
    if (!confirm(t('confirmDeleteVariant'))) return;

    setLoading(variantId.toString());
    
    try {
      const result = await deleteProductVariant(Number(variantId));
      
      if (result.success) {
        router.refresh();
      } else {
        console.error('Failed to delete variant:', result.error);
      }
    } catch (error) {
      console.error('Error deleting variant:', error);
    } finally {
      setLoading(null);
    }
  };

  const handleEditVariant = (variant: SerializedProductVariant) => {
    setEditingVariant({
      id: variant.id,
      original_variant_name: variant.original_variant_name,
      original_variant_type: variant.original_variant_type,
      available_quantity: variant.available_quantity?.toString() || '',
      min_quantity: variant.min_quantity?.toString() || '1',
      price_low: variant.price_low.toString(),
      price_high: variant.price_high?.toString() || '',
      currency: variant.currency,
    });
  };

  const handleSaveVariant = async () => {
    if (!editingVariant) return;

    setLoading(editingVariant.id.toString());
    
    try {
      const result = await updateProductVariant(Number(editingVariant.id), {
        original_variant_name: editingVariant.original_variant_name,
        original_variant_type: editingVariant.original_variant_type,
        available_quantity: editingVariant.available_quantity ? parseFloat(editingVariant.available_quantity) : null,
        min_quantity: editingVariant.min_quantity ? parseFloat(editingVariant.min_quantity) : null,
        price_low: parseFloat(editingVariant.price_low),
        price_high: editingVariant.price_high ? parseFloat(editingVariant.price_high) : null,
        currency: editingVariant.currency,
      });
      
      if (result.success) {
        setEditingVariant(null);
        router.refresh();
      } else {
        console.error('Failed to update variant:', result.error);
      }
    } catch (error) {
      console.error('Error updating variant:', error);
    } finally {
      setLoading(null);
    }
  };

  const handleAddVariant = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading('add');

    try {
      const result = await fetch('/api/admin/products/variants', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          productId,
          ...newVariant,
          available_quantity: newVariant.available_quantity ? parseFloat(newVariant.available_quantity) : null,
          min_quantity: newVariant.min_quantity ? parseFloat(newVariant.min_quantity) : null,
          price_low: parseFloat(newVariant.price_low),
          price_high: newVariant.price_high ? parseFloat(newVariant.price_high) : null,
        }),
      });

      if (result.ok) {
        setShowAddForm(false);
        setNewVariant({
          original_variant_name: '',
          original_variant_type: '',
          available_quantity: '',
          min_quantity: '1',
          price_low: '',
          price_high: '',
          currency: 'CNY',
        });
        router.refresh();
      } else {
        console.error('Failed to add variant');
      }
    } catch (error) {
      console.error('Error adding variant:', error);
    } finally {
      setLoading(null);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>{t('productVariants')}</CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAddForm(!showAddForm)}
          >
            <Plus className="h-4 w-4 mr-2" />
            {t('addVariant')}
          </Button>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Add Form */}
          {showAddForm && (
            <form onSubmit={handleAddVariant} className="border rounded-lg p-4 space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="variant_name">{t('variantName')}</Label>
                  <Input
                    id="variant_name"
                    value={newVariant.original_variant_name}
                    onChange={(e) => setNewVariant({ ...newVariant, original_variant_name: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="variant_type">{t('variantType')}</Label>
                  <Input
                    id="variant_type"
                    value={newVariant.original_variant_type}
                    onChange={(e) => setNewVariant({ ...newVariant, original_variant_type: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="price_low">{t('priceFrom')}</Label>
                  <Input
                    id="price_low"
                    type="number"
                    step="0.01"
                    value={newVariant.price_low}
                    onChange={(e) => setNewVariant({ ...newVariant, price_low: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="price_high">{t('priceTo')}</Label>
                  <Input
                    id="price_high"
                    type="number"
                    step="0.01"
                    value={newVariant.price_high}
                    onChange={(e) => setNewVariant({ ...newVariant, price_high: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="available_quantity">{t('availableQuantity')}</Label>
                  <Input
                    id="available_quantity"
                    type="number"
                    value={newVariant.available_quantity}
                    onChange={(e) => setNewVariant({ ...newVariant, available_quantity: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="min_quantity">{t('minQuantity')}</Label>
                  <Input
                    id="min_quantity"
                    type="number"
                    value={newVariant.min_quantity}
                    onChange={(e) => setNewVariant({ ...newVariant, min_quantity: e.target.value })}
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Button type="submit" disabled={loading === 'add'}>
                  <Save className="h-4 w-4 mr-2" />
                  {loading === 'add' ? t('adding') : t('addVariant')}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowAddForm(false)}
                >
                  {t('cancel')}
                </Button>
              </div>
            </form>
          )}

          {/* Variants List */}
          {variants.length === 0 ? (
            <p className="text-muted-foreground text-center py-8">{t('noVariants')}</p>
          ) : (
            <div className="space-y-4">
              {variants.map((variant) => {
                const isEditing = editingVariant?.id === variant.id;
                const variantTranslation = variant.translations.find(
                  t => t.language_code === locale
                ) || variant.translations.find(
                  t => t.language_code === 'en'
                ) || variant.translations[0];

                return (
                  <div key={variant.id.toString()} className="border rounded-lg p-4">
                    {isEditing ? (
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label>{t('variantName')}</Label>
                            <Input
                              value={editingVariant.original_variant_name}
                              onChange={(e) => setEditingVariant({ ...editingVariant, original_variant_name: e.target.value })}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label>{t('variantType')}</Label>
                            <Input
                              value={editingVariant.original_variant_type}
                              onChange={(e) => setEditingVariant({ ...editingVariant, original_variant_type: e.target.value })}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label>{t('priceFrom')}</Label>
                            <Input
                              type="number"
                              step="0.01"
                              value={editingVariant.price_low}
                              onChange={(e) => setEditingVariant({ ...editingVariant, price_low: e.target.value })}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label>{t('priceTo')}</Label>
                            <Input
                              type="number"
                              step="0.01"
                              value={editingVariant.price_high}
                              onChange={(e) => setEditingVariant({ ...editingVariant, price_high: e.target.value })}
                            />
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            onClick={handleSaveVariant}
                            disabled={loading === variant.id.toString()}
                          >
                            <Save className="h-4 w-4 mr-2" />
                            {loading === variant.id.toString() ? t('saving') : t('save')}
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setEditingVariant(null)}
                          >
                            <X className="h-4 w-4 mr-2" />
                            {t('cancel')}
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center justify-between">
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 flex-1">
                          <div>
                            <p className="text-sm text-muted-foreground">{t('variantName')}</p>
                            <p className="font-medium">
                              {variantTranslation?.variant_name || variant.original_variant_name}
                            </p>
                          </div>
                          <div>
                            <p className="text-sm text-muted-foreground">{t('variantType')}</p>
                            <p className="font-medium">
                              {variantTranslation?.variant_type || variant.original_variant_type}
                            </p>
                          </div>
                          <div>
                            <p className="text-sm text-muted-foreground">{t('price')}</p>
                            <p className="font-medium">
                              {formatCurrency(Number(variant.price_low), variant.currency)}
                              {variant.price_high && Number(variant.price_high) !== Number(variant.price_low) && (
                                <span> - {formatCurrency(Number(variant.price_high), variant.currency)}</span>
                              )}
                            </p>
                          </div>
                          <div>
                            <p className="text-sm text-muted-foreground">{t('quantity')}</p>
                            <p className="font-medium">
                              {variant.available_quantity ? Number(variant.available_quantity) : t('unlimited')}
                            </p>
                          </div>
                        </div>
                        <div className="flex gap-2 ml-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditVariant(variant)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => handleDeleteVariant(variant.id)}
                            disabled={loading === variant.id.toString()}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
