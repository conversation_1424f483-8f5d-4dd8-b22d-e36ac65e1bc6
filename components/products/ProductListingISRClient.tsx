'use client';

// components/products/ProductListingISRClient.tsx
// ISR-optimized product listing client with instant loading and progressive enhancement

import { useState, useTransition, useEffect, useMemo, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { ProductCard } from './ProductCard';
import { ProductFilters } from './ProductFilters';
import { PricingProvider, type PricingData } from '@/components/providers/PricingProvider';
import { useCurrency } from '@/components/providers/CurrencyProvider';
import type { ProductListItem, PaginationInfo, CategoryWithTranslations, ProductFiltersState } from '@/lib/types';
import { UnpaidOrdersBanner } from '../ui/unpaid-orders-banner';

interface ProductListingISRClientProps {
  initialProducts: ProductListItem[];
  initialPagination: PaginationInfo;
  categories: CategoryWithTranslations[];
  locale: string;
  initialFilters: ProductFiltersState;
}

export function ProductListingISRClient({
  initialProducts,
  initialPagination,
  categories,
  locale,
  initialFilters,
}: ProductListingISRClientProps) {
  const t = useTranslations('products');
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();
  const { currency: userCurrency } = useCurrency();

  // State for static products (loads instantly)
  const [products, setProducts] = useState<ProductListItem[]>(initialProducts);
  const [pagination, setPagination] = useState<PaginationInfo>(initialPagination);
  const [searchQuery, setSearchQuery] = useState(initialFilters.search || '');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState(initialFilters.search || '');
  
  // State for progressive enhancement
  const [personalizedPricing, setPersonalizedPricing] = useState<PricingData | null>(null);
  const [isLoadingPricing, setIsLoadingPricing] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [isSearching, setIsSearching] = useState(false);

  // Memoize products array to prevent unnecessary re-renders
  const memoizedProducts = useMemo(() => products, [products]) as ProductListItem[];

  // Debounce search query updates
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Progressive enhancement: Load personalized pricing after initial render
  useEffect(() => {
    if (products.length === 0 || !userCurrency) return;

    const loadPersonalizedPricing = async () => {
      setIsLoadingPricing(true);
      try {
        const productIds = products.map(p => p.id);
        const response = await fetch('/api/products/listing/pricing', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            productIds,
            currency: userCurrency,
            locale,
          }),
        });

        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            setPersonalizedPricing(data.pricing);
          }
        }
      } catch (error) {
        console.error('Error loading personalized pricing:', error);
      } finally {
        setIsLoadingPricing(false);
      }
    };

    // Only load pricing if currency is different from default
    if (userCurrency !== 'XAF') {
      loadPersonalizedPricing();
    }
  }, [products, userCurrency, locale]);

  // Handle filter updates with progressive enhancement
  const updateFilters = useCallback((updates: Record<string, string | number | undefined>) => {
    const params = new URLSearchParams(searchParams.toString());

    Object.entries(updates).forEach(([key, value]) => {
      if (value === undefined || value === null || value === '') {
        params.delete(key);
      } else {
        params.set(key, value.toString());
      }
    });

    // Reset to page 1 when filters change
    if (!updates.page) {
      params.delete('page');
    }

    const newSearchParams = params.toString();
    
    startTransition(() => {
      router.push(`/${locale}/products?${newSearchParams}`);
    });
  }, [searchParams, router, locale, startTransition]);

  // Handle search with API fallback for dynamic results
  const handleSearch = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!debouncedSearchQuery.trim()) {
      updateFilters({ search: undefined });
      return;
    }

    setIsSearching(true);
    
    try {
      // Try to get search results from API for immediate feedback
      const response = await fetch(`/api/products/listing/search?${new URLSearchParams({
        search: debouncedSearchQuery,
        locale,
        currency: userCurrency,
        limit: '24',
      })}`);

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setProducts(data.data);
          setPagination(data.pagination);
        }
      }
    } catch (error) {
      console.error('Error searching products:', error);
    } finally {
      setIsSearching(false);
    }

    // Also update URL for proper navigation
    updateFilters({ search: debouncedSearchQuery });
  }, [debouncedSearchQuery, updateFilters, locale, userCurrency]);

  const handleSortChange = useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    updateFilters({ sortBy: e.target.value });
  }, [updateFilters]);

  // Load more products for infinite scroll
  const loadMoreProducts = useCallback(async () => {
    if (isLoadingMore || !pagination.hasMore || !pagination.nextCursor) return;

    setIsLoadingMore(true);
    try {
      const response = await fetch(`/api/products/listing/search?${new URLSearchParams({
        cursor: pagination.nextCursor,
        locale,
        currency: userCurrency,
        limit: '24',
      })}`);

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setProducts(prev => [...prev, ...data.data]);
          setPagination(data.pagination);
        }
      }
    } catch (error) {
      console.error('Failed to load more products:', error);
    } finally {
      setIsLoadingMore(false);
    }
  }, [isLoadingMore, pagination, locale, userCurrency]);

  // Infinite scroll effect for mobile
  useEffect(() => {
    let isLoadingRef = false;
    let debounceTimer: NodeJS.Timeout;

    const handleScroll = () => {
      clearTimeout(debounceTimer);

      debounceTimer = setTimeout(() => {
        if (window.innerWidth >= 1024 || isLoadingRef || !pagination.hasMore || isLoadingMore) return;

        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const windowHeight = window.innerHeight;
        const documentHeight = document.documentElement.scrollHeight;

        if (scrollTop + windowHeight >= documentHeight - 100) {
          isLoadingRef = true;
          loadMoreProducts().finally(() => {
            isLoadingRef = false;
          });
        }
      }, 150);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(debounceTimer);
    };
  }, [pagination.hasMore, isLoadingMore, loadMoreProducts]);

  return (
    <PricingProvider pricing={personalizedPricing || null} isLoading={isLoadingPricing}>
      {/* Unpaid Orders Sticky Banner */}
      <div className="sticky md:pb-4 top-16 z-40">
        <UnpaidOrdersBanner locale={locale} />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Pricing loading indicator */}
        {isLoadingPricing && !personalizedPricing && (
          <div className="fixed bottom-4 right-4 bg-background border rounded-lg p-3 shadow-lg z-40">
            <div className="flex items-center gap-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
              <p className="text-xs text-muted-foreground">{t('loadingPrices')}</p>
            </div>
          </div>
        )}

        {/* Filters Sidebar - Desktop only */}
        <aside className="hidden lg:block lg:col-span-1">
          <div className="sticky top-20">
            <ProductFilters
              categories={categories}
              selectedCategory={initialFilters.categoryId}
              selectedPriceRange={
                initialFilters.minPrice !== undefined || initialFilters.maxPrice !== undefined
                  ? { min: initialFilters.minPrice ?? 0, max: initialFilters.maxPrice ?? null }
                  : undefined
              }
              onCategoryChange={(categoryId) =>
                updateFilters({ category: categoryId })
              }
              onPriceRangeChange={(range) =>
                updateFilters({
                  minPrice: range?.min ?? undefined,
                  maxPrice: range?.max ?? undefined,
                })
              }
              onClearFilters={() => {
                setSearchQuery('');
                setDebouncedSearchQuery('');
                router.push(`/${locale}/products`);
              }}
            />
          </div>
        </aside>

        {/* Main Content */}
        <div className="lg:col-span-3 space-y-6">
          {/* Search Bar */}
          <form onSubmit={handleSearch} className="flex gap-2">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder={t('searchPlaceholder')}
              className="flex-1 h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            />
            <Button type="submit" disabled={isSearching}>
              {isSearching ? t('searching') : t('search')}
            </Button>
          </form>

          {/* Sort and Filter - Desktop */}
          <div className="hidden lg:flex flex-row gap-4 justify-end">
            <select
              value={initialFilters.sortBy}
              onChange={handleSortChange}
              className="flex h-10 w-48 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            >
              <option value="newest">{t('sortNewest')}</option>
              <option value="price_asc">{t('sortPriceLow')}</option>
              <option value="price_desc">{t('sortPriceHigh')}</option>
              <option value="popular">{t('sortPopular')}</option>
            </select>
          </div>

          {/* Categories - Horizontal scrollable tabs on mobile */}
          <div className="lg:hidden border-b sticky top-16 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 -mx-4 px-2">
            <div className="flex gap-0 overflow-x-auto">
              <button
                onClick={() => updateFilters({ category: undefined })}
                className={`px-6 py-3 text-sm whitespace-nowrap border-b-2 transition-colors flex-shrink-0 ${
                  !initialFilters.categoryId
                    ? 'border-primary text-primary font-medium'
                    : 'border-transparent text-muted-foreground hover:text-foreground'
                }`}
              >
                {t('allCategories')}
              </button>
              {categories.map((category) => {
                const categoryName = category.translations[0]?.name || 'Category';
                return (
                  <button
                    key={category.id}
                    onClick={() => updateFilters({ category: category.id })}
                    className={`px-6 py-3 text-sm whitespace-nowrap border-b-2 transition-colors flex-shrink-0 ${
                      initialFilters.categoryId === category.id
                        ? 'border-primary text-primary font-medium'
                        : 'border-transparent text-muted-foreground hover:text-foreground'
                    }`}
                  >
                    {categoryName}
                  </button>
                );
              })}
            </div>
          </div>

          {/* Products Grid - Loads instantly with static data */}
          {isPending ? (
            <div className="text-center py-12">
              <p className="text-muted-foreground">{t('loading')}</p>
            </div>
          ) : products.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-muted-foreground">{t('noResults')}</p>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                {memoizedProducts.map((product) => (
                  <ProductCard
                    key={product.id}
                    product={product}
                    locale={locale}
                  />
                ))}
              </div>

              {/* Pagination - Desktop only */}
              <div className="hidden lg:flex items-center justify-center gap-2 mt-8">
                {pagination.hasMore && (
                  <>
                    <Button
                      variant="outline"
                      onClick={() => updateFilters({ page: 1 })}
                      disabled={!pagination.prevCursor || isPending}
                    >
                      {t('previous')}
                    </Button>

                    <span className="text-sm text-muted-foreground px-4">
                      {t('showingResults', { count: products.length })}
                    </span>

                    <Button
                      variant="outline"
                      onClick={() => updateFilters({ page: 2 })}
                      disabled={!pagination.hasMore || isPending}
                    >
                      {t('next')}
                    </Button>
                  </>
                )}
              </div>

              {/* Infinite scroll loading - Mobile only */}
              <div className="lg:hidden flex items-center justify-center mt-8">
                {isLoadingMore && (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                    <p className="text-sm text-muted-foreground">{t('loadingMore')}</p>
                  </div>
                )}
                {!pagination.hasMore && products.length > 0 && (
                  <p className="text-sm text-muted-foreground">
                    {t('showingResults', { count: products.length })}
                  </p>
                )}
              </div>
            </>
          )}
        </div>
      </div>
    </PricingProvider>
  );
}
