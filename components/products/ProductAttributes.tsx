'use client';

import { useTranslations } from 'next-intl';
import type { ProductWithDetails } from '@/lib/types';

interface ProductAttributesProps {
  attributes: ProductWithDetails['product_attributes'];
  layout?: 'grid' | 'list';
  className?: string;
}

export function ProductAttributes({
  attributes,
  layout = 'grid',
  className = ''
}: ProductAttributesProps) {
  const t = useTranslations('products');

  if (!attributes || attributes.length === 0) {
    return <p className="text-muted-foreground">{t('noSpecifications')}</p>;
  }

  const gridClass = layout === 'grid'
    ? 'grid grid-cols-1 md:grid-cols-2 gap-4'
    : 'grid grid-cols-1 gap-4';

  return (
    <div className={`${gridClass} ${className}`}>
      {attributes.map((attr, index) => {
        const attrTranslation = attr.translations[0];
        const key = attrTranslation?.attr_key || attr.original_attr_key;
        const value = attrTranslation?.attr_value || attr.original_attr_value;
        return (
          <div key={`${attr.original_attr_key}-${index}`} className="flex flex-col p-3 bg-muted/50 rounded-lg">
            <span className="text-sm text-muted-foreground font-medium">{key}</span>
            <span className="text-base font-semibold mt-1">{value}</span>
          </div>
        );
      })}
    </div>
  );
}