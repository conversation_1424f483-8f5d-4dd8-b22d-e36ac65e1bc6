// components/products/ProductPricingSkeleton.tsx
// Loading skeleton for product pricing section

'use client';

export function ProductPricingSkeleton() {
  return (
    <div className="animate-pulse">
      {/* Price skeleton */}
      <div className="h-8 bg-gray-200 rounded w-32 mb-2"></div>
      
      {/* Min order skeleton */}
      <div className="h-4 bg-gray-200 rounded w-24"></div>
    </div>
  );
}

export function WishlistButtonSkeleton() {
  return (
    <div className="animate-pulse">
      <div className="h-10 bg-gray-200 rounded w-10"></div>
    </div>
  );
}

export function AvailabilityBadgeSkeleton() {
  return (
    <div className="animate-pulse">
      <div className="h-6 bg-gray-200 rounded w-20"></div>
    </div>
  );
}
