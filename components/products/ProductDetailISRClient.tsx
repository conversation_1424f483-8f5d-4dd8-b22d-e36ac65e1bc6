// components/products/ProductDetailISRClient.tsx
// ISR-optimized product detail client with progressive enhancement

'use client';

import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import { ErrorBoundary } from "react-error-boundary";
import { useMobileDetection } from "@/hooks/use-mobile-detection";
import { useProductTracking } from "@/hooks/use-product-tracking";
import { useAddToCart } from "@/hooks/mutations/useAddToCart";
import { useImageNavigation } from "@/hooks/use-image-navigation";
import { useQuantityManagement } from "@/hooks/use-quantity-management";
import { useProductPricing } from "@/hooks/use-product-pricing";

// New ISR-specific hooks
import { useProductPricingAPI } from "@/hooks/queries/useProductPricingAPI";
import { useWishlistStatusAPI, useWishlistToggleAPI } from "@/hooks/queries/useWishlistStatusAPI";
import { useProductAvailability } from "@/hooks/queries/useProductAvailability";

import { ProductImageGallery } from "./ProductImageGallery";
import { ProductInfoSection } from "./ProductInfoSection";
import { ProductWithDetails } from "@/lib/types";

interface ProductDetailISRClientProps {
  product: ProductWithDetails; 
  locale: string;
}

export function ProductDetailISRClient({
  product: initialProduct,
  locale,
}: ProductDetailISRClientProps) {
  const t = useTranslations("products");

  // Custom hooks for state management
  const { isMobile, maskOpacity } = useMobileDetection();

  // Get the slug for API calls
  const slug = initialProduct.translations[0]?.slug || "";

  // Progressive enhancement - load personalized data
  // Only start loading after component mounts to avoid blocking SSR
  const [shouldLoadDynamicData, setShouldLoadDynamicData] = useState(false);

  useEffect(() => {
    // Start loading dynamic data after initial render
    setShouldLoadDynamicData(true);
  }, []);

  const { data: pricingData, isLoading: isLoadingPricing } = useProductPricingAPI(
    slug,
    locale,
    shouldLoadDynamicData
  );
  const { data: wishlistData, isLoading: isLoadingWishlist } = useWishlistStatusAPI(
    initialProduct.id,
    shouldLoadDynamicData
  );
  const { data: availabilityData } = useProductAvailability(
    slug,
    locale,
    shouldLoadDynamicData
  );
  
  // Wishlist mutation
  const wishlistMutation = useWishlistToggleAPI();

  // Track product view
  useProductTracking(initialProduct, {});

  // Image navigation
  const { imageIndex, direction, setImageByIndex, paginate } = useImageNavigation(initialProduct.product_images);

  // Quantity management
  const [selectedVariants, setSelectedVariants] = useState<Record<number, number>>({});
  const { quantity, handleSetQuantity } = useQuantityManagement(1, initialProduct.offers[0], initialProduct.offers[0]);

  // Cart operations
  const { mutate: addToCart, isPending: isAddingToCart } = useAddToCart();

  // Zoom state
  const [isZoomOpen, setIsZoomOpen] = useState(false);

  // Merge static product with dynamic pricing data
  const [currentProduct, setCurrentProduct] = useState(initialProduct);

  useEffect(() => {
    if (pricingData?.offers) {
      // Update product with personalized pricing
      setCurrentProduct((prev) => ({
        ...prev,
        offers: prev.offers.map((offer) => {
          const pricingOffer = pricingData.offers.find((p) => p.id === offer.id.toString());
          if (pricingOffer) {
            return {
              ...offer,
              display_price: pricingOffer.display_price,
              display_currency: pricingOffer.display_currency,
              exchange_rate: pricingOffer.exchange_rate,
            };
          }
          return offer;
        }),
      }));
    }
  }, [pricingData]);

  // Product data
  const productName = currentProduct.translations[0]?.name || currentProduct.original_name;
  const productDescription = "";

  // Images
  const galleryImages = currentProduct.product_images.map((img) => ({
    id: img.id,
    image_url: img.image_url,
    product_id: img.product_id,
    alt: `${productName} - ${img.image_type}`,
    image_type: img.image_type,
  }));

  const images = galleryImages;

  // Get categories
  const categories = currentProduct.categories;

  // Use centralized pricing logic
  const { price, currency, variantTypes } = useProductPricing({
    product: currentProduct,
    quantity: 1, // Initial quantity, will be updated by quantity hook
  });

  const lowestOffer = [...currentProduct.offers]
    .sort((a, b) => Number(a.min_quantity) - Number(b.min_quantity))
    .map((offer) => ({
      id: offer.id,
      min_quantity: Number(offer.min_quantity),
    }))[0];

  // Wishlist handlers
  const isWishlisted = wishlistData?.isWishlisted || false;
  const isWishlistLoading = isLoadingWishlist || wishlistMutation.isPending;

  const handleWishlistToggle = () => {
    if (wishlistData?.requiresAuth) {
      // Redirect to login or show auth modal
      window.location.href = `/${locale}/login?redirect=${encodeURIComponent(window.location.pathname)}`;
      return;
    }

    wishlistMutation.mutate({
      productId: initialProduct.id,
      action: isWishlisted ? 'remove' : 'add',
    });
  };

  // Cart handlers
  const handleAddToCart = (params?: {
    selectedVariants?: Record<number, number>;
    selectedImage?: number;
    isBuyNow?: boolean;
  }) => {
    const finalVariants = params?.selectedVariants || selectedVariants;
    const finalImageIndex = params?.selectedImage ?? imageIndex;

    addToCart({
      productId: currentProduct.id,
      quantity,
      variantId: Object.keys(finalVariants).length > 0 ? Object.values(finalVariants)[0] : undefined,
      imageUrl: galleryImages[finalImageIndex]?.image_url,
      isBuyNow: params?.isBuyNow || false,
    });
  };

  const handleBuyNow = (params?: {
    selectedVariants?: Record<number, number>;
    selectedImage?: number;
  }) => {
    handleAddToCart({ ...params, isBuyNow: true });
  };

  // Structured data for SEO
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Product",
    name: productName,
    description: productDescription,
    image: images.map((img) => img.image_url),
    offers: {
      "@type": "Offer",
      price: price,
      priceCurrency: currency,
      availability: availabilityData?.available ? "https://schema.org/InStock" : "https://schema.org/OutOfStock",
    },
    brand: {
      "@type": "Brand",
      name: currentProduct.marketplace,
    },
  };

  return (
    <ErrorBoundary
      fallback={<div className="text-center py-8">Something went wrong loading the product details.</div>}
      resetKeys={[initialProduct.id, locale]} // Reset when product or locale changes
    >
      <>
        {/* Structured Data for SEO */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData),
          }}
        />

        <div className="flex flex-col lg:flex-row gap-8 lg:gap-12">
          {/* Image Gallery Section */}
          <ProductImageGallery
            images={images}
            productName={productName || ""}
            isMobile={isMobile}
            maskOpacity={maskOpacity}
            imageIndex={imageIndex}
            direction={direction}
            isZoomOpen={isZoomOpen}
            setIsZoomOpen={setIsZoomOpen}
            paginate={paginate}
            setImageByIndex={setImageByIndex}
          />

          {/* Product Info Section */}
          <ProductInfoSection
            product={currentProduct}
            personalizedProduct={currentProduct}
            categories={categories}
            productName={productName || ""}
            price={price}
            currency={currency}
            lowestOffer={lowestOffer}
            isMobile={isMobile}
            selectedVariants={selectedVariants}
            setSelectedVariants={setSelectedVariants}
            quantity={quantity}
            handleSetQuantity={handleSetQuantity}
            images={images}
            imageIndex={imageIndex}
            setImageByIndex={setImageByIndex}
            variantTypes={variantTypes}
            isAdding={isAddingToCart}
            handleAddToCart={handleAddToCart}
            handleBuyNow={handleBuyNow}
            isWishlisted={isWishlisted}
            isWishlistLoading={isWishlistLoading}
            handleWishlistToggle={handleWishlistToggle}
            locale={locale}
            t={t}
            isLoadingPricing={isLoadingPricing}
            isLoadingWishlistStatus={isLoadingWishlist}
          />
        </div>
      </>
    </ErrorBoundary>
  );
}
