'use client';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { ProductAttributes } from './ProductAttributes';
import { ProductImagesGrid } from './ProductImagesGrid';
import type { ProductWithDetails } from '@/lib/types';
import { ProductImageType } from '@/app/generated/prisma';

type TranslationFunction = (key: string, options?: Record<string, string | number>) => string;

interface ProductDetailsTabsProps {
  product: ProductWithDetails;
  descriptionImages: {
    id: number;
    product_id: number;
    image_url: string;
    image_type: ProductImageType;
  }[];
  productName: string;
  isMobile: boolean;
  t: TranslationFunction;
}

export function ProductDetailsTabs({
  product,
  descriptionImages,
  productName,
  isMobile,
  t,
}: ProductDetailsTabsProps) {
  return (
    <div className="mx-2 mt-12 lg:mt-16">
      {isMobile ? (
        <div className="space-y-6">
          {/* Specifications Section */}
          <Card>
            <CardHeader>
              <CardTitle>{t('productSpecifications')}</CardTitle>
            </CardHeader>
            <CardContent>
              <ProductAttributes attributes={product.product_attributes} layout="list" />
            </CardContent>
          </Card>

          {/* Description Images Section */}
          <Card>
            <CardHeader>
              <CardTitle>{t('additionalDescriptionImages')}</CardTitle>
            </CardHeader>
            <CardContent>
              <ProductImagesGrid images={descriptionImages} productName={productName} layout="list" />
            </CardContent>
          </Card>
        </div>
      ) : (
        <Tabs defaultValue="specifications" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="specifications">{t('specifications')}</TabsTrigger>
            <TabsTrigger value="description">{t('descriptionImages')}</TabsTrigger>
          </TabsList>

          {/* Specifications Tab */}
          <TabsContent value="specifications" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>{t('productSpecifications')}</CardTitle>
              </CardHeader>
              <CardContent>
                <ProductAttributes attributes={product.product_attributes} layout="grid" />
              </CardContent>
            </Card>
          </TabsContent>

          {/* Description Images Tab */}
          <TabsContent value="description" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>{t('additionalDescriptionImages')}</CardTitle>
              </CardHeader>
              <CardContent>
                <ProductImagesGrid images={descriptionImages} productName={productName} layout="grid" />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}