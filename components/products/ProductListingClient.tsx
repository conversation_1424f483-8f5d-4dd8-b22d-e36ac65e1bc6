'use client';

// components/products/ProductListingClient.tsx
// Client component for product listing with filters

import { useState, useTransition, useEffect, useMemo, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { ProductCard } from './ProductCard';
import { ProductFilters } from './ProductFilters';
import { PricingProvider, type PricingData } from '@/components/providers/PricingProvider';
import { useCurrency } from '@/components/providers/CurrencyProvider';
import { useProducts } from '@/hooks/queries/useProducts';
import { usePrefetchProduct } from '@/hooks/queries/usePrefetchProduct';
import { useListingPricingUpdate } from '@/hooks/use-listing-pricing-update';
import type { ProductListItem, PaginationInfo, CategoryWithTranslations, ProductFiltersState } from '@/lib/types';
import { UnpaidOrdersBanner } from '../ui/unpaid-orders-banner';

interface ProductListingClientProps {
  initialProducts: ProductListItem[];
  initialPagination: PaginationInfo;
  categories: CategoryWithTranslations[];
  locale: string;
  initialFilters: ProductFiltersState;
}

export function ProductListingClient({
  initialProducts,
  initialPagination,
  categories,
  locale,
  initialFilters,
}: ProductListingClientProps) {
  const t = useTranslations('products');
  const router = useRouter();
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();
  const [isPending, startTransition] = useTransition();
  const { currency: userCurrency } = useCurrency();
  const { prefetchOnInteraction } = usePrefetchProduct();

  const [searchQuery, setSearchQuery] = useState(initialFilters.search || '');
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(initialPagination.hasMore);
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState(initialFilters.search || '');

  // Use TanStack Query for products with personalized pricing
  const {
    data: productsData,
    isLoading: isLoadingProducts,
    isFetching: isFetchingProducts,
    error: productsError,
    fetchNextPage,
    hasNextPage,
    updateFilters: prefetchFilters
  } = useProducts({
    ...initialFilters,
    search: debouncedSearchQuery || initialFilters.search
  }, locale, userCurrency, true);

  const products = productsData?.pages.flatMap(page => page.data) || initialProducts;
  const productIds = products.map(p => p.id);

  // Use separate hook for pricing updates - doesn't block initial render
  const { pricing: personalizedPricing, isUpdatingPricing } = useListingPricingUpdate({
    productIds,
    locale,
    initialPricing: null, // Start with no pricing, will be updated when currency changes
  });

  const isLoadingPricing = isUpdatingPricing;

  // Memoize products array to prevent unnecessary re-renders
  const memoizedProducts = useMemo(() => products, [products]) as ProductListItem[];

  // Batch pricing is now handled by the useProducts hook - simplified

  // Debounce search query updates
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 300); // 300ms debounce delay

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Enhanced infinite scroll with pre-loading indicators and better UX
  useEffect(() => {
    let isLoadingRef = false;
    let debounceTimer: NodeJS.Timeout;

    const handleScroll = () => {
      // Clear existing timer
      clearTimeout(debounceTimer);

      // Debounce scroll events by 150ms
      debounceTimer = setTimeout(() => {
        if (window.innerWidth >= 1024 || isLoadingRef || !hasMore || isLoadingMore) return; // Only on mobile/tablet, prevent concurrent loads

        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const windowHeight = window.innerHeight;
        const documentHeight = document.documentElement.scrollHeight;

        // Load more when user is near bottom (100px)
        if (scrollTop + windowHeight >= documentHeight - 100) {
          isLoadingRef = true;
          loadMoreProducts().finally(() => {
            isLoadingRef = false;
          });
        }
      }, 150);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(debounceTimer);
    };
  }, [hasNextPage, isLoadingMore, currentPage, initialFilters, locale, userCurrency, fetchNextPage]); // Dependencies for loadMoreProducts

  const updateFilters = useCallback((updates: Record<string, string | number | undefined>) => {
    const params = new URLSearchParams(searchParams.toString());

    Object.entries(updates).forEach(([key, value]) => {
      if (value === undefined || value === null || value === '') {
        params.delete(key);
      } else {
        params.set(key, value.toString());
      }
    });

    // Reset to page 1 when filters change
    if (!updates.page) {
      params.delete('page');
    }

    // Prefetch the new filter state for instant loading
    const newSearchParams = params.toString();
    const newFilters = {
      search: params.get('search') || undefined,
      categoryId: params.get('category') ? parseInt(params.get('category')!) : undefined,
      minPrice: params.get('minPrice') ? parseFloat(params.get('minPrice')!) : undefined,
      maxPrice: params.get('maxPrice') ? parseFloat(params.get('maxPrice')!) : undefined,
      sortBy: (params.get('sortBy') as 'newest' | 'price_asc' | 'price_desc' | 'popular') || 'newest',
      page: params.get('page') ? parseInt(params.get('page')!) : 1,
    };

    prefetchFilters(newFilters);

    startTransition(() => {
      router.push(`/${locale}/products?${newSearchParams}`);
    });
  }, [searchParams, prefetchFilters, router, locale, startTransition]);

  const handleSearch = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    updateFilters({ search: debouncedSearchQuery });
  }, [debouncedSearchQuery, updateFilters]);

  const handleSortChange = useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    updateFilters({ sortBy: e.target.value });
  }, [updateFilters]);

  const loadMoreProducts = useCallback(async () => {
    if (isLoadingMore || !hasNextPage) return;

    setIsLoadingMore(true);
    try {
      await fetchNextPage();
      setCurrentPage(prev => prev + 1);

      // Note: Pricing prefetching is now handled by the useListingPricingUpdate hook
    } catch (error) {
      console.error('Failed to load more products:', error);
    } finally {
      setIsLoadingMore(false);
    }
  }, [isLoadingMore, hasNextPage, fetchNextPage]);

  const handlePageChange = useCallback((page: number) => {
    updateFilters({ page });
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, [updateFilters]);

  // Handle errors gracefully
  if (productsError) {
    console.error('Product listing error:', productsError);
  }

  return (
    <PricingProvider pricing={personalizedPricing || null} isLoading={isLoadingPricing}>
      {/* Unpaid Orders Sticky Banner */}
      <div className="sticky md:pb-4 top-16 z-40">
        <UnpaidOrdersBanner locale={locale} />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Show error state instead of global loading overlay */}
        {productsError && (
          <div className="col-span-full flex items-center justify-center py-12">
            <div className="text-center">
              <p className="text-muted-foreground mb-4">{t('errorLoadingProducts')}</p>
              <Button
                onClick={() => window.location.reload()}
                variant="outline"
              >
                {t('retry')}
              </Button>
            </div>
          </div>
        )}

        {/* Only show pricing loading for initial load */}
        {isLoadingPricing && !personalizedPricing && (
          <div className="fixed bottom-4 right-4 bg-background border rounded-lg p-3 shadow-lg z-40">
            <div className="flex items-center gap-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
              <p className="text-xs text-muted-foreground">{t('loadingPrices')}</p>
            </div>
          </div>
        )}
      {/* Filters Sidebar - Desktop only */}
      <aside className="hidden lg:block lg:col-span-1">
        <div className="sticky top-20">
          <ProductFilters
            categories={categories}
            selectedCategory={initialFilters.categoryId}
            selectedPriceRange={
              initialFilters.minPrice !== undefined || initialFilters.maxPrice !== undefined
                ? { min: initialFilters.minPrice ?? 0, max: initialFilters.maxPrice ?? null }
                : undefined
            }
            onCategoryChange={(categoryId) =>
              updateFilters({ category: categoryId })
            }
            onPriceRangeChange={(range) =>
              updateFilters({
                minPrice: range?.min ?? undefined,
                maxPrice: range?.max ?? undefined,
              })
            }
            onClearFilters={() => {
              setSearchQuery('');
              setDebouncedSearchQuery('');
              router.push(`/${locale}/products`);
            }}
          />
        </div>
      </aside>

      {/* Main Content */}
      <div className="lg:col-span-3 space-y-6">
        {/* Sort and Filter - Desktop */}
        <div className="hidden lg:flex flex-row gap-4 justify-end">
          <select
            value={initialFilters.sortBy}
            onChange={handleSortChange}
            className="flex h-10 w-48 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          >
            <option value="newest">{t('sortNewest')}</option>
            <option value="price_asc">{t('sortPriceLow')}</option>
            <option value="price_desc">{t('sortPriceHigh')}</option>
            <option value="popular">{t('sortPopular')}</option>
          </select>
        </div>


        {/* Categories - Horizontal scrollable tabs on mobile */}
        <div className="lg:hidden border-b sticky top-16 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 -mx-4 px-2">
          <div className="flex gap-0 overflow-x-auto">
            <button
              onClick={() => updateFilters({ category: undefined })}
              className={`px-6 py-3 text-sm whitespace-nowrap border-b-2 transition-colors flex-shrink-0 ${
                !initialFilters.categoryId
                  ? 'border-primary text-primary font-medium'
                  : 'border-transparent text-muted-foreground hover:text-foreground'
              }`}
            >
              {t('allCategories')}
            </button>
            {categories.map((category) => {
              const categoryName = category.translations[0]?.name || 'Category';
              return (
                <button
                  key={category.id}
                  onClick={() => updateFilters({ category: category.id })}
                  className={`px-6 py-3 text-sm whitespace-nowrap border-b-2 transition-colors flex-shrink-0 ${
                    initialFilters.categoryId === category.id
                      ? 'border-primary text-primary font-medium'
                      : 'border-transparent text-muted-foreground hover:text-foreground'
                  }`}
                >
                  {categoryName}
                </button>
              );
            })}
          </div>
        </div>

        {/* Products Grid */}
        {isPending ? (
          <div className="text-center py-12">
            <p className="text-muted-foreground">{t('loading')}</p>
          </div>
        ) : products.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-muted-foreground">{t('noResults')}</p>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
              {memoizedProducts.map((product) => (
                <ProductCard
                  key={product.id}
                  product={product}
                  locale={locale}
                />
              ))}
            </div>

            {/* Pagination - Desktop only */}
            <div className="hidden lg:flex items-center justify-center gap-2 mt-8">
              {initialPagination.hasMore && (
                <>
                  <Button
                    variant="outline"
                    onClick={() => handlePageChange(1)}
                    disabled={!initialPagination.prevCursor || isPending}
                  >
                    {t('previous')}
                  </Button>

                  <span className="text-sm text-muted-foreground px-4">
                    {t('showingResults', { count: products.length })}
                  </span>

                  <Button
                    variant="outline"
                    onClick={() => handlePageChange(2)}
                    disabled={!initialPagination.hasMore || isPending}
                  >
                    {t('next')}
                  </Button>
                </>
              )}
            </div>

            {/* Infinite scroll loading - Mobile only */}
            <div className="lg:hidden flex items-center justify-center mt-8">
              {isLoadingMore && (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                  <p className="text-sm text-muted-foreground">{t('loadingMore')}</p>
                </div>
              )}
              {!hasMore && products.length > 0 && (
                <p className="text-sm text-muted-foreground">
                  {t('showingResults', { count: products.length })}
                </p>
              )}
            </div>
          </>
        )}
      </div>
    </div>
    </PricingProvider>
  );
}

