// components/products/OrderConstructionSheet.tsx
'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import {
  ShoppingCart,
  Check,
  Package,
  X,
  Minus,
  Plus
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Sheet, Sheet<PERSON>ontent, SheetHeader, SheetTitle } from '@/components/ui/sheet';
import { QuantitySelector } from '@/components/ui/quantity-selector';
import { formatCurrency } from '@/lib/utils';
import { MARKETPLACES } from '@/lib/constants';
import type { ProductImage, ProductWithDetails } from '@/lib/types';
import { useToast } from '@/components/providers/ToastProvider';
import { useProductPricing, OfferWithDisplay } from '@/hooks/use-product-pricing';
import { OrderConstructionForm } from './OrderConstructionForm';
import { ProductMediaDisplayClient } from './ProductMediaDisplayClient';

interface OrderConstructionSheetProps {
   product: ProductWithDetails;
   locale: string;
   isOpen: boolean;
   onOpenChange: (open: boolean) => void;
   showBuyNow?: boolean;
   onBuyNow?: (params?: { selectedVariants?: Record<number, number>; selectedImage?: number; isBuyNow?: boolean }) => void;
   isAdding?: boolean;
   quantity: number;
   onQuantityChange: (quantity: number) => void;
   selectedVariants: Record<number, number>;
   setSelectedVariants: (variants: Record<number, number>) => void;
   selectedImage: number;
   setSelectedImage: (index: number) => void;
   personalizedProduct: ProductWithDetails;
   isLoadingPricing: boolean;
   onAddToCart: (params?: { selectedVariants?: Record<number, number>; selectedImage?: number; isBuyNow?: boolean }) => Promise<void>;
}

export function OrderConstructionSheet({
   product,
   locale,
   isOpen,
   onOpenChange,
   showBuyNow = false,
   onBuyNow,
   isAdding = false,
   quantity,
   onQuantityChange,
   selectedVariants,
   setSelectedVariants,
   selectedImage,
   setSelectedImage,
   personalizedProduct,
   isLoadingPricing,
   onAddToCart
}: OrderConstructionSheetProps) {
   const t = useTranslations('products');
   const { showToast } = useToast();

   const [localIsAdding, setLocalIsAdding] = useState(false);

  // Get translations
  const translation = product.translations[0];
  const productName = translation?.name || product.original_name || 'Product';

  // Get images - separate preview/video from description images
  const galleryImages = product.product_images.filter(img => img.image_type !== 'description' && img.image_type !== 'video');
  const images = galleryImages.map((img) => ({...img, id: Number(img.id)}));
  const currentImage = images[selectedImage]?.image_url;
  const currentImageType = images[selectedImage]?.image_url;

  // Use centralized pricing logic
  const { price, currency, currentOffer, variantTypes } = useProductPricing({
    product: personalizedProduct,
    quantity
  });

  const lowestOffer = [...personalizedProduct.offers].sort((a, b) => Number(a.min_quantity) - Number(b.min_quantity))[0] as OfferWithDisplay;
  const minQuantity = Number(lowestOffer?.min_quantity || 1);
  const maxQuantity = 10000; // Reasonable maximum to prevent unrealistic orders

  // Sync quantity to minimum when sheet opens
  useEffect(() => {
    if (isOpen && quantity < minQuantity) {
      onQuantityChange(minQuantity);
    }
  }, [isOpen, quantity, minQuantity, onQuantityChange]);

  // Handle add to cart
  const handleAddToCart = async () => {
    setLocalIsAdding(true);

    try {
      await onAddToCart({
        selectedVariants,
        selectedImage,
        isBuyNow: false
      });

      setLocalIsAdding(false);
      onOpenChange(false); // Close the sheet after adding to cart
    } catch (error) {
      console.error('Error adding to cart:', error);
      const errorMessage = error instanceof Error ? error.message : t('cartError');
      showToast('error', errorMessage);
      setLocalIsAdding(false);
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent side="bottom" className="h-[85vh] py-1 px-4 overflow-y-auto rounded-t-xl">
        <SheetHeader className="text-left sticky top-0 bg-background z-10 pb-2 border-b">
          <div className="flex items-center justify-between">
            <SheetTitle className="text-lg font-normal">{t('constructOrder')}</SheetTitle>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onOpenChange(false)}
              className="h-8 w-8"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </SheetHeader>
        
        <div className="space-y-6">
          {/* Product Image and Basic Info - Always Visible */}
          <div className="sticky top-0 border rounded-2xl bg-background z-10 px-2 pt-2 pb-4 border-b">
            <div className="flex gap-4">
              <div className="relative w-24 h-24 flex-shrink-0">
                <div className={`relative ${currentImageType === 'video' ? 'aspect-video' : 'aspect-square'} overflow-hidden rounded-lg bg-muted border`}>
                  {currentImage ? (
                    <ProductMediaDisplayClient
                      src={currentImage}
                      alt={productName}
                      type='preview'
                      className="object-cover w-full h-full"
                    />
                  ) : (
                    <div className="flex h-full items-center justify-center text-muted-foreground">
                      {t('noImage')}
                    </div>
                  )}
                </div>
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-sm line-clamp-2">{productName}</h3>
                <div className="flex items-center gap-2 mt-1">
                  {product.weight && (
                    <Badge variant="outline" className="text-xs">
                      <Package className="h-3 w-3 mr-1" />
                      {Number(product.weight).toFixed(2)} {product.weight_unit || 'kg'}
                    </Badge>
                  )}
                </div>
                {(() => {
                  const currentOfferTyped = currentOffer as OfferWithDisplay;
                  const savings = lowestOffer && currentOfferTyped && currentOfferTyped.id !== lowestOffer.id
                    ? (Number(lowestOffer.display_price) - Number(currentOfferTyped.display_price)) * quantity
                    : 0;
                  const originalPrice = lowestOffer ? Number(lowestOffer.display_price) * quantity : 0;
                  const percentage = originalPrice > 0 ? Math.round((savings / originalPrice) * 100) : 0;

                  return savings > 0 ? (
                    <div className="relative">
                      <div className="flex items-center gap-2">
                        <span className="line-through text-muted-foreground text-sm">
                          {formatCurrency(originalPrice, currency)}
                        </span>
                        <div className="relative text-lg font-bold text-primary">
                          {formatCurrency(price * quantity, currency)}
                          <Badge className="absolute -top-1 -right-7 text-xs bg-red-500 text-white px-1 py-0">
                            -{percentage}%
                          </Badge>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <span className="text-lg font-bold text-primary">
                      {formatCurrency(price * quantity, currency)}
                    </span>
                  );
                })()}
                <div className="mt-2 flex items-center justify-between">
                  <div>
                    <p className="text-xs text-muted-foreground">
                      {quantity} {t('pieces')} × {formatCurrency(price, currency)} {t('each')}
                    </p>
                    {lowestOffer && Number(lowestOffer.min_quantity) > 1 && (
                      <p className="text-xs text-muted-foreground">
                        {t('minOrder')}: {lowestOffer.min_quantity.toString()} {t('pieces')}
                      </p>
                    )}
                  </div>
                  {/* Quantity Selector - Only show when no variants */}
                  {(!variantTypes || variantTypes.length === 0) && (
                    <QuantitySelector
                      quantity={quantity}
                      onChange={onQuantityChange}
                      min={minQuantity}
                      max={maxQuantity}
                      size="sm"
                    />
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Image Gallery */}
          {images.length > 1 && (
            <div className="space-y-3">
              <Label className="text-base font-semibold">
                {t('selectDesign')}
              </Label>
              <div className="flex gap-2 overflow-x-auto pb-2 mt-4">
                {images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImage(index)}
                    className={`relative ${image.image_type === 'video' ? 'aspect-video' : 'aspect-square'} w-16 h-16 overflow-hidden rounded-md border-2 flex-shrink-0 transition-colors ${
                      selectedImage === index
                        ? 'border-primary'
                        : 'border-transparent hover:border-muted-foreground'
                    }`}
                  >
                    <ProductMediaDisplayClient
                      src={image.image_url}
                      type='preview'
                      alt={`${productName} ${index + 1}`}
                      className="object-cover w-full h-full"
                    />
                  </button>
                ))}
              </div>
            </div>
          )}

          <OrderConstructionForm
            product={product}
            personalizedProduct={personalizedProduct}
            selectedVariants={selectedVariants}
            setSelectedVariants={setSelectedVariants}
            quantity={quantity}
            setQuantity={onQuantityChange}
            images={images}
            selectedImage={selectedImage}
            setSelectedImage={setSelectedImage}
            productName={productName}
            showImageGallery={false}
            t={t}
          />

          {/* Action Buttons */}
          <div className="sticky bottom-0 bg-background rounded-xl p-2">
            {showBuyNow ? (
              <div className="flex gap-3">
                <Button
                  size="lg"
                  variant="outline"
                  className="flex-1"
                  onClick={handleAddToCart}
                  disabled={localIsAdding}
                >
                  {localIsAdding ? (
                    <>
                      <Check className="mr-2 h-5 w-5" />
                      {t('addedToCart')}
                    </>
                  ) : (
                    <>
                      <ShoppingCart className="mr-2 h-5 w-5" />
                      {t('addToCart')}
                    </>
                  )}
                </Button>
                <Button
                  size="lg"
                  className="flex-1"
                  onClick={() => onBuyNow?.({
                    selectedVariants,
                    selectedImage,
                    isBuyNow: true
                  })}
                  disabled={isAdding}
                >
                  {t('buyNow')}
                </Button>
              </div>
            ) : (
              <Button
                size="lg"
                className="w-full"
                onClick={handleAddToCart}
                disabled={localIsAdding}
              >
                {localIsAdding ? (
                  <>
                    <Check className="mr-2 h-5 w-5" />
                    {t('addedToCart')}
                  </>
                ) : (
                  <>
                    <ShoppingCart className="mr-2 h-5 w-5" />
                    {t('addToCart')} - {formatCurrency(price * quantity, currency)}
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}