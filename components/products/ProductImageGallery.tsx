'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { ProductMediaDisplayClient } from './ProductMediaDisplayClient';
import { ChevronLeft, ChevronRight, ZoomIn, ShoppingCart } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface ProductImage {
  id: number;
  product_id: number;
  image_url: string;
  image_type: string;
}

interface ProductImageGalleryProps {
  images: ProductImage[];
  productName: string;
  isMobile: boolean;
  maskOpacity: number;
  imageIndex: number;
  direction: number;
  isZoomOpen: boolean;
  setIsZoomOpen: (open: boolean) => void;
  paginate: (direction: number) => void;
  setImageByIndex: (index: number) => void;
}

const swipeConfidenceThreshold = 10000;
const swipePower = (offset: number, velocity: number) => {
  return Math.abs(offset) * velocity;
};

const variants = {
  enter: (direction: number) => ({
    x: direction > 0 ? 1000 : -1000,
    opacity: 0,
  }),
  center: {
    zIndex: 1,
    x: 0,
    opacity: 1,
  },
  exit: (direction: number) => ({
    zIndex: 0,
    x: direction < 0 ? 1000 : -1000,
    opacity: 0,
  }),
};

export function ProductImageGallery({
  images,
  productName,
  isMobile,
  maskOpacity,
  imageIndex,
  direction,
  isZoomOpen,
  setIsZoomOpen,
  paginate,
  setImageByIndex,
}: ProductImageGalleryProps) {
  const currentImage = images[imageIndex]?.image_url;
  const currentImageType = images[imageIndex]?.image_type;

  return (
    <div className={`space-y-4 ${isMobile ? 'w-full' : 'lg:sticky lg:top-0 lg:h-screen lg:w-1/2'}`}>
      {/* Main Image with Zoom */}
      <div className="relative">
        <div
          className={`relative overflow-hidden ${isMobile ? 'w-full h-96 rounded-none' : `${currentImageType === 'video' ? 'aspect-video' : 'aspect-square'} rounded-lg`} bg-muted ${isMobile ? '' : 'border'}`}
        >
          <AnimatePresence initial={false} custom={direction}>
            <motion.div
              key={imageIndex}
              className="w-full h-full absolute"
              custom={direction}
              variants={variants}
              initial="enter"
              animate="center"
              exit="exit"
              transition={{
                x: { type: 'spring', stiffness: 300, damping: 30 },
                opacity: { duration: 0.2 },
              }}
              drag="x"
              dragConstraints={{ left: 0, right: 0 }}
              dragElastic={1}
              onDragEnd={(e, { offset, velocity }) => {
                const swipe = swipePower(offset.x, velocity.x);

                if (swipe < -swipeConfidenceThreshold) {
                  paginate(1);
                } else if (swipe > swipeConfidenceThreshold) {
                  paginate(-1);
                }
              }}
            >
              {currentImage ? (
                <ProductMediaDisplayClient
                  src={currentImage}
                  alt={productName}
                  type={currentImageType || 'preview'}
                  className="object-cover"
                  priority={true}
                />
              ) : (
                <div className="flex h-full items-center justify-center text-muted-foreground">
                  No image available
                </div>
              )}
            </motion.div>
          </AnimatePresence>

          {/* Image Navigation Arrows - Hidden on mobile */}
          {images.length > 1 && !isMobile && (
            <>
              <Button
                variant="secondary"
                size="icon"
                className="absolute left-2 top-1/2 -translate-y-1/2 opacity-75 hover:opacity-100 z-10"
                onClick={() => paginate(-1)}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="secondary"
                size="icon"
                className="absolute right-2 top-1/2 -translate-y-1/2 opacity-75 hover:opacity-100 z-10"
                onClick={() => paginate(1)}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </>
          )}

          {/* Zoom Button */}
          {currentImageType !== 'video' && (
            <Dialog open={isZoomOpen} onOpenChange={setIsZoomOpen}>
              <DialogTrigger asChild>
                <Button
                  variant="secondary"
                  size="icon"
                  className="absolute bottom-2 right-2 opacity-75 hover:opacity-100 z-10"
                >
                  <ZoomIn className="h-4 w-4" />
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl w-full p-0">
                <div className="relative aspect-square w-full">
                  {currentImage && (
                    <Image
                      src={currentImage}
                      alt={productName}
                      fill
                      className="object-contain"
                      sizes="100vw"
                    />
                  )}
                </div>
              </DialogContent>
            </Dialog>
          )}

          {/* Image Counter - Desktop only */}
          {images.length > 1 && !isMobile && (
            <div className="absolute bottom-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded z-10">
              {imageIndex + 1} / {images.length}
            </div>
          )}

          {/* Mobile Pagination Dots */}
          {isMobile && images.length > 1 && (
            <div className="absolute bottom-4 left-0 right-0 z-10">
              <div className="flex items-center justify-center gap-2">
                {images.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setImageByIndex(index)}
                    className={`h-2 rounded-full transition-all duration-300 ${
                      index === imageIndex ? 'w-4 bg-white/90' : 'w-2 bg-white/60'
                    }`}
                    aria-label={`Go to image ${index + 1}`}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Overlay Cart Button for Mobile */}
          {isMobile && (
            <div className="fixed top-4 right-4 z-20">
              <Button
                variant="secondary"
                size="icon"
                className="bg-white/80 backdrop-blur"
                onClick={() => window.location.href = '/cart'}
              >
                <ShoppingCart className="h-5 w-5" />
              </Button>
            </div>
          )}

          {/* Scroll Mask for Mobile */}
          {isMobile && (
            <div
              className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-white pointer-events-none z-10"
              style={{ opacity: maskOpacity }}
            />
          )}
        </div>
      </div>

      {/* Thumbnail Gallery */}
      {!isMobile && images.length > 1 && (
        <div className="flex gap-2 overflow-x-auto">
          {images.map((image, index) => (
            <button
              key={index}
              onClick={() => setImageByIndex(index)}
              className={`relative ${image.image_type === 'video' ? 'aspect-video' : 'aspect-square'} overflow-hidden rounded-md border-2 transition-colors flex-shrink-0 w-24 ${
                imageIndex === index
                  ? 'border-primary'
                  : 'border-transparent hover:border-muted-foreground'
              }`}
            >
              <ProductMediaDisplayClient
                src={image.image_url}
                alt={`${productName} ${index + 1}`}
                type={image.image_type}
                className="object-cover"
                sizes="100px"
              />
            </button>
          ))}
        </div>
      )}
    </div>
  );
}