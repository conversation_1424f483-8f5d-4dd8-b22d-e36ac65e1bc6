'use client';

// components/products/RelatedProductsClient.tsx
// Client component for related products with personalized pricing and prefetching

// import { useTranslations } from 'next-intl';
import { ProductCard } from './ProductCard';
import { PricingProvider } from '@/components/providers/PricingProvider';
import { useCurrency } from '@/components/providers/CurrencyProvider';
import { useRelatedProducts } from '@/hooks/queries/useRelatedProducts';
import { usePrefetchProduct } from '@/hooks/queries/usePrefetchProduct';
import { useBatchPricing } from '@/hooks/queries/useBatchPricing';
import type { ProductListItem } from '@/lib/types';
import type { PricingData } from '@/components/providers/PricingProvider';

interface RelatedProductsClientProps {
  products: ProductListItem[];
  locale: string;
}

export function RelatedProductsClient({ products: initialProducts, locale }: RelatedProductsClientProps) {
  // const t = useTranslations('products');
  const { currency: userCurrency } = useCurrency();

  // Get product ID from initial products for related products query
  const productId = initialProducts[0]?.id;

  // Use comprehensive related products query with prefetching
  // Only fetch if we don't have initial products (server-prefetched data)
  const { data: relatedProducts, isLoading: isLoadingRelated } = useRelatedProducts(
    productId,
    locale,
    userCurrency,
    6,
    !initialProducts.length || initialProducts.length === 0 // Only run query if no initial products
  );

  // Use related products from query if available, otherwise use initial products (server-prefetched)
  const products = (relatedProducts && relatedProducts.length > 0) ? relatedProducts : initialProducts;
  const isLoadingPricing = isLoadingRelated && !relatedProducts;

  // If we have initial products from server, don't show loading state
  const shouldShowLoading = initialProducts.length === 0 && isLoadingRelated;

  // Get product IDs for batch pricing
  const productIds = products.map(p => p.id);

  // Batch pricing for all related products to improve performance
  const { data: batchPricing } = useBatchPricing(
    productIds,
    userCurrency,
    locale,
    { enabled: productIds.length > 0 }
  );

  // Convert batch pricing to PricingProvider format
  const personalizedPricing: Record<number, PricingData> | null = batchPricing ? batchPricing : null;

  // Prefetch product details on interaction for better UX
  const { prefetchOnInteraction } = usePrefetchProduct();

  return (
    <PricingProvider pricing={personalizedPricing} isLoading={shouldShowLoading}>
      <div className="grid m-2 grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {products.map((product) => {
          // Get prefetch handlers for this product
          const prefetchHandlers = prefetchOnInteraction(
            product.translations?.[0]?.slug || '',
            locale,
            userCurrency
          );

          return (
            <div
              key={product.id}
              {...prefetchHandlers}
              className="group cursor-pointer"
            >
              <ProductCard
                product={product}
                locale={locale}
              />
            </div>
          );
        })}
      </div>
    </PricingProvider>
  );
}