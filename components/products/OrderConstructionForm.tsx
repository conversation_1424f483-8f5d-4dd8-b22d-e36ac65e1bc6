// components/products/OrderConstructionForm.tsx
'use client';

import { Label } from '@/components/ui/label';
import { Info, Check, CheckCircle } from 'lucide-react';
import { QuantitySelector } from '@/components/ui/quantity-selector';
import { formatCurrency } from '@/lib/utils';
import { useMemo } from 'react';
import type { ProductImage, ProductWithDetails } from '@/lib/types';
import { useProductPricing, OfferWithDisplay, VariantWithDisplay } from '@/hooks/use-product-pricing';
import { ProductMediaDisplayClient } from './ProductMediaDisplayClient';

interface OrderConstructionFormProps {
   product: ProductWithDetails;
   personalizedProduct: ProductWithDetails;
   selectedVariants: Record<number, number>;
   setSelectedVariants: (variants: Record<number, number>) => void;
   quantity: number;
   setQuantity: (quantity: number) => void;
   images: ProductImage[];
   selectedImage: number;
   setSelectedImage: (index: number) => void;
   productName: string;
   showImageGallery?: boolean;
   t: (key: string) => string;
}

export function OrderConstructionForm({
   product,
   personalizedProduct,
   selectedVariants,
   setSelectedVariants,
   quantity,
   setQuantity,
   images,
   selectedImage,
   setSelectedImage,
   productName,
   showImageGallery = false,
   t
}: OrderConstructionFormProps) {
  // Use centralized pricing logic for variant types
  const { variantTypes, getOfferForQuantity } = useProductPricing({
    product: personalizedProduct,
    quantity
  });

  const groupedVariants = useMemo(() => {
    const groups: Record<string, typeof personalizedProduct.variants> = {};
    personalizedProduct.variants.forEach(variant => {
      const variantTranslation = variant.translations[0];
      const type = variantTranslation?.variant_type || variant.original_variant_type;
      if (!groups[type]) groups[type] = [];
      groups[type].push(variant);
    });
    return groups;
  }, [personalizedProduct.variants]);

  return (
    <div className="space-y-6">
      {/* Image Gallery */}
      {showImageGallery && images.length > 1 && (
        <div className="space-y-3">
          <Label className="text-base font-semibold">
            {t('selectDesign')}
          </Label>
          <div className="flex gap-2 overflow-x-auto pb-2 mt-4">
            {images.map((image, index) => (
              <button
                key={index}
                onClick={() => setSelectedImage(index)}
                className={`relative ${image.image_type === 'video' ? 'aspect-video' : 'aspect-square'} w-16 h-16 overflow-hidden rounded-md border-2 flex-shrink-0 transition-all duration-200 ${
                  selectedImage === index
                    ? 'border-primary ring-2 ring-primary/20'
                    : 'border-transparent hover:border-primary/50 hover:ring-1 hover:ring-primary/10'
                }`}
              >
                <ProductMediaDisplayClient
                  src={image.image_url}
                  type='preview'
                  alt={`${productName} ${index + 1}`}
                  className="object-cover w-full h-full"
                />
                {selectedImage === index && <Check className="absolute bottom-1 right-1 h-3 w-3 bg-white rounded-full p-0.5 text-primary border border-primary/20 shadow-sm" />}
              </button>
            ))}
          </div>
        </div>
      )}
      {/* Variants Selection */}
      {personalizedProduct.variants.length > 0 && (
        <div className="space-y-3">
          <Label className="text-base font-semibold flex items-center gap-2">
            {variantTypes.length > 0 ? `${t('select')} ${variantTypes.join('/')}` : t('selectVariant')}
            <Info className="h-4 w-4 text-muted-foreground" />
          </Label>
          {Object.entries(groupedVariants).map(([type, variants]) => (
            <div key={type} className="space-y-2">
              <Label className="text-sm font-medium">{type}</Label>
              <div className="space-y-2">
                {variants.map((variant, index) => {
                  const variantTranslation = variant.translations[0];
                  const variantName =
                    variantTranslation?.variant_name || variant.original_variant_name;
                  const variantWithDisplay = variant as VariantWithDisplay;
                  const variantPrice = Number(variantWithDisplay.display_price || variant.price_low);
                  const variantCurrency = variantWithDisplay.display_currency || variant.currency;
                  const variantId = Number(variant.id || index);
                  const currentQuantity = selectedVariants[variantId] || 0;

                  const updateQuantity = (newQty: number) => {
                    const newVariants = { ...selectedVariants };
                    if (newQty <= 0) {
                      delete newVariants[variantId];
                    } else {
                      newVariants[variantId] = newQty;
                    }
                    setSelectedVariants(newVariants);
                    const totalQty = Object.values(newVariants).reduce((sum, qty) => sum + qty, 0);
                    setQuantity(totalQty);
                  };

                  return (
                    <div
                      key={variantId}
                      className="flex items-center justify-between p-3 rounded-lg border border-muted-foreground/20 hover:border-primary/50 transition-all bg-background"
                    >
                      <div className="flex flex-col">
                        <span className="font-medium text-sm">{variantName}</span>
                        <span className="text-xs text-muted-foreground">
                          {formatCurrency(variantPrice, variantCurrency)}
                        </span>
                      </div>
                      <QuantitySelector
                        quantity={currentQuantity}
                        onChange={updateQuantity}
                        min={0}
                        size="default"
                      />
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      )}


      {/* Pricing Tiers */}
      {personalizedProduct.offers.length > 1 && (
        <div className="space-y-3">
          <Label className="text-base font-semibold flex items-center gap-2">
            {t('pricingTiers')}
            <Info className="h-4 w-4 text-muted-foreground" />
          </Label>
          <div className="space-y-2">
            {personalizedProduct.offers.filter((obj, index, self) => self.findIndex(t => t.min_quantity === obj.min_quantity) === index).map((offer, index) => {
              const offerWithDisplay = offer as OfferWithDisplay;
              const offerPrice = Number(offerWithDisplay.display_price || offer.price_low);
              const offerCurrency = offerWithDisplay.display_currency || offer.currency;
              const isSelected = quantity >= Number(offer.min_quantity || 1);

              return (
                <div
                  key={index}
                  className={`relative flex justify-between items-center p-4 rounded-lg transition-all ${
                    isSelected
                      ? 'bg-gradient-to-r from-primary/15 to-primary/25 border-2 border-primary shadow-lg scale-105'
                      : 'bg-muted/30 hover:bg-muted/50 border border-transparent hover:border-primary/50 hover:shadow-md'
                  }`}
                >
                  {isSelected && <CheckCircle className="absolute top-2 right-2 h-5 w-5 text-primary" />}
                  <div className="flex flex-col">
                    <span className={`text-sm ${isSelected ? 'font-bold' : 'font-medium'}`}>{offer.quantity_info}</span>
                    {offer.min_quantity && (
                      <span className="text-xs text-muted-foreground">
                        Min: {Number(offer.min_quantity)} {t('pieces')}
                      </span>
                    )}
                  </div>
                  <span className={`text-lg font-bold ${isSelected ? 'text-primary scale-110' : 'text-primary'}`}>
                    {formatCurrency(offerPrice, offerCurrency)}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      )}

      
    </div>
  );
}