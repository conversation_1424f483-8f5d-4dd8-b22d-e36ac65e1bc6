'use client';

import { useTranslations } from 'next-intl';
import { ProductMediaDisplayClient } from './ProductMediaDisplayClient';
import type { ProductWithDetails } from '@/lib/types';

interface ProductImagesGridProps {
  images: ProductWithDetails['product_images'];
  productName: string;
  layout?: 'grid' | 'list';
  className?: string;
}

export function ProductImagesGrid({
  images,
  productName,
  layout = 'grid',
  className = ''
}: ProductImagesGridProps) {
  const t = useTranslations('products');

  if (!images || images.length === 0) {
    return <p className="text-muted-foreground">{t('noAdditionalDescriptionImages')}</p>;
  }

  const gridClass = layout === 'grid'
    ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'
    : 'grid grid-cols-1 gap-4';

  return (
    <div className={`${gridClass} ${className}`}>
      {images.map((image, index) => (
        <div key={`desc-${image.id || index}`} className="relative group">
          <div className={`relative ${image.image_type === 'video' ? 'aspect-video' : 'aspect-square'} overflow-hidden rounded-lg bg-muted border`}>
            <ProductMediaDisplayClient
              src={image.image_url}
              alt={`${productName} description ${index + 1}`}
              type={image.image_type}
              className="object-cover"
            />
          </div>
        </div>
      ))}
    </div>
  );
}