'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { OrderConstructionSheet } from './OrderConstructionSheet';
import { formatCurrency } from '@/lib/utils';
import { ShoppingCart, Heart } from 'lucide-react';
import type { ProductWithDetails, TranslationFunction } from '@/lib/types';

interface MobileOrderBarProps {
  product: ProductWithDetails;
  locale: string;
  price: number;
  currency: string;
  quantity: number;
  selectedVariants: Record<number, number>;
  setSelectedVariants: (variants: Record<number, number>) => void;
  setQuantity: (quantity: number) => void;
  selectedImage: number;
  setSelectedImage: (index: number) => void;
  personalizedProduct: ProductWithDetails;
  isLoadingPricing: boolean;
  onAddToCart: (params?: { selectedVariants?: Record<number, number>; selectedImage?: number; isBuyNow?: boolean }) => Promise<void>;
  isAdding: boolean;
  onBuyNow: (params?: { selectedVariants?: Record<number, number>; selectedImage?: number; isBuyNow?: boolean }) => void;
  isWishlisted: boolean;
  handleWishlistToggle: () => void;
  t: TranslationFunction;
  isLoadingWishlistStatus?: boolean;
}

export function MobileOrderBar({
  product,
  locale,
  price,
  currency,
  quantity,
  selectedVariants,
  setSelectedVariants,
  setQuantity,
  selectedImage,
  setSelectedImage,
  personalizedProduct,
  isLoadingPricing,
  onAddToCart,
  isAdding,
  onBuyNow,
  isWishlisted,
  handleWishlistToggle,
  t,
  isLoadingWishlistStatus = false,
}: MobileOrderBarProps) {
  const [isOrderSheetOpen, setIsOrderSheetOpen] = useState(false);

  return (
    <>
      <div className="fixed bottom-0 left-0 right-0 bg-background border-t py-1 px-2 z-40 shadow-lg">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-muted-foreground">{t('total')}</p>
            {isLoadingPricing ? (
              <div className="h-6 bg-gray-200 animate-pulse rounded w-24"></div>
            ) : (
              <p className="text-xl font-bold text-primary">{formatCurrency(price * quantity, currency)}</p>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Button
              size="lg"
              variant="outline"
              onClick={handleWishlistToggle}
              disabled={isLoadingWishlistStatus}
              className={`p-2 ${isLoadingWishlistStatus ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {isLoadingWishlistStatus ? (
                <div className="h-5 w-5 bg-gray-300 animate-pulse rounded"></div>
              ) : (
                <Heart className={`h-5 w-5 ${isWishlisted ? 'fill-current text-red-500' : ''}`} />
              )}
            </Button>
            <Button
              size="lg"
              onClick={() => setIsOrderSheetOpen(true)}
              className="flex-1"
            >
              <ShoppingCart className="mr-2 h-5 w-5" />
              {t('constructOrder')}
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile Order Construction Sheet */}
      <OrderConstructionSheet
        product={product}
        locale={locale}
        isOpen={isOrderSheetOpen}
        onOpenChange={setIsOrderSheetOpen}
        showBuyNow={true}
        onBuyNow={onBuyNow}
        isAdding={isAdding}
        quantity={quantity}
        onQuantityChange={setQuantity}
        selectedVariants={selectedVariants}
        setSelectedVariants={setSelectedVariants}
        selectedImage={selectedImage}
        setSelectedImage={setSelectedImage}
        personalizedProduct={personalizedProduct}
        isLoadingPricing={isLoadingPricing}
        onAddToCart={onAddToCart}
      />
    </>
  );
}