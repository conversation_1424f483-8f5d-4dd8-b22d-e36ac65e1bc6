// components/products/RelatedProductsISRSection.tsx
// ISR-optimized related products section with client-side loading

'use client';

import { useTranslations } from "next-intl";
import { useRelatedProductsAPI } from "@/hooks/queries/useRelatedProductsAPI";
import { RelatedProductsClient } from "./RelatedProductsClient";

interface RelatedProductsISRSectionProps {
  productSlug: string;
  locale: string;
}

export function RelatedProductsISRSection({
  productSlug,
  locale,
}: RelatedProductsISRSectionProps) {
  const t = useTranslations("products");
  
  // Load related products with personalized pricing client-side
  const { data: relatedProductsData, isLoading, error } = useRelatedProductsAPI(
    productSlug,
    locale,
    6,
    true
  );

  // Show loading skeleton
  if (isLoading) {
    return (
      <div className="mt-16">
        <h2 className="text-2xl ml-2 font-bold mb-6">{t('relatedProducts')}</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {Array.from({ length: 6 }).map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="aspect-square bg-gray-200 rounded-lg mb-2"></div>
              <div className="h-4 bg-gray-200 rounded mb-1"></div>
              <div className="h-3 bg-gray-200 rounded w-2/3"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Don't show section if there's an error or no products
  if (error || !relatedProductsData?.data || relatedProductsData.data.length === 0) {
    return null;
  }

  return (
    <div className="mt-16">
      <h2 className="text-2xl ml-2 font-bold mb-6">{t('relatedProducts')}</h2>
      <RelatedProductsClient products={relatedProductsData.data} locale={locale} />
    </div>
  );
}
