'use client';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { QuantitySelector } from '@/components/ui/quantity-selector';
import { OrderConstructionForm } from './OrderConstructionForm';
import { SocialShare } from './SocialShare';
import { CategoryBadges } from './CategoryBadges';
import { formatCurrency } from '@/lib/utils';
import { Package, ShoppingCart, Check, Heart } from 'lucide-react';
import type { ProductImage, ProductWithDetails, TranslationFunction } from '@/lib/types';

interface ProductInfoSectionProps {
  product: ProductWithDetails;
  personalizedProduct: ProductWithDetails;
  categories: ProductWithDetails['categories'];
  productName: string;
  price: number;
  currency: string;
  lowestOffer: { id: number; min_quantity: number; };
  isMobile: boolean;
  selectedVariants: Record<number, number>;
  setSelectedVariants: (variants: Record<number, number>) => void;
  quantity: number;
  handleSetQuantity: (value: number | ((prev: number) => number)) => void;
  images: ProductImage[];
  imageIndex: number;
  setImageByIndex: (index: number) => void;
  variantTypes: string[];
  isAdding: boolean;
  handleAddToCart: (params?: {
    selectedVariants?: Record<number, number>;
    selectedImage?: number;
    isBuyNow?: boolean;
  }) => void;
  handleBuyNow: (params?: {
    selectedVariants?: Record<number, number>;
    selectedImage?: number;
    isBuyNow?: boolean;
  }) => void;
  isWishlisted: boolean;
  isWishlistLoading: boolean;
  handleWishlistToggle: () => void;
  locale: string;
  t: TranslationFunction;
  isLoadingPricing?: boolean;
  isLoadingWishlistStatus?: boolean;
}

export function ProductInfoSection({
  product,
  personalizedProduct,
  categories,
  productName,
  price,
  currency,
  lowestOffer,
  isMobile,
  selectedVariants,
  setSelectedVariants,
  quantity,
  handleSetQuantity,
  images,
  imageIndex,
  setImageByIndex,
  variantTypes,
  isAdding,
  handleAddToCart,
  handleBuyNow,
  isWishlisted,
  isWishlistLoading,
  handleWishlistToggle,
  locale,
  t,
  isLoadingPricing = false,
  isLoadingWishlistStatus = false,
}: ProductInfoSectionProps) {
  return (
    <div className={`space-y-6 ${isMobile ? 'px-4' : 'lg:w-1/2 lg:overflow-y-auto'}`}>
      {/* Breadcrumb / Categories */}
      <CategoryBadges categories={categories} />

      {/* Title and Marketplace */}
      <div>
        <h1 className="text-3xl font-bold mb-3">{productName}</h1>
        <div className="flex items-center gap-3">
          {product.weight && (
            <Badge variant="outline" className="text-sm">
              <Package className="h-3 w-3 mr-1" />
              {Number(product.weight).toFixed(2)} {product.weight_unit || 'kg'}
            </Badge>
          )}
        </div>
      </div>

      {/* Price Section */}
      <div className="mt-4">
        {isLoadingPricing ? (
          <div className="h-8 bg-gray-200 animate-pulse rounded w-32"></div>
        ) : (
          <span className="text-2xl font-bold text-primary">
            {formatCurrency(price, currency)}
          </span>
        )}
        {lowestOffer && Number(lowestOffer.min_quantity) > 1 && (
          <p className="text-sm text-muted-foreground mt-1">
            {t('minOrder')}: {lowestOffer.min_quantity} {t('pieces')}
          </p>
        )}
      </div>

      {/* Desktop-only order construction */}
      {!isMobile && (
        <Card className="border-2 border-primary/10 shadow-sm">
          <CardContent className="p-6">
            <OrderConstructionForm
              product={product}
              personalizedProduct={personalizedProduct}
              selectedVariants={selectedVariants}
              setSelectedVariants={setSelectedVariants}
              quantity={quantity}
              setQuantity={handleSetQuantity}
              images={images}
              selectedImage={imageIndex}
              setSelectedImage={setImageByIndex}
              productName={productName}
              showImageGallery={true}
              t={t}
            />

            {/* Quantity Selector - Only show when no variants */}
            {(!variantTypes || variantTypes.length === 0) && (
              <div className="mt-6">
                <QuantitySelector
                  quantity={quantity}
                  onChange={handleSetQuantity}
                  min={Number(lowestOffer?.min_quantity || 1)}
                  size="sm"
                />
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-3 mt-6">
              <Button
                size="lg"
                variant="outline"
                className="flex-1"
                onClick={() => handleAddToCart({
                  selectedVariants: selectedVariants,
                  selectedImage: imageIndex,
                  isBuyNow: false
                })}
                disabled={isAdding}
              >
                {isAdding ? (
                  <>
                    <Check className="mr-2 h-5 w-5" />
                    {t('addedToCart')}
                  </>
                ) : (
                  <>
                    <ShoppingCart className="mr-2 h-5 w-5" />
                    {t('addToCart')}
                  </>
                )}
              </Button>
              <Button
                size="lg"
                className="flex-1"
                onClick={() => handleBuyNow({
                  selectedVariants: selectedVariants,
                  selectedImage: imageIndex,
                  isBuyNow: true
                })}
                disabled={isAdding}
              >
                {t('buyNow')}
              </Button>
              <Button
                size="lg"
                variant="outline"
                onClick={handleWishlistToggle}
                disabled={isWishlistLoading || isLoadingWishlistStatus}
                className={(isWishlistLoading || isLoadingWishlistStatus) ? 'opacity-50 cursor-not-allowed' : ''}
              >
                {isLoadingWishlistStatus ? (
                  <div className="h-5 w-5 bg-gray-300 animate-pulse rounded"></div>
                ) : (
                  <Heart className={`h-5 w-5 ${isWishlisted ? 'fill-current text-red-500' : ''}`} />
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Social Share */}
      <SocialShare product={product} locale={locale} price={price} currency={currency} />
    </div>
  );
}