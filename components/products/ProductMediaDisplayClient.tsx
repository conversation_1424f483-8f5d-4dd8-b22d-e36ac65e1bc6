// components/products/ProductMediaDisplayClient.tsx
'use client';

// Client component to display product media (images or videos) with Next.js optimizations

import { useState, useEffect } from 'react';
import Image from 'next/image';

interface ProductMediaDisplayClientProps {
  src: string;
  alt: string;
  type: string;
  className?: string;
  priority?: boolean;
  sizes?: string;
}

export function ProductMediaDisplayClient({
  src,
  alt,
  type,
  className = "object-cover",
  priority = false,
  sizes
}: ProductMediaDisplayClientProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [errorType, setErrorType] = useState<'cors' | 'network' | 'format' | 'forbidden' | 'unknown'>('unknown');

  // Reset state when src changes
  useEffect(() => {
    setIsLoading(true);
    setHasError(false);
    setErrorType('unknown');
  }, [src]);

  const handleVideoError = (e: React.SyntheticEvent<HTMLVideoElement, Event>) => {
    const video = e.currentTarget;
    setIsLoading(false);

    // Check if it's a CORS error
    if (video.error?.code === video.error?.MEDIA_ERR_SRC_NOT_SUPPORTED) {
      setErrorType('cors');
    } else if (video.error?.code === video.error?.MEDIA_ERR_NETWORK) {
      setErrorType('network');
    } else {
      setErrorType('format');
    }

    setHasError(true);
  };

  const handleImageError = () => {
    setIsLoading(false);
    setErrorType('forbidden'); // Assuming 403 is the most common issue with external images
    setHasError(true);
  };

  if (type === 'video') {
    return (
      <div className="relative w-full h-full">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-muted">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        )}
        {hasError ? (
          <div className="absolute inset-0 flex items-center justify-center bg-muted text-muted-foreground">
            <div className="text-center p-4">
              {errorType === 'cors' ? (
                <>
                  <p className="font-medium">External video cannot be embedded</p>
                  <p className="text-sm mt-1">This video is hosted on an external site</p>
                  <span
                    onClick={() => window.open(src, '_blank', 'noopener,noreferrer')}
                    className="text-primary hover:underline text-sm mt-2 inline-block cursor-pointer"
                  >
                    View video externally →
                  </span>
                </>
              ) : errorType === 'network' ? (
                <>
                  <p>Video failed to load</p>
                  <p className="text-sm mt-1">Network error - check your connection</p>
                </>
              ) : (
                <>
                  <p>Video format not supported</p>
                  <p className="text-sm mt-1">This video format may not be playable</p>
                </>
              )}
            </div>
          </div>
        ) : (
          <video
            key={src} // Add key to force re-render when src changes
            src={src}
            className={className}
            controls
            preload="none"
            playsInline
            crossOrigin="anonymous"
            onLoadStart={() => setIsLoading(true)}
            onLoadedData={() => setIsLoading(false)}
            onError={handleVideoError}
          >
            Your browser does not support the video tag.
          </video>
        )}
      </div>
    );
  }

  // Default to img for images
  return (
    <div className="relative w-full h-full">
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-muted">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      )}
      {hasError ? (
        <div className="absolute inset-0 flex items-center justify-center bg-muted text-muted-foreground">
          <div className="text-center p-4">
            {errorType === 'forbidden' ? (
              <>
                <p className="font-medium">Image temporarily unavailable</p>
                <p className="text-sm mt-1">This product image is hosted externally</p>
                <span
                  onClick={() => window.open(src, '_blank', 'noopener,noreferrer')}
                  className="text-primary hover:underline text-sm mt-2 inline-block cursor-pointer"
                >
                  View image externally →
                </span>
              </>
            ) : (
              <>
                <p>Image failed to load</p>
                <p className="text-sm mt-1">Please try refreshing the page</p>
              </>
            )}
          </div>
        </div>
      ) : (
        <Image
          key={src} // Add key to force re-render when src changes
          src={src}
          alt={alt}
          fill
          sizes={sizes || '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw'}
          className={className}
          onLoad={() => setIsLoading(false)}
          onError={handleImageError}
          priority={priority}
        />
      )}
    </div>
  );
}