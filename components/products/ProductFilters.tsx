'use client';

// components/products/ProductFilters.tsx
// Filter sidebar for product listing

import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { PRICE_RANGES } from '@/lib/constants';
import type { CategoryWithTranslations } from '@/lib/types';

interface ProductFiltersProps {
  categories: CategoryWithTranslations[];
  selectedCategory?: number;
  selectedPriceRange?: { min: number; max: number | null };
  onCategoryChange: (categoryId?: number) => void;
  onPriceRangeChange: (range?: { min: number; max: number | null }) => void;
  onClearFilters: () => void;
}

export function ProductFilters({
  categories,
  selectedCategory,
  selectedPriceRange,
  onCategoryChange,
  onPriceRangeChange,
  onClearFilters,
}: ProductFiltersProps) {
  const t = useTranslations('products');

  const hasActiveFilters = selectedCategory || selectedPriceRange;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold">{t('filters')}</h2>
        {hasActiveFilters && (
          <Button variant="ghost" size="sm" onClick={onClearFilters}>
            {t('clearFilters')}
          </Button>
        )}
      </div>

      <Separator />

      {/* Categories - Hidden on mobile since we have tabs */}
      <div className="hidden lg:block space-y-3">
        <Label className="text-sm font-semibold">{t('category')}</Label>
        <div className="space-y-2">
          <button
            onClick={() => onCategoryChange(undefined)}
            className={`w-full text-left text-sm px-3 py-2 rounded-md transition-colors ${
              !selectedCategory
                ? 'bg-primary text-primary-foreground'
                : 'hover:bg-muted'
            }`}
          >
            {t('allCategories')}
          </button>
          {categories.map((category) => {
            const categoryName = category.translations[0]?.name || 'Category';
            const productCount = category._count?.products || 0;

            return (
              <button
                key={category.id}
                onClick={() => onCategoryChange(category.id)}
                className={`w-full text-left text-sm px-3 py-2 rounded-md transition-colors flex items-center justify-between ${
                  selectedCategory === category.id
                    ? 'bg-primary text-primary-foreground'
                    : 'hover:bg-muted'
                }`}
              >
                <span>{categoryName}</span>
                <span className="text-xs opacity-70">({productCount})</span>
              </button>
            );
          })}
        </div>
      </div>


      {/* Price Range */}
      <div className="space-y-3">
        <Label className="text-sm font-semibold">{t('priceRange')}</Label>
        <div className="space-y-2">
          <button
            onClick={() => onPriceRangeChange(undefined)}
            className={`w-full text-left text-sm px-3 py-2 rounded-md transition-colors ${
              !selectedPriceRange
                ? 'bg-primary text-primary-foreground'
                : 'hover:bg-muted'
            }`}
          >
            All Prices
          </button>
          {PRICE_RANGES.map((range, index) => (
            <button
              key={index}
              onClick={() => onPriceRangeChange(range)}
              className={`w-full text-left text-sm px-3 py-2 rounded-md transition-colors ${
                selectedPriceRange?.min === range.min &&
                selectedPriceRange?.max === range.max
                  ? 'bg-primary text-primary-foreground'
                  : 'hover:bg-muted'
              }`}
            >
              {range.label}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}

