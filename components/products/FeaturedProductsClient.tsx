'use client';

// components/products/FeaturedProductsClient.tsx
// Client component for featured products with batch pricing

import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ProductCard } from './ProductCard';
import { PricingProvider } from '@/components/providers/PricingProvider';
import { useCurrency } from '@/components/providers/CurrencyProvider';
import { useBatchPricing } from '@/hooks/queries/useBatchPricing';
import { DEFAULT_CURRENCY } from '@/lib/constants';
import type { ProductListItem } from '@/lib/types';

interface FeaturedProductsClientProps {
  products: ProductListItem[];
  locale: string;
}

export function FeaturedProductsClient({ products, locale }: FeaturedProductsClientProps) {
  const t = useTranslations('home');
  const { currency: userCurrency } = useCurrency();

  // Use React Query for batch pricing - automatic deduplication and caching
  const { data: personalizedPricing, isLoading: isLoadingPricing } = useBatchPricing(
    products.map(p => p.id),
    userCurrency,
    locale,
    {
      enabled: products.length > 0 && userCurrency !== DEFAULT_CURRENCY,
    }
  );

  return (
    <PricingProvider pricing={personalizedPricing || null} isLoading={isLoadingPricing}>
      <section className="container mx-auto px-4">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h2 className="text-3xl font-bold">{t('featuredProducts')}</h2>
            <p className="text-muted-foreground mt-2">
              {t('featuredProductsDescription')}
            </p>
          </div>
          <Link href={`/${locale}/products`}>
            <Button variant="outline">
              {t('viewAll')}
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </Link>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {products.map((product) => (
            <ProductCard
              key={product.id}
              product={product}
              locale={locale}
            />
          ))}
        </div>
      </section>
    </PricingProvider>
  );
}