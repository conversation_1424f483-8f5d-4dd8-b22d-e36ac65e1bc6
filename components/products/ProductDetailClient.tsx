// // components/products/ProductDetailClient.tsx
// "use client";

// import { useState } from "react";
// import { useTranslations } from "next-intl";
// import { ErrorBoundary } from "react-error-boundary";
// import type { SerializedProductWithDetails } from "@/lib/types";
// import { useMobileDetection } from "@/hooks/use-mobile-detection";
// import { useProductTracking } from "@/hooks/use-product-tracking";
// import { useWishlist } from "@/hooks/use-wishlist";
// import { useAddToCart } from "@/hooks/mutations/useAddToCart";
// import { useImageNavigation } from "@/hooks/use-image-navigation";
// import { useQuantityManagement } from "@/hooks/use-quantity-management";
// import { useProductPricing } from "@/hooks/use-product-pricing";
// import { useProductPricingUpdate } from "@/hooks/use-product-pricing-update";
// import { ProductImageGallery } from "./ProductImageGallery";
// import { ProductInfoSection } from "./ProductInfoSection";
// import { ProductDetailsTabs } from "./ProductDetailsTabs";
// import { MobileOrderBar } from "./MobileOrderBar";
// import { measureAsync } from "@/lib/utils/performance-monitoring";
// import { useCartAnalytics } from "@/hooks/queries/useCartAnalytics";

// interface ProductDetailClientProps {
//   product: SerializedProductWithDetails;
//   locale: string;
// }

// export function ProductDetailClient({
//   product: initialProduct,
//   locale,
// }: ProductDetailClientProps) {
//   const t = useTranslations("products");

//   // Custom hooks for state management
//   const { isMobile, maskOpacity } = useMobileDetection();

//   // Use separate hook for pricing updates - doesn't block initial render
//   const { product, isUpdatingPricing } = useProductPricingUpdate({
//     slug: initialProduct.translations[0]?.slug || "",
//     locale,
//     initialProduct,
//   });

//   // Critical rendering data: product details (from ISR), pricing (only if currency changed), images, basic info
//   // Non-critical data: wishlist status, analytics, related products (server-prefetched)

//   // State management hooks - must be called before any early returns
//   const [selectedVariants, setSelectedVariants] = useState<
//     Record<number, number>
//   >({});

//   // Get translations and derived data
//   const translation = product.translations[0];
//   const productName = translation?.name || product.original_name || "Product";

//   // Get images - separate preview/video from description images
//   const galleryImages = product.product_images
//     .filter(
//       (img) => img.image_type !== "description" && img.image_type !== "video"
//     )
//     .map((img) => ({
//       id: Number(img.id),
//       product_id: img.product_id,
//       image_url: img.image_url,
//       image_type: img.image_type,
//       display_order: 0, // Default, assuming no display_order in schema yet
//     }));
//   const descriptionImages = product.product_images.filter(
//     (img) => img.image_type === "description"
//   );
//   const images = galleryImages;

//   // Get categories
//   const categories = product.categories;

//   // Use centralized pricing logic
//   const { price, currency, currentOffer, variantTypes } = useProductPricing({
//     product,
//     quantity: 1, // Initial quantity, will be updated by quantity hook
//   });

//   const lowestOffer = [...product.offers]
//     .sort((a, b) => Number(a.min_quantity) - Number(b.min_quantity))
//     .map((offer) => ({
//       id: offer.id,
//       min_quantity: Number(offer.min_quantity),
//     }))[0];

//   const { quantity, handleSetQuantity } = useQuantityManagement(
//     Number(product.offers[0]?.min_quantity) || 1,
//     {
//       id: Number(currentOffer.id),
//       min_quantity: Number(
//         currentOffer.min_quantity || lowestOffer.min_quantity || 1
//       ),
//     },
//     {
//       id: Number(lowestOffer.id),
//       min_quantity: Number(lowestOffer.min_quantity || 1),
//     }
//   );

//   // Image navigation
//   const {
//     imageIndex,
//     direction,
//     isZoomOpen,
//     setIsZoomOpen,
//     paginate,
//     setImageByIndex,
//   } = useImageNavigation(images);

//   // Product tracking - non-blocking analytics
//   useProductTracking(product, selectedVariants);

//   // Cart analytics for better UX insights - non-blocking
//   const { trackCartOperation } = useCartAnalytics();


//   // Background refresh for product data to keep it fresh (completed)

//   // Performance monitoring for product page load (completed)

//   // Non-rendering requests that don't block display:
//   // - Product tracking (analytics)
//   // - Cart analytics
//   // - Wishlist status (handled separately)
//   // - Related products (loaded separately in RelatedProductsClient)

//   // Wishlist management - non-blocking for initial render
//   const { isWishlisted, isWishlistLoading, handleWishlistToggle } = useWishlist(
//     product,
//     selectedVariants,
//     t
//   );

//   // Individual loading states for better UX
//   const isLoadingWishlistStatus = isWishlistLoading;
//   const isLoadingProductPricing = isUpdatingPricing;

//   // For ISR pages, we show content immediately - individual components handle their own loading states

//   // TanStack Query mutations for cart operations with optimistic updates
//   const { mutateAsync: addToCartMutation, isPending: isAddingToCart } =
//     useAddToCart();

//   // Enhanced cart operations with TanStack Query, optimistic updates, and performance monitoring
//   const handleAddToCart = async () => {
//     // Validate variant selection
//     if (variantTypes && variantTypes.length > 0) {
//       if (Object.keys(selectedVariants).length === 0) {
//         // Toast error will be handled by mutation
//         return;
//       }
//     }

//     // Get selected image URL
//     const selectedImageUrl = images[imageIndex]?.image_url;

//     // Track cart operation for analytics
//     trackCartOperation("add_item", {
//       productId: product.id,
//       quantity,
//     });

//     try {
//       // Measure performance of cart addition
//       await measureAsync("cart_add_operation", async () => {
//         if (variantTypes && variantTypes.length > 0) {
//           // Add each selected variant to cart with batch operation for better UX
//           const addPromises = Object.entries(selectedVariants).map(
//             ([variantId, qty]) =>
//               addToCartMutation({
//                 productId: product.id,
//                 variantId: Number(variantId),
//                 quantity: qty,
//                 imageUrl: selectedImageUrl,
//               })
//           );
//           await Promise.all(addPromises);
//         } else {
//           // No variants required, add product directly
//           await addToCartMutation({
//             productId: product.id,
//             variantId: undefined,
//             quantity,
//             imageUrl: selectedImageUrl,
//           });
//         }
//       });
//     } catch (error) {
//       console.error("Error adding to cart:", error);
//       // Error handling is done by the mutation hook
//     }
//   };

//   // Buy now functionality - for now keep simple, can enhance later
//   const handleBuyNow = async () => {
//     await handleAddToCart();
//     // Navigate to checkout - this could be enhanced with a proper buy now flow
//   };

//   // Error fallback component for product detail errors
//   const ProductErrorFallback = ({
//     error,
//     resetErrorBoundary,
//   }: {
//     error: Error;
//     resetErrorBoundary: () => void;
//   }) => (
//     <div className="flex flex-col items-center justify-center min-h-[400px] p-8 text-center">
//       <h2 className="text-2xl font-semibold text-destructive mb-4">
//         {t("errorLoadingProduct")}
//       </h2>
//       <p className="text-muted-foreground mb-6 max-w-md">
//         {error.message || t("somethingWentWrong")}
//       </p>
//       <button
//         onClick={resetErrorBoundary}
//         className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
//       >
//         {t("tryAgain")}
//       </button>
//     </div>
//   );

//   // Generate structured data for SEO
//   const structuredData = {
//     "@context": "https://schema.org",
//     "@type": "Product",
//     name: productName,
//     description: product.product_attributes
//       .map((attr) => {
//         const attrTranslation = attr.translations[0];
//         const key = attrTranslation?.attr_key || attr.original_attr_key;
//         const value = attrTranslation?.attr_value || attr.original_attr_value;
//         return `${key}: ${value}`;
//       })
//       .join(", "),
//     image: product.product_images.map((img) => img.image_url),
//     offers: {
//       "@type": "Offer",
//       price: price.toString(),
//       priceCurrency: currency,
//       availability: "https://schema.org/InStock",
//       seller: {
//         "@type": "Organization",
//         name:
//           product.product_attributes.find(
//             (attr) =>
//               attr.translations[0].attr_key in
//               ["Brand", "Marque", "العلامة التجارية"]
//           )?.translations[0].attr_value || "Unknown Brand",
//       },
//       priceValidUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
//         .toISOString()
//         .split("T")[0],
//     },
//     category: categories
//       .map((cat) => {
//         const categoryTranslation = cat.category.translations[0];
//         return categoryTranslation?.name || "Category";
//       })
//       .join(", "),
//     brand: {
//       "@type": "Brand",
//       name:
//         product.product_attributes.find(
//           (attr) =>
//             attr.translations[0].attr_key in
//             ["Brand", "Marque", "العلامة التجارية"]
//         )?.translations[0].attr_value || "Unknown Brand",
//     },
//   };

//   return (
//     <ErrorBoundary
//       FallbackComponent={ProductErrorFallback}
//       onError={(error, errorInfo) => {
//         console.error("Product detail error:", error, errorInfo);
//         // Could send to analytics service here
//       }}
//       onReset={() => {
//         // Reset any local state that might be causing issues
//         window.location.reload(); // Simple reset for now
//       }}
//       resetKeys={[initialProduct.id, locale]} // Reset when product or locale changes
//     >
//       <>
//         {/* Structured Data for SEO */}
//         <script
//           type="application/ld+json"
//           dangerouslySetInnerHTML={{
//             __html: JSON.stringify(structuredData),
//           }}
//         />

//         <div className="flex flex-col lg:flex-row gap-8 lg:gap-12">
//           {/* Image Gallery Section */}
//           <ProductImageGallery
//             images={images}
//             productName={productName}
//             isMobile={isMobile}
//             maskOpacity={maskOpacity}
//             imageIndex={imageIndex}
//             direction={direction}
//             isZoomOpen={isZoomOpen}
//             setIsZoomOpen={setIsZoomOpen}
//             paginate={paginate}
//             setImageByIndex={setImageByIndex}
//           />

//           {/* Product Info Section */}
//           <ProductInfoSection
//             product={product}
//             personalizedProduct={product}
//             categories={categories}
//             productName={productName}
//             price={price}
//             currency={currency}
//             lowestOffer={lowestOffer}
//             isMobile={isMobile}
//             selectedVariants={selectedVariants}
//             setSelectedVariants={setSelectedVariants}
//             quantity={quantity}
//             handleSetQuantity={handleSetQuantity}
//             images={images}
//             imageIndex={imageIndex}
//             setImageByIndex={setImageByIndex}
//             variantTypes={variantTypes}
//             isAdding={isAddingToCart}
//             handleAddToCart={handleAddToCart}
//             handleBuyNow={handleBuyNow}
//             isWishlisted={isWishlisted}
//             isWishlistLoading={isWishlistLoading}
//             handleWishlistToggle={handleWishlistToggle}
//             locale={locale}
//             t={t}
//             isLoadingPricing={isLoadingProductPricing}
//             isLoadingWishlistStatus={isLoadingWishlistStatus}
//           />
//         </div>

//         {/* Product Details Tabs */}
//         <ProductDetailsTabs
//           product={product}
//           descriptionImages={descriptionImages}
//           productName={productName}
//           isMobile={isMobile}
//           t={t}
//         />

//         {/* Mobile Order Bar */}
//         {isMobile && (
//           <MobileOrderBar
//             product={product}
//             locale={locale}
//             price={price}
//             currency={currency}
//             quantity={quantity}
//             selectedVariants={selectedVariants}
//             setSelectedVariants={setSelectedVariants}
//             setQuantity={(value) => handleSetQuantity(value)}
//             selectedImage={imageIndex}
//             setSelectedImage={setImageByIndex}
//             personalizedProduct={product}
//             isLoadingPricing={isLoadingProductPricing}
//             onAddToCart={handleAddToCart}
//             isAdding={isAddingToCart}
//             onBuyNow={handleBuyNow}
//             isWishlisted={isWishlisted}
//             handleWishlistToggle={handleWishlistToggle}
//             t={t}
//             isLoadingWishlistStatus={isLoadingWishlistStatus}
//           />
//         )}
//       </>
//     </ErrorBoundary>
//   );
// }
