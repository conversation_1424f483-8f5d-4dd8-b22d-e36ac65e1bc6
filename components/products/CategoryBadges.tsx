'use client';

import { useTranslations } from 'next-intl';
import { Badge } from '@/components/ui/badge';
import type { ProductWithDetails } from '@/lib/types';

interface CategoryBadgesProps {
  categories: ProductWithDetails['categories'];
  className?: string;
}

export function CategoryBadges({ categories, className = '' }: CategoryBadgesProps) {
  const t = useTranslations('products');

  if (!categories || categories.length === 0) {
    return null;
  }

  return (
    <div className={`flex flex-wrap gap-2 ${className}`}>
      {categories.map((cat) => {
        const categoryTranslation = cat.category.translations[0];
        return (
          <Badge key={cat.category_id} variant="secondary">
            {categoryTranslation?.name || t('categoryFallback')}
          </Badge>
        );
      })}
    </div>
  );
}