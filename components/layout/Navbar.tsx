'use client';

// components/layout/Navbar.tsx
// Professional navigation bar with modern design, search, and mobile menu

import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import {
  ShoppingCart,
  User,
  Search,
  Sun,
  Moon,
  Store,
  Package,
  LogOut,
  Settings,
  ChevronLeft,
  Home,
  ShoppingBag,
  UserCircle,
  Grid3X3,
  ChevronDown
} from 'lucide-react';
import { useTheme } from 'next-themes';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuLabel,
} from '@/components/ui/dropdown-menu';
import { usePrefetchCart } from '@/hooks/queries/usePrefetchCart';
import { useAuth } from '@/components/providers/FirebaseAuthProvider';
import { CurrencySwitcher } from '@/components/ui/currency-switcher';
import { LoadingLink } from '@/components/ui/loading-link';
import { checkAdminAccess, validateSession } from '@/lib/actions/auth.actions';
import { useUserProfile, useNavbarPrefetch, useNavbarAnalytics } from '@/hooks/queries/useNavbarData';
import { useRefreshUserProfile, useUpdateThemePreference, useUpdateCurrencyPreference } from '@/hooks/mutations/useNavbarMutations';
import { useCartCount } from '@/hooks/use-cart-count';
import { useAdminCache } from '@/hooks/use-admin-cache';
import { useState, useEffect, useRef, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

export function Navbar() {
  const t = useTranslations('nav');
  const tCommon = useTranslations('common');
  const pathname = usePathname();
  const router = useRouter();
  const { theme, setTheme } = useTheme();
  const { user, refreshSession } = useAuth();
  const [mounted, setMounted] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [hasAdminAccess, setHasAdminAccess] = useState(false);
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const isCheckingAdminAccess = useRef(false);

  // Use new SWR and TanStack Query hooks for improved UX
  const { data: userProfile, error: userProfileError } = useUserProfile();
  // const { prefetchCartSummary } = useNavbarPrefetch();
  const { trackSearchInteraction, trackNavigationClick, trackCartInteraction } = useNavbarAnalytics();
  // const refreshUserProfile = useRefreshUserProfile();
  const updateTheme = useUpdateThemePreference();
  // const updateCurrency = useUpdateCurrencyPreference();

  // Instant cart count from localStorage
  const { count: cartCount, resetCount } = useCartCount();

  // Instant admin status from localStorage
  const { getCachedAdminStatus, updateAdminCache, clearAdminCache } = useAdminCache();
  const cachedIsAdmin = getCachedAdminStatus(user?.uid);

  // Prefetch cart data on hover/interaction for better UX
  const { prefetchOnInteraction } = usePrefetchCart();
  const cartPrefetchHandlers = prefetchOnInteraction();

  // Get locale from pathname
  const locale = pathname.split('/')[1] || 'en';

  useEffect(() => {
    setMounted(true);
  }, []);

  // Focus search input when search becomes active
  useEffect(() => {
    if (isSearchActive && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isSearchActive]);

  // Check admin access with session synchronization and caching
  const checkAdminAccessWithSync = useCallback(async () => {
    if (!user || isCheckingAdminAccess.current) {
      return;
    }

    isCheckingAdminAccess.current = true;

    try {
      // First attempt to check admin access
      const hasAccess = await checkAdminAccess();

      // Update both state and cache
      setHasAdminAccess(hasAccess);
      updateAdminCache(hasAccess, user.uid);

      if (hasAccess) {
        return;
      }

      // If access check failed, try refreshing the session
      console.log('Admin access check failed, attempting session refresh...');
      const refreshSuccess = await refreshSession();

      if (refreshSuccess) {
        // Try checking admin access again after refresh
        const hasAccessAfterRefresh = await checkAdminAccess();
        setHasAdminAccess(hasAccessAfterRefresh);
        updateAdminCache(hasAccessAfterRefresh, user.uid);

        if (hasAccessAfterRefresh) {
          console.log('Admin access restored after session refresh');
        }
      } else {
        console.error('Session refresh failed');
        setHasAdminAccess(false);
        updateAdminCache(false, user.uid);
      }
    } catch (error) {
      console.error('Error checking admin access:', error);
      setHasAdminAccess(false);
      updateAdminCache(false, user.uid);
    } finally {
      isCheckingAdminAccess.current = false;
    }
  }, [user, updateAdminCache]);

  // Global session synchronization for all authenticated users
  const ensureSessionSync = async () => {
    if (!user) return;

    try {
      // Check if session is still valid (doesn't require specific permissions)
      const isSessionValid = await validateSession();

      if (isSessionValid) {
        // Session is still active, no action needed
        return;
      }

      // Session might be expired, try refreshing
      console.log('Session expired, attempting refresh...');
      const refreshSuccess = await refreshSession();

      if (refreshSuccess) {
        console.log('Session refreshed successfully');
        // Re-check admin access after refresh (in case user was upgraded)
        checkAdminAccessWithSync();
      } else {
        console.error('Session refresh failed - user may need to log in again');
      }
    } catch (error) {
      console.error('Error during session sync:', error);
      // Try refreshing anyway in case of network/server errors
      try {
        const refreshSuccess = await refreshSession();
        if (refreshSuccess) {
          console.log('Session recovered after error');
          checkAdminAccessWithSync();
        }
      } catch (refreshError) {
        console.error('Session recovery failed:', refreshError);
      }
    }
  };

  // Check admin access when user changes - now with improved caching
  useEffect(() => {
    if (user && !userProfile) {
      checkAdminAccessWithSync();
    }
  }, [user, userProfile, checkAdminAccessWithSync]);

  // Force recheck admin access when navigating to different pages (in case roles were updated)
  // Debounce this effect to prevent multiple calls during navigation
  useEffect(() => {
    if (user) {
      const timeoutId = setTimeout(() => {
        checkAdminAccessWithSync();
      }, 100); // Small delay to debounce

      return () => clearTimeout(timeoutId);
    }
  }, [pathname, user, checkAdminAccessWithSync]);

  // Periodic session synchronization for all authenticated users (every 45 minutes)
  useEffect(() => {
    if (!user) return;

    // Initial session check after 5 minutes of being logged in
    const initialCheckTimeout = setTimeout(() => {
      ensureSessionSync();
    }, 5 * 60 * 1000); // 5 minutes

    // Periodic checks every 45 minutes
    const syncInterval = setInterval(() => {
      ensureSessionSync();
    }, 45 * 60 * 1000); // 45 minutes

    return () => {
      clearTimeout(initialCheckTimeout);
      clearInterval(syncInterval);
    };
  }, [user]);

  // Handle search with analytics tracking
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      trackSearchInteraction(searchQuery.trim(), 'submit');
      router.push(`/${locale}/products?search=${encodeURIComponent(searchQuery.trim())}`);
      setSearchQuery('');
      setIsSearchActive(false);
    }
  };

  const handleLogout = async () => {
    try {
      await fetch('/api/auth/session', { method: 'DELETE' });
      // Reset cart count and admin cache on logout
      resetCount();
      clearAdminCache();
      window.location.href = `/${locale}/login`;
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // Determine context based on current path
  const getNavigationContext = () => {
    if (pathname.startsWith(`/${locale}/checkout`) || pathname.startsWith(`/${locale}/cart`)) {
      return 'checkout';
    }
    if (pathname.startsWith(`/${locale}/admin`)) {
      return 'admin';
    }
    if (pathname.startsWith(`/${locale}/account`)) {
      return 'account';
    }
    if (pathname.startsWith(`/${locale}/products`)) {
      return 'products';
    }
    if (pathname.startsWith(`/${locale}/auth`) || pathname === `/${locale}/login` || pathname === `/${locale}/register`) {
      return 'auth';
    }
    return 'marketing';
  };

  const context = getNavigationContext();

  // Context-aware navigation items
  const getNavigationItems = () => {
    switch (context) {
      case 'checkout':
        return [
          { name: t('home'), href: `/${locale}`, icon: Home },
          { name: t('products'), href: `/${locale}/products`, icon: ShoppingBag },
        ];
      case 'admin':
        return [
          { name: 'Dashboard', href: `/${locale}/admin`, icon: Grid3X3 },
          { name: 'Products', href: `/${locale}/admin/products`, icon: Package },
          { name: 'Orders', href: `/${locale}/admin/orders`, icon: Package },
          { name: 'Customers', href: `/${locale}/admin/customers`, icon: User },
        ];
      case 'account':
        return [
          { name: t('home'), href: `/${locale}`, icon: Home },
          { name: t('products'), href: `/${locale}/products`, icon: ShoppingBag },
          { name: 'Orders', href: `/${locale}/account/orders`, icon: Package },
          { name: 'Profile', href: `/${locale}/account/profile`, icon: UserCircle },
        ];
      case 'products':
        return [
          { name: t('home'), href: `/${locale}`, icon: Home },
          { name: t('products'), href: `/${locale}/products`, icon: ShoppingBag },
        ];
      case 'auth':
        return [
          { name: t('home'), href: `/${locale}`, icon: Home },
        ];
      default: // marketing
        return [
          { name: t('home'), href: `/${locale}`, icon: Home },
          { name: t('products'), href: `/${locale}/products`, icon: ShoppingBag },
        ];
    }
  };

  // Context-aware user menu items
  const getUserMenuItems = () => {
    switch (context) {
      case 'admin':
        return [
          { name: 'Dashboard', href: `/${locale}/admin`, icon: Grid3X3 },
          { name: 'Profile', href: `/${locale}/account/profile`, icon: UserCircle },
          { name: 'Settings', href: `/${locale}/account`, icon: Settings },
        ];
      case 'checkout':
        return [
          { name: 'Orders', href: `/${locale}/account/orders`, icon: Package },
          { name: 'Profile', href: `/${locale}/account/profile`, icon: UserCircle },
          { name: 'Settings', href: `/${locale}/account`, icon: Settings },
        ];
      default:
        return [
          { name: t('orders'), href: `/${locale}/account/orders`, icon: Package },
          { name: t('profile'), href: `/${locale}/account/profile`, icon: UserCircle },
          { name: t('settings'), href: `/${locale}/account`, icon: Settings },
        ];
    }
  };

  const navigation = getNavigationItems();
  const userMenuItems = getUserMenuItems();

  // Determine which elements to show based on context
  const shouldShowSearch = context !== 'auth' && context !== 'checkout';
  const shouldShowCart = context !== 'admin' && context !== 'auth';
  const shouldShowCurrencySwitcher = context !== 'admin';
  const shouldShowThemeToggle = context !== 'checkout';

  // Use cached admin access from multiple sources (cache first, then SWR, then state)
  const hasAdminAccessCached = cachedIsAdmin || userProfile?.hasAdminAccess || hasAdminAccess;

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 shadow-sm">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <LoadingLink href={`/${locale}`} className="flex items-center space-x-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary text-primary-foreground">
              <Store className="h-6 w-6" />
            </div>
            <div className="hidden lg:block">
              <h1 className="text-xl font-bold text-foreground">MaoMao</h1>
              <p className="text-xs text-muted-foreground">Marketplace</p>
            </div>
          </LoadingLink>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            {navigation.map((item) => {
              const Icon = item.icon;
              const isActive = pathname === item.href;
              return (
                <LoadingLink
                  key={item.href}
                  href={item.href}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    isActive
                      ? 'bg-primary text-primary-foreground'
                      : 'text-muted-foreground hover:text-foreground hover:bg-accent'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{item.name}</span>
                </LoadingLink>
              );
            })}
          </nav>

          {/* Search Bar - Desktop */}
          {shouldShowSearch && (
            <div className="hidden md:flex flex-1 max-w-sm mx-8">
              <form onSubmit={handleSearch} className="w-full" suppressHydrationWarning={true}>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder={tCommon('searchProducts')}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 pr-4 py-2 w-full"
                    suppressHydrationWarning={true}
                  />
                </div>
              </form>
            </div>
          )}

          {/* Right Side Actions - Desktop */}
          <div className="hidden sm:flex items-center space-x-2">
            {/* Currency Switcher */}
            {shouldShowCurrencySwitcher && (
              <div className="hidden sm:block">
                <CurrencySwitcher />
              </div>
            )}

            {/* Theme Toggle */}
            {shouldShowThemeToggle && mounted && (
              <Button
                variant="ghost"
                size="icon"
                onClick={() => {
                  const newTheme = theme === 'dark' ? 'light' : 'dark';
                  setTheme(newTheme);
                  updateTheme.mutate(newTheme);
                }}
                className="hidden sm:flex"
              >
                {theme === 'dark' ? (
                  <Sun className="h-5 w-5" />
                ) : (
                  <Moon className="h-5 w-5" />
                )}
              </Button>
            )}

            {/* Cart */}
            {shouldShowCart && (
              <Link
                href={`/${locale}/cart`}
                {...cartPrefetchHandlers}
                onClick={() => trackCartInteraction('click')}
                onMouseEnter={() => trackCartInteraction('hover')}
              >
                <Button variant="ghost" size="icon" className="relative">
                  <ShoppingCart className="h-5 w-5" />
                  {cartCount > 0 && (
                    <Badge
                      variant="destructive"
                      className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs font-semibold min-w-[20px] rounded-full"
                    >
                      {cartCount > 99 ? '99+' : cartCount}
                    </Badge>
                  )}
                </Button>
              </Link>
            )}

            {/* User Menu - Desktop */}
            {user ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="flex items-center space-x-2 pl-2">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={user.photoURL || ''} alt={user.displayName || 'User'} />
                      <AvatarFallback>
                        {user.displayName?.charAt(0) || user.email?.charAt(0) || 'U'}
                      </AvatarFallback>
                    </Avatar>
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuLabel>
                    <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium">{user.displayName || user.email?.split('@')[0] || 'User'}</p>
                      <p className="text-xs text-muted-foreground">{user.email}</p>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {userMenuItems.map((item) => {
                    const Icon = item.icon;
                    return (
                      <DropdownMenuItem key={item.href} asChild>
                        <Link
                          href={item.href}
                          className="flex items-center space-x-2"
                          onClick={() => trackNavigationClick(item.href, 'user_menu')}
                        >
                          <Icon className="h-4 w-4" />
                          <span>{item.name}</span>
                        </Link>
                      </DropdownMenuItem>
                    );
                  })}
                  {hasAdminAccessCached && (
                    <>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem asChild>
                        <Link
                          href={`/${locale}/admin`}
                          onClick={() => {
                            trackNavigationClick(`/${locale}/admin`, 'admin_access');
                            // Server validation happens in middleware
                          }}
                        >
                          <Settings className="h-4 w-4 mr-2" />
                          Admin Dashboard
                        </Link>
                      </DropdownMenuItem>
                    </>
                  )}
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleLogout} className="text-red-600">
                    <LogOut className="h-4 w-4 mr-2" />
                    <span>{t('logout')}</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <div className="hidden sm:flex items-center space-x-2">
                <Link href={`/${locale}/login`}>
                  <Button variant="ghost" size="sm">
                    {t('login')}
                  </Button>
                </Link>
                <Link href={`/${locale}/register`}>
                  <Button size="sm">
                    {t('register')}
                  </Button>
                </Link>
              </div>
            )}
          </div>

          {/* Mobile Actions */}
          <div className="flex items-center lg:hidden">
            {/* Cart - Mobile */}
            {shouldShowCart && (
              <Link
                href={`/${locale}/cart`}
                {...cartPrefetchHandlers}
                onClick={() => trackCartInteraction('click')}
                onMouseEnter={() => trackCartInteraction('hover')}
                className="relative mr-2"
              >
                <Button variant="ghost" size="icon" className="relative">
                  <ShoppingCart className="h-5 w-5" />
                  {cartCount > 0 && (
                    <Badge
                      variant="destructive"
                      className="absolute -top-1 -right-1 h-4 w-4 flex items-center justify-center p-0 text-[10px] font-semibold min-w-[16px] rounded-full"
                    >
                      {cartCount > 9 ? '9+' : cartCount}
                    </Badge>
                  )}
                </Button>
              </Link>
            )}
            {/* Mobile Search Toggle */}
            {shouldShowSearch && (
              <Button
                variant="ghost"
                size="icon"
                onClick={() => {
                  setIsSearchActive(!isSearchActive);
                  trackSearchInteraction(searchQuery, isSearchActive ? 'clear' : 'start');
                }}
                className={cn(
                  "transition-all duration-300",
                  isSearchActive ? "opacity-0 scale-0" : "opacity-100 scale-100"
                )}
              >
                <Search className="h-5 w-5" />
              </Button>
            )}

            {/* Mobile Search Bar */}
            <div
              className={cn(
                "absolute left-0 right-0 top-0 h-16 bg-background border-b flex items-center px-4 transition-all duration-300 z-10",
                isSearchActive ? "opacity-100 translate-x-0" : "opacity-0 -translate-x-full"
              )}
            >
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsSearchActive(false)}
                className="mr-2"
              >
                <ChevronLeft className="h-5 w-5" />
              </Button>
              <form onSubmit={handleSearch} className="flex-1" suppressHydrationWarning={true}>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    ref={searchInputRef}
                    type="search"
                    placeholder={tCommon('searchProducts')}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 pr-4 py-2 w-full"
                    suppressHydrationWarning={true}
                  />
                </div>
              </form>
            </div>

            {/* Cart - Mobile */}
            {shouldShowCart && (
              <Link
                href={`/${locale}/cart`}
                {...cartPrefetchHandlers}
                className={cn(
                  "transition-all duration-300",
                  isSearchActive ? "opacity-0 scale-0" : "opacity-100 scale-100"
                )}
                onClick={() => trackCartInteraction('click')}
              >
                <Button variant="ghost" size="icon" className="relative">
                  <ShoppingCart className="h-5 w-5" />
                </Button>
              </Link>
            )}

            {/* User Profile - Mobile (Always Visible) */}
            <div className={cn(
              "transition-all duration-300",
              isSearchActive ? "opacity-0 scale-0" : "opacity-100 scale-100"
            )}>
              {user ? (
                <DropdownMenu open={userMenuOpen} onOpenChange={setUserMenuOpen}>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="relative h-10 w-10 rounded-full">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={user.photoURL || ''} alt={user.displayName || 'User'} />
                        <AvatarFallback>
                          {user.displayName?.charAt(0) || user.email?.charAt(0) || 'U'}
                        </AvatarFallback>
                      </Avatar>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56">
                    <DropdownMenuLabel>
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium">{user.displayName || user.email?.split('@')[0] || 'User'}</p>
                        <p className="text-xs text-muted-foreground">{user.email}</p>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    {userMenuItems.map((item) => {
                      const Icon = item.icon;
                      return (
                        <DropdownMenuItem key={item.href} asChild>
                          <Link href={item.href} className="flex items-center space-x-2">
                            <Icon className="h-4 w-4" />
                            <span>{item.name}</span>
                          </Link>
                        </DropdownMenuItem>
                      );
                    })}
                    {hasAdminAccess && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem asChild>
                          <Link href={`/${locale}/admin`}>
                            <Settings className="h-4 w-4 mr-2" />
                            Admin Dashboard
                          </Link>
                        </DropdownMenuItem>
                      </>
                    )}
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handleLogout} className="text-red-600">
                      <LogOut className="h-4 w-4 mr-2" />
                      <span>{t('logout')}</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                <Link href={`/${locale}/login`}>
                  <Button variant="ghost" size="icon" className="relative h-10 w-10 rounded-full">
                    <User className="h-5 w-5" />
                  </Button>
                </Link>
              )}
            </div>

          </div>
        </div>
      </div>
    </header>
  );
}