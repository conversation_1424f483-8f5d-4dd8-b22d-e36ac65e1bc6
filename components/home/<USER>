'use client';

// components/home/<USER>
// Client component with enhanced UX using SWR + TanStack Query

import Link from 'next/link';
import { ArrowRight, Package, Truck, Shield, Globe, RefreshCw } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { FeaturedProductsClient } from '@/components/products/FeaturedProductsClient';
import React from 'react';
import { usePrefetchHome } from '@/hooks/use-prefetch-home';
import { useHomePricingUpdate } from '@/hooks/use-home-pricing-update';
import { UnpaidOrdersBanner } from '@/components/ui/unpaid-orders-banner';
import type { ProductListItem, CategoryWithTranslations } from '@/lib/types';
import { useUnpaidOrdersContext } from '../providers/UnpaidOrdersProvider';

interface HomePageContentProps {
  featuredProducts: ProductListItem[];
  categories: CategoryWithTranslations[];
  locale: string;
}

export function HomePageContent({
  featuredProducts: initialFeaturedProducts,
  categories: initialCategories,
  locale,
}: HomePageContentProps) {
  const t = useTranslations('home');
  const prefetch = usePrefetchHome();

  // Use separate hook for pricing updates - doesn't block initial render
  const { featuredProducts, isUpdatingPricing } = useHomePricingUpdate({
    locale,
    initialFeaturedProducts,
  });

  // Categories are static and don't need pricing updates
  const categories = initialCategories;

  // Prefetch categories on mount
  React.useEffect(() => {
    prefetch.prefetchCategories(locale);
  }, [locale, prefetch]);

  return (
    <div>
      {/* Unpaid Orders Sticky Banner */}
      <div className="sticky top-16 z-40">
        <UnpaidOrdersBanner locale={locale} />
      </div>
      <div className="space-y-16">
      {/* Hero Section */}
      <section className={`relative bg-gradient-to-br from-primary/10 via-primary/5 to-background py-20`}>
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center space-y-6">
            <h1 className="text-4xl md:text-6xl font-bold tracking-tight">
              {t('heroTitle')}
            </h1>
            <p className="text-xl text-muted-foreground">
              {t('heroSubtitle')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href={`/${locale}/products`}>
                <Button size="lg" className="w-full sm:w-auto">
                  {t('shopNow')}
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="pt-6 text-center space-y-2">
              <div className="mx-auto w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                <Package className="h-6 w-6 text-primary" />
              </div>
              <h3 className="font-semibold">{t('feature1Title')}</h3>
              <p className="text-sm text-muted-foreground">
                {t('feature1Description')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6 text-center space-y-2">
              <div className="mx-auto w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                <Truck className="h-6 w-6 text-primary" />
              </div>
              <h3 className="font-semibold">{t('feature2Title')}</h3>
              <p className="text-sm text-muted-foreground">
                {t('feature2Description')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6 text-center space-y-2">
              <div className="mx-auto w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                <Shield className="h-6 w-6 text-primary" />
              </div>
              <h3 className="font-semibold">{t('feature3Title')}</h3>
              <p className="text-sm text-muted-foreground">
                {t('feature3Description')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6 text-center space-y-2">
              <div className="mx-auto w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                <Globe className="h-6 w-6 text-primary" />
              </div>
              <h3 className="font-semibold">{t('feature4Title')}</h3>
              <p className="text-sm text-muted-foreground">
                {t('feature4Description')}
              </p>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Featured Products with enhanced UX */}
      {featuredProducts.length > 0 && (
        <FeaturedProductsClient
          products={featuredProducts}
          locale={locale}
        />
      )}

      {/* Categories with prefetching */}
      {categories.length > 0 && (
        <section className="container mx-auto px-4">
          <div className="mb-8">
            <h2 className="text-3xl font-bold">{t('shopByCategory')}</h2>
            <p className="text-muted-foreground mt-2">
              {t('shopByCategoryDescription')}
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
            {categories.slice(0, 10).map((category) => {
              const categoryTranslation = category.translations[0];
              const categoryName = categoryTranslation?.name || 'Category';
              const productCount = category._count?.products || 0;

              // Prefetch category page on interaction
              const prefetchHandlers = prefetch.prefetchCategoryOnInteraction(
                category.id,
                locale,
                'USD' // Use default currency for prefetching
              );

              return (
                <Link
                  key={category.id}
                  href={`/${locale}/products?category=${category.id}`}
                  {...prefetchHandlers}
                >
                  <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                    <CardContent className="p-6 text-center space-y-2">
                      <h3 className="font-semibold">{categoryName}</h3>
                      <p className="text-sm text-muted-foreground">
                        {productCount} {t('products')}
                      </p>
                    </CardContent>
                  </Card>
                </Link>
              );
            })}
          </div>
        </section>
      )}

      {/* Loading overlay for pricing updates */}
      {isUpdatingPricing && (
        <div className="fixed bottom-4 right-4 z-50">
          <div className="bg-primary/10 border border-primary/20 rounded-lg p-3 shadow-lg">
            <div className="flex items-center gap-2">
              <RefreshCw className="h-4 w-4 animate-spin text-primary" />
              <p className="text-xs text-muted-foreground">Updating prices...</p>
            </div>
          </div>
        </div>
      )}
    </div>
    </div>
  );
}