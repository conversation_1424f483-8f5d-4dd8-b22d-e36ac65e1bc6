'use client';

import React, { createContext, useContext, useEffect, useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { DEFAULT_CURRENCY } from '@/lib/constants';

interface CurrencyContextType {
  currency: string;
  setCurrency: (currency: string) => void;
  isLoading: boolean;
}

const CurrencyContext = createContext<CurrencyContextType | undefined>(undefined);

interface CurrencyProviderProps {
  children: React.ReactNode;
  defaultCurrency?: string;
  initialCurrency?: string; // From server-side detection
}

export function CurrencyProvider({ children, defaultCurrency = DEFAULT_CURRENCY, initialCurrency }: CurrencyProviderProps) {
  const [currency, setCurrencyState] = useState(initialCurrency || defaultCurrency);
  const [isLoading, setIsLoading] = useState(false); // Start with false since we have initialCurrency from server
  const router = useRouter();
  const initializationAttempted = useRef(false);

  useEffect(() => {
    // Skip if we already have a currency from server-side detection
    if (initialCurrency) {
      setIsLoading(false);
      return;
    }

    // Skip if already attempted initialization
    if (initializationAttempted.current) {
      return;
    }

    initializationAttempted.current = true;

    const initializeCurrency = async () => {
      try {
        // 1. Check localStorage first (fastest)
        const stored = localStorage.getItem('preferred_currency');
        if (stored && stored.length === 3) {
          setCurrencyState(stored);
          setIsLoading(false);
          return;
        }

        // 2. Use default currency - no more geolocation
        setCurrencyState(DEFAULT_CURRENCY);
        localStorage.setItem('preferred_currency', DEFAULT_CURRENCY);
        setIsLoading(false);
      } catch (error) {
        console.error('Error initializing currency:', error);
        setCurrencyState(DEFAULT_CURRENCY);
        setIsLoading(false);
      }
    };

    initializeCurrency();
  }, [initialCurrency]);

  const setCurrency = async (newCurrency: string) => {
    setCurrencyState(newCurrency);
    localStorage.setItem('preferred_currency', newCurrency);

    // Set HTTP-only cookie for server-side reading (max-age: 1 year)
    document.cookie = `preferred_currency=${newCurrency}; path=/; max-age=31536000; SameSite=Lax`;

    // Update user preference in database (non-blocking, fire and forget)
    // Don't await this - let it happen in the background
    fetch('/api/user/currency', {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ currency: newCurrency }),
    }).catch(error => {
      console.error('Error updating user currency:', error);
    });

    // Refresh the page to update all prices
    router.refresh();
  };


  return (
    <CurrencyContext.Provider value={{ currency, setCurrency, isLoading }}>
      {children}
    </CurrencyContext.Provider>
  );
}

export function useCurrency() {
  const context = useContext(CurrencyContext);
  if (context === undefined) {
    throw new Error('useCurrency must be used within a CurrencyProvider');
  }
  return context;
}

// Helper function to detect currency from browser locale (no API call, instant)
function getCurrencyFromLocale(): string {
  if (typeof window === 'undefined') return DEFAULT_CURRENCY;

  const locale = navigator.language || 'en-US';
  const currencyMap: Record<string, string> = {
    'en-US': 'USD',
    'en-GB': 'GBP',
    'en-EU': 'EUR',
    'ja-JP': 'JPY',
    'zh-CN': 'CNY',
    'fr-FR': 'EUR',
    'de-DE': 'EUR',
    'es-ES': 'EUR',
    'it-IT': 'EUR',
    // Add more mappings as needed
  };

  // Check exact match first
  if (currencyMap[locale]) {
    return currencyMap[locale];
  }

  // Check language prefix
  const language = locale.split('-')[0];
  const languageMap: Record<string, string> = {
    'en': 'USD',
    'fr': 'EUR',
    'de': 'EUR',
    'es': 'EUR',
    'it': 'EUR',
    'ja': 'JPY',
    'zh': 'CNY',
  };

  return languageMap[language] || DEFAULT_CURRENCY;
}