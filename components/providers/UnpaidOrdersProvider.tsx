// components/providers/UnpaidOrdersProvider.tsx
// Context provider for global unpaid orders state management

'use client';

import { createContext, useContext, ReactNode } from 'react';
import { useUnpaidOrders } from '@/hooks/queries/useUnpaidOrders';
import { UnpaidOrdersContextType } from '@/lib/types/unpaid-orders';

const UnpaidOrdersContext = createContext<UnpaidOrdersContextType | undefined>(undefined);

interface UnpaidOrdersProviderProps {
  children: ReactNode;
}

/**
 * Provider component for global unpaid orders state
 * Wraps the entire app to provide unpaid orders count to all components
 */
export function UnpaidOrdersProvider({ children }: UnpaidOrdersProviderProps) {
  const unpaidOrdersData = useUnpaidOrders();

  return (
    <UnpaidOrdersContext.Provider value={unpaidOrdersData}>
      {children}
    </UnpaidOrdersContext.Provider>
  );
}

/**
 * Hook to access unpaid orders context
 * Must be used within UnpaidOrdersProvider
 */
export function useUnpaidOrdersContext(): UnpaidOrdersContextType {
  const context = useContext(UnpaidOrdersContext);
  if (context === undefined) {
    throw new Error('useUnpaidOrdersContext must be used within an UnpaidOrdersProvider');
  }
  return context;
}