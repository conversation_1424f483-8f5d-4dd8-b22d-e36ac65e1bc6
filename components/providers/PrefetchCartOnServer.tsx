// components/providers/PrefetchCartOnServer.tsx
// Server-side cart prefetching component

import { getCart } from '@/lib/actions/cart.actions';
import { dehydrate } from '@tanstack/react-query';
import { QueryClient } from '@tanstack/react-query';
import { HydrationBoundary } from '@tanstack/react-query';

interface PrefetchCartOnServerProps {
  children: React.ReactNode;
}

export async function PrefetchCartOnServer({ children }: PrefetchCartOnServerProps) {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 60 * 1000, // 1 minute
      },
    },
  });

  // Prefetch cart data on server
  await queryClient.prefetchQuery({
    queryKey: ['cart'],
    queryFn: getCart,
  });

  const dehydratedState = dehydrate(queryClient);

  return (
    <HydrationBoundary state={dehydratedState}>
      {children}
    </HydrationBoundary>
  );
}