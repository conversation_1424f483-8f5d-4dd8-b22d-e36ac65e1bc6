'use client';

// components/providers/index.tsx
// Compose all providers together with caching layers

import { ThemeProvider } from './ThemeProvider';
import { FirebaseAuthProvider } from './FirebaseAuthProvider';
import { CurrencyProvider } from './CurrencyProvider';
import { LoadingProvider } from './LoadingProvider';
import { ToastProvider } from './ToastProvider';
import { QueryProvider } from './QueryProvider';
import { SWRProvider } from './SWRProvider';
import { ErrorBoundaryProvider } from './ErrorBoundaryProvider';
import { UnpaidOrdersProvider } from './UnpaidOrdersProvider';

interface ProvidersProps {
  children: React.ReactNode;
  initialCurrency?: string;
}

export function Providers({ children, initialCurrency }: ProvidersProps) {
  return (
    <ErrorBoundaryProvider>
      <QueryProvider>
        <SWRProvider>
          <ThemeProvider>
            <FirebaseAuthProvider>
              <CurrencyProvider initialCurrency={initialCurrency}>
                <LoadingProvider>
                  <ToastProvider>
                    <UnpaidOrdersProvider>
                      {children}
                    </UnpaidOrdersProvider>
                  </ToastProvider>
                </LoadingProvider>
              </CurrencyProvider>
            </FirebaseAuthProvider>
          </ThemeProvider>
        </SWRProvider>
      </QueryProvider>
    </ErrorBoundaryProvider>
  );
}
