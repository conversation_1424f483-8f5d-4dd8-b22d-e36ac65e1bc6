'use client';

import { SWRConfig } from 'swr';

// Custom fetcher function
const fetcher = async (url: string) => {
  const res = await fetch(url);

  if (!res.ok) {
    const error = new Error('An error occurred while fetching the data.');
    // Attach extra info to the error object.
    const errorWithInfo = error as Error & { info?: unknown; status?: number };
    try {
      errorWithInfo.info = await res.json();
    } catch {
      // If JSON parsing fails, just set status
    }
    errorWithInfo.status = res.status;
    throw errorWithInfo;
  }

  return res.json();
};

interface SWRProviderProps {
  children: React.ReactNode;
}

export function SWRProvider({ children }: SWRProviderProps) {
  return (
    <SWRConfig
      value={{
        fetcher,
        revalidateOnFocus: false,
        revalidateOnReconnect: false,
        dedupingInterval: 5000, // 5 seconds
        errorRetryCount: 3,
        errorRetryInterval: 1000, // 1 second
        shouldRetryOnError: (error) => {
          // Don't retry on 4xx errors
          if (error.status && error.status >= 400 && error.status < 500) {
            return false;
          }
          return true;
        },
        onError: (error, key) => {
          console.error(`SWR Error for ${key}:`, error);
        },
      }}
    >
      {children}
    </SWRConfig>
  );
}