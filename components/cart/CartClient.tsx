'use client';

// components/cart/CartClient.tsx
// Shopping cart page client component

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { Skeleton } from '@/components/ui/skeleton';
import { Trash2, ShoppingBag } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { QuantitySelector } from '@/components/ui/quantity-selector';
import { useCartStore } from '@/hooks/use-cart-store';
import { useRemoveFromCart } from '@/hooks/mutations/useRemoveFromCart';
import { useUpdateCartQuantity } from '@/hooks/mutations/useUpdateCartQuantity';
import { useCartCount } from '@/hooks/use-cart-count';
import { formatCurrency } from '@/lib/utils';
import { DEFAULT_CURRENCY } from '@/lib/constants';
import { motion, AnimatePresence } from 'framer-motion';
import { ProductMediaDisplayClient } from '../products/ProductMediaDisplayClient';
import type { CartItem } from '@/lib/types';

interface CartClientProps {
  locale: string;
}

interface CartItemComponentProps {
  item: CartItem;
  index: number;
  isMobile: boolean;
  locale: string;
  t: ReturnType<typeof useTranslations>;
  onQuantityChange: (productId: number, quantity: number, variantId?: number) => void;
  onRemove: (productId: number, variantId?: number) => void;
}

function CartItemComponent({ item, index, isMobile, locale, t, onQuantityChange, onRemove }: CartItemComponentProps) {
  const [localQuantity, setLocalQuantity] = useState(item.quantity);

  // Update local quantity when item quantity changes (from server refresh)
  useEffect(() => {
    setLocalQuantity(item.quantity);
  }, [item.quantity]);

  const handleQuantityChange = (newQuantity: number) => {
    setLocalQuantity(newQuantity);
    onQuantityChange(item.productId, newQuantity, item.variantId);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, x: -100, scale: 0.95 }}
      transition={{ duration: 0.3, delay: index * 0.1 }}
      layout
      drag={false}
      className="relative overflow-hidden"
    >
      <Card className={`shadow-lg ${isMobile ? '' : 'hover:shadow-xl'} transition-shadow m-4 duration-300 border-0 bg-gradient-to-r from-background to-background/95 relative`}>
        <CardContent className="p-6">
          {/* Remove Button - Top Right */}
          <motion.div
            className="absolute top-4 right-2"
            whileHover={{ scale: isMobile ? 1 : 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onRemove(item.productId, item.variantId)}
              className="h-8 w-8 p-0 text-destructive hover:text-destructive hover:bg-destructive/10"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </motion.div>
          <div className="flex gap-4">
            {/* Image */}
            <motion.div
              className="relative w-20 h-20 md:w-24 md:h-24 flex-shrink-0 rounded-xl overflow-hidden bg-muted shadow-sm"
              whileHover={isMobile ? {} : { scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              {item.imageUrl ? (
                <ProductMediaDisplayClient
                  src={item.imageUrl}
                  alt={item.productName}
                  className="object-cover"
                  sizes="96px"
                  type='preview'
                />
              ) : (
                <div className="flex h-full items-center justify-center text-xs text-muted-foreground">
                  {t('noImage')}
                </div>
              )}
            </motion.div>

            {/* Details */}
            <div className="flex-1 space-y-3">
              <div>
                  <Link
                    href={`/${locale}/products/${item.productSlug}`}
                    className="font-semibold hover:text-primary transition-colors text-base md:text-lg line-clamp-2"
                  >
                    {item.productName}
                  </Link>
                 {item.variantId && (
                   <p className="text-sm text-muted-foreground">
                     {item.variantName || t('variantNotSpecified')}
                   </p>
                 )}
              </div>

             <div className="flex items-center justify-between">
               {/* Quantity */}
               <div className="flex items-center gap-3">
                 <label className="text-sm text-muted-foreground hidden sm:block">
                   {t('quantity')}:
                 </label>
                 <QuantitySelector
                   quantity={localQuantity}
                   onChange={handleQuantityChange}
                   min={1}
                   size="default"
                 />
               </div>

               {/* Price */}
               <div className="text-right">
                 <motion.p
                   className="font-bold text-lg"
                   key={item.price * localQuantity}
                   initial={{ scale: 0.9, opacity: 0 }}
                   animate={{ scale: 1, opacity: 1 }}
                 >
                   {formatCurrency(item.price * localQuantity, item.currency)}
                 </motion.p>
                 <p className="text-xs text-muted-foreground">
                   {formatCurrency(item.price, item.currency)} {t('each')}
                 </p>
               </div>
             </div>

             </div>
           </div>

        </CardContent>
      </Card>
    </motion.div>
  );
}

function CartSkeleton() {
  return (
    <motion.div
      className="space-y-4"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header Skeleton */}
      <div>
        <Skeleton className="h-9 w-48 mb-2" />
        <Skeleton className="h-5 w-32" />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        {/* Cart Items Skeleton */}
        <div className="lg:col-span-2 space-y-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <motion.div
              key={i}
              initial={{ opacity: 0, y: 20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.3, delay: i * 0.1 }}
            >
              <Card className="shadow-lg border-0 bg-gradient-to-r from-background to-background/95">
                <CardContent className="p-6">
                  <div className="flex gap-4">
                    {/* Image Skeleton */}
                    <Skeleton className="relative w-20 h-20 md:w-24 md:h-24 flex-shrink-0 rounded-xl" />

                    {/* Details Skeleton */}
                    <div className="flex-1 space-y-3">
                      <div>
                        <Skeleton className="h-5 w-3/4" />
                        <Skeleton className="h-4 w-1/2" />
                      </div>

                      <div className="flex items-center justify-between">
                        {/* Quantity Skeleton */}
                        <div className="flex items-center gap-3">
                          <Skeleton className="h-8 w-20" />
                        </div>

                        {/* Price Skeleton */}
                        <div className="text-right">
                          <Skeleton className="h-6 w-16" />
                          <Skeleton className="h-4 w-12" />
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Order Summary Skeleton */}
        <div className="lg:col-span-1">
          <Card className="sticky top-20 shadow-lg border-0 bg-gradient-to-br from-background to-muted/20">
            <CardHeader>
              <Skeleton className="h-6 w-24" />
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-4 w-12" />
                </div>
              </div>

              <Skeleton className="h-16 w-full rounded-xl" />
            </CardContent>
            <CardFooter>
              <Skeleton className="h-12 w-full" />
            </CardFooter>
          </Card>
        </div>
      </div>
    </motion.div>
  );
}

export function CartClient({ locale }: CartClientProps) {
    const t = useTranslations('cart');
    // const tCommon = useTranslations('common');

    const { data, error, initialize, removeItem, updateQuantity, isInitialized } = useCartStore();
    const items = data?.items || [];
    const totals = data?.totals;

    // Sync cart count with localStorage on cart page load
    const { syncWithServer, resetCount } = useCartCount();

    const [mounted, setMounted] = useState(false);
    const [isMobile, setIsMobile] = useState(false);

    // React Query mutations for server sync
    const { mutate: removeFromCart } = useRemoveFromCart();
    const { mutate: updateCartQuantity } = useUpdateCartQuantity();

    // Debounce server sync for quantity changes
    const [quantityUpdateQueue, setQuantityUpdateQueue] = useState<Map<string, number>>(new Map());

    // Debounced server sync function
    useEffect(() => {
      if (quantityUpdateQueue.size === 0) return;

      const timeoutId = setTimeout(() => {
        const updates = Array.from(quantityUpdateQueue.entries());
        updates.forEach(([key, quantity]) => {
          const [productId, variantId] = key.split('-');
          // Update Zustand store for optimistic update
          updateQuantity(Number(productId), quantity, variantId ? Number(variantId) : undefined);
          // Sync with server via React Query mutation
          updateCartQuantity({
            productId: Number(productId),
            quantity,
            variantId: variantId ? Number(variantId) : undefined,
          });
        });
        setQuantityUpdateQueue(new Map());
      }, 500); // 500ms debounce

      return () => clearTimeout(timeoutId);
    }, [quantityUpdateQueue, updateQuantity, updateCartQuantity]);

    useEffect(() => {
        setMounted(true);
        // Initialize cart on component mount
        initialize();

        // Set mobile state
        const checkMobile = () => setIsMobile(window.innerWidth < 768);
        checkMobile();
        window.addEventListener('resize', checkMobile);
        return () => window.removeEventListener('resize', checkMobile);
    }, [initialize]);

    // Sync cart count with server data when cart is loaded
    useEffect(() => {
        if (isInitialized && data?.itemCount !== undefined) {
            syncWithServer(data.itemCount);
        }
    }, [isInitialized, data?.itemCount, syncWithServer]);

    // Reset cart count to 0 if cart is cleared
    useEffect(() => {
        if (isInitialized && data?.itemCount === 0) {
            resetCount();
        }
    }, [isInitialized, data?.itemCount, resetCount]);

    if (!mounted) {
      return <CartSkeleton />;
    }
  
    if (!isInitialized) {
      return <CartSkeleton />;
    }
  
    if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
        <p className="text-destructive">Error loading cart: {error}</p>
        <Button onClick={() => initialize()} variant="outline">
          Retry
        </Button>
      </div>
    );
  }

  if (items.length === 0) {
     return (
       <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
         <ShoppingBag className="h-16 w-16 text-muted-foreground" />
         <h2 className="text-2xl font-semibold">{t('empty')}</h2>
         <p className="text-muted-foreground text-center max-w-md">
           {t('emptyDescription')}
         </p>
         <Link href={`/${locale}/products`}>
           <Button>{t('continueShopping')}</Button>
         </Link>
       </div>
     );
   }

   // If no cart data but somehow got here, show auth required
   if (!data) {
     return (
       <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
         <ShoppingBag className="h-16 w-16 text-muted-foreground" />
         <h2 className="text-2xl font-semibold">{t('signInRequired')}</h2>
         <p className="text-muted-foreground text-center max-w-md">
           {t('signInRequiredDescription')}
         </p>
         <div className="flex gap-2">
           <Link href={`/${locale}/login`}>
             <Button>{t('signIn')}</Button>
           </Link>
           <Link href={`/${locale}/register`}>
             <Button variant="outline">{t('signUp')}</Button>
           </Link>
         </div>
       </div>
     );
   }

  // Use server-calculated totals (shipping and taxes will be calculated at checkout via pricing rules)
  const subtotal = totals?.subtotal || 0;
  const currency = totals?.currency || DEFAULT_CURRENCY;

  return (
    <motion.div
      className="space-y-4"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div>
        <motion.h1
          className="text-3xl font-bold mb-2 bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
        >
          {t('title')}
        </motion.h1>
        <motion.p
          className="text-muted-foreground"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
        >
          {t('itemsInCart', { count: items.length })}
        </motion.p>
      </div>

      <div className={`${isMobile ? 'flex flex-col' : 'grid grid-cols-1 lg:grid-cols-3'} gap-4`}>
        {/* Cart Items */}
        <motion.div
          className={`${isMobile ? 'flex-1 pb-24' : 'lg:col-span-2'} space-y-1`}
        >

          <AnimatePresence>
            {items.map((item, index) => (
              <CartItemComponent
                key={`${item.productId}-${item.variantId || 'no-variant'}`}
                item={item}
                index={index}
                isMobile={isMobile}
                locale={locale}
                t={t}
                onQuantityChange={(productId, quantity, variantId) => {
                  const key = `${productId}-${variantId || ''}`;
                  setQuantityUpdateQueue(prev => new Map(prev.set(key, quantity)));
                }}
                onRemove={(productId, variantId) => {
                  // Optimistic update in Zustand
                  removeItem(productId, variantId);
                  // Server sync via React Query mutation
                  removeFromCart({ productId, variantId });
                }}
              />
            ))}
          </AnimatePresence>
        </motion.div>

        {/* Order Summary */}
        {!isMobile ? (
          <div className="lg:col-span-1">
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4 }}
            >
              <Card className="sticky top-20 shadow-lg border-0 bg-gradient-to-br from-background to-muted/20">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <ShoppingBag className="h-5 w-5" />
                    {t('subtotal')}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">{t('subtotal')}</span>
                      <motion.span
                        className="font-bold text-lg"
                        key={subtotal}
                        initial={{ scale: 0.9, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                      >
                        {formatCurrency(subtotal, currency)}
                      </motion.span>
                    </div>
                  </div>

                  <motion.div
                    className="bg-gradient-to-r from-muted/50 to-muted/30 p-4 rounded-xl border border-muted"
                    whileHover={{ scale: 1.02 }}
                    transition={{ duration: 0.2 }}
                  >
                    <p className="text-xs text-muted-foreground">
                      {t('shippingNote')}
                    </p>
                  </motion.div>
                </CardContent>
                <CardFooter>
                  <Link href={`/${locale}/shipping`} className="w-full">
                    <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                      <Button size="lg" className="w-full bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary">
                        {t('proceedToCheckout')}
                      </Button>
                    </motion.div>
                  </Link>
                </CardFooter>
              </Card>
            </motion.div>
          </div>
        ) : (
          /* Mobile Bottom Sheet */
          <motion.div
            className="fixed bottom-0 left-0 right-0 bg-background/95 backdrop-blur-lg border-t border-muted shadow-2xl rounded-t-2xl p-6 pb-safe"
            initial={{ y: 100 }}
            animate={{ y: 0 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
          >
            <div className="flex items-center justify-center mb-4">
              <div className="w-12 h-1 bg-muted rounded-full"></div>
            </div>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">{t('subtotal')}</span>
                <motion.span
                  className="font-bold text-xl"
                  key={subtotal}
                  initial={{ scale: 0.9, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                >
                  {formatCurrency(subtotal, currency)}
                </motion.span>
              </div>
              <div className="bg-muted/50 p-3 rounded-lg">
                <p className="text-xs text-muted-foreground text-center">
                  {t('shippingNote')}
                </p>
              </div>
              <Link href={`/${locale}/shipping`} className="block">
                <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                  <Button size="lg" className="w-full bg-gradient-to-r from-primary to-primary/90 h-12 text-base font-semibold">
                    {t('proceedToCheckout')}
                  </Button>
                </motion.div>
              </Link>
            </div>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
}

