// components/checkout/ShippingOrderSummary.tsx
// Order summary component for shipping page with SWR integration

'use client';

import { useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { Info } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useCart } from '@/hooks/queries/useCart';
import { usePrefetchCart } from '@/hooks/queries/usePrefetchCart';
import { useCartAnalytics } from '@/hooks/queries/useCartAnalytics';
import { formatCurrency } from '@/lib/utils';
import { ProductMediaDisplayClient } from '../products/ProductMediaDisplayClient';

export function ShippingOrderSummary() {
  const t = useTranslations('checkout');
  const tCart = useTranslations('cart');

  // Check if this is a buy now checkout by looking at URL params
  const isBuyNow = typeof window !== 'undefined' && new URLSearchParams(window.location.search).get('buyNow') === 'true';

  // Use SWR-powered cart hook with analytics
  const { data: cartData, isLoading, error, isValidating } = useCart({
    enabled: !isBuyNow, // Only fetch for regular cart, buy now handled separately
    revalidateOnFocus: false,
    revalidateOnReconnect: true,
  });

  // Prefetch and analytics hooks
  const { prefetchOnVisible } = usePrefetchCart();
  const { trackCartView } = useCartAnalytics();

  // For buy now, we need to handle it differently since it's not in SWR
  // This would need to be handled by the parent component
  const items = cartData?.items || [];
  const totals = cartData?.totals;

  // Track cart view when data loads
  useEffect(() => {
    if (cartData && !isLoading) {
      trackCartView(cartData.itemCount, cartData.totals?.subtotal);
    }
  }, [cartData, isLoading, trackCartView]);

  if (isLoading || isValidating) {
    return (
      <Card className="shadow-sm border border-gray-200">
        <CardHeader>
          <CardTitle className="text-lg">{t('orderSummary')}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="space-y-2">
              <div className="h-3 bg-gray-200 rounded w-full"></div>
              <div className="h-3 bg-gray-200 rounded w-2/3"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="shadow-sm border border-red-200">
        <CardHeader>
          <CardTitle className="text-lg">{t('orderSummary')}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center py-8 text-red-600">
            <p>{t('cartLoadError')}</p>
            <button
              onClick={() => window.location.reload()}
              className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
              {t('retry')}
            </button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card
      className="shadow-sm border border-gray-200 sticky top-6"
      ref={prefetchOnVisible().ref}
    >
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <span className="bg-blue-100 p-1 rounded-md">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 2a4 4 0 00-4 4v1H5a1 1 0 00-.994.89l-1 9A1 1 0 004 18h12a1 1 0 00.994-1.11l-1-9A1 1 0 0015 7h-1V6a4 4 0 00-4-4zm2 5V6a2 2 0 10-4 0v1h4zm-6 3a1 1 0 112 0 1 1 0 01-2 0zm7-1a1 1 0 100 2 1 1 0 000-2z" clipRule="evenodd" />
            </svg>
          </span>
          {t('orderSummary')}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Items */}
        <div className="space-y-4 max-h-96 overflow-y-auto pr-2">
          {items.map((item) => (
            <div key={`${item.productId}-${item.variantId || 'no-variant'}`} className="flex gap-3 pb-4 border-b border-gray-100 last:border-0 last:pb-0">
              {/* Image */}
              <div className="relative w-16 h-16 flex-shrink-0 rounded-md overflow-hidden bg-gray-100">
                {item.imageUrl ? (
                  <ProductMediaDisplayClient
                    src={item.imageUrl}
                    alt={item.productName}
                    className="object-cover"
                    sizes="64px"
                    type='preview'
                  />
                ) : (
                  <div className="flex h-full items-center justify-center text-xs text-gray-500">
                    {tCart('noImage')}
                  </div>
                )}
              </div>

              {/* Details */}
              <div className="flex-1 min-w-0">
                <p className="font-medium text-sm text-gray-900 line-clamp-1">{item.productName}</p>
                {item.variantId && (
                  <p className="text-xs text-gray-600 line-clamp-1">
                    {item.variantName || t('notSpecified')}
                  </p>
                )}
                <p className="text-xs text-gray-600 mt-1">
                  {tCart('quantity')}: {item.quantity}
                </p>
              </div>

              {/* Price */}
              <div className="text-right">
                <p className="font-semibold text-sm text-gray-900">
                  {formatCurrency(item.price * item.quantity, item.currency)}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Totals */}
        <div className="pt-4 space-y-3">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">{tCart('subtotal')}</span>
            <span className="text-gray-900">{formatCurrency(totals?.subtotal || 0, totals?.currency || 'USD')}</span>
          </div>
          <div className="flex justify-between text-sm items-start">
            <span className="text-gray-600">{t('shipping')}</span>
            <div className="flex items-start gap-2 ms-2">
              <Info className="h-4 w-4 inline-flex text-blue-500 flex-shrink-0 mr-1 mt-0.5" />
              <span className="text-xs text-blue-600 dark:text-blue-400 text-right">
                {t('shippingCostMessage')}
              </span>
            </div>
          </div>
          <div className="border-t border-gray-200 pt-3 flex justify-between font-bold text-base">
            <span className="text-gray-900">{t('total')}</span>
            <span className="text-gray-900">{formatCurrency(totals?.subtotal || 0, totals?.currency || 'USD')}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}