'use client';

// components/checkout/MaomaoPayStep2.tsx
// Refactored: Now an active confirmation step with a clear primary action.
// Guides the user to confirm after they have paid via the SMS prompt.

import { useTranslations } from 'next-intl';
import { ArrowLeft, Hourglass, Smartphone } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

interface MaomaoPayStep2Props {
  onBack: () => void;
  onCancel: () => void;
  onConfirm: () => void; // The new primary action
  isProcessing: boolean; // To disable the button on submission
  orderId: string;
}

export function MaomaoPayStep2({
  onBack,
  onCancel,
  onConfirm,
  isProcessing,
  orderId,
}: MaomaoPayStep2Props) {
  const t = useTranslations('checkout');

  return (
    <Card>
      <CardHeader className="text-center">
        <div className="mx-auto flex h-14 w-14 items-center justify-center rounded-full bg-primary/10 mb-3">
          <Smartphone className="h-8 w-8 text-primary" />
        </div>
        <CardTitle>{t('checkYourPhone')}</CardTitle>
        <CardDescription>
          {t('paymentPromptSent')}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Separator />
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-muted-foreground">{t('orderNumber')}</span>
            <span className="font-mono font-medium">{orderId}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">{t('paymentMethodLabel')}</span>
            <span className="font-medium">{t('mobileMoneyLabel')}</span>
          </div>
        </div>
        <div className="rounded-md border bg-muted p-3 text-sm text-muted-foreground">
          <p className="font-semibold text-foreground mb-1">{t('instructions')}:</p>
          <ul className="list-disc space-y-1 pl-4">
            <li>{t('instructions1')}</li>
            <li>{t('instructions2')}</li>
            <li>
              {t('instructions3')}
            </li>
          </ul>
        </div>
      </CardContent>
      <CardFooter className="flex flex-col gap-3">
        <Button
          onClick={onConfirm}
          disabled={isProcessing}
          className="w-full"
          size="lg"
        >
          {isProcessing && (
            <Hourglass className="mr-2 h-4 w-4 animate-spin" />
          )}
          {isProcessing ? t('confirming') : t('iHaveCompletedPayment')}
        </Button>
        <Button
          onClick={onBack}
          variant="outline"
          className="w-full"
          disabled={isProcessing}
        >
          {t('changePhoneNumber')}
        </Button>
        <Button
          onClick={onCancel}
          variant="link"
          className="text-muted-foreground"
          disabled={isProcessing}
        >
          {t('cancelPayment')}
        </Button>
      </CardFooter>
    </Card>
  );
}