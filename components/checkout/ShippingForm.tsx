'use client';

import { useState, useCallback } from 'react';
import { redirect, useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { useCart } from '@/hooks/queries/useCart';
import { useAddToCart } from '@/hooks/mutations/useAddToCart';
import { useUpdateCartQuantity } from '@/hooks/mutations/useUpdateCartQuantity';
import { useRemoveFromCart } from '@/hooks/mutations/useRemoveFromCart';
import { createOrder } from '@/lib/actions/order.actions';
import { deleteAddress } from '@/lib/actions/user.actions';
import { useCurrency } from '@/components/providers/CurrencyProvider';
import { Plane, Package } from 'lucide-react';
import { DeleteConfirmationDialog } from '@/components/ui/delete-confirmation-dialog';
import { AddressSelector } from './AddressSelector';
import type { Address } from '@/lib/types';

interface ShippingFormProps {
  addresses: Address[];
  locale: string;
  selectedAddressId?: string | null;
  selectedShippingMethod?: 'boat' | 'plane';
  onAddressChange?: (addressId: string) => void;
  onShippingMethodChange?: (method: 'boat' | 'plane') => void;
  showSubmitButton?: boolean;
  isConfirmationStep?: boolean;
}

export function ShippingForm({
  addresses: initialAddresses,
  locale,
  selectedAddressId: externalSelectedAddressId,
  selectedShippingMethod: externalSelectedShippingMethod,
  onAddressChange,
  onShippingMethodChange,
  showSubmitButton = true,
}: ShippingFormProps) {
  const t = useTranslations('checkout');
  const router = useRouter();

  const isBuyNow = typeof window !== 'undefined' && new URLSearchParams(window.location.search).get('buyNow') === 'true';

  // Use SWR-powered cart hook with optimistic updates
  const { data: cartData, clearCart } = useCart({
    enabled: !isBuyNow, // Only fetch for regular cart
  });

  const items = cartData?.items || [];
  const { currency } = useCurrency();

  const [addresses, setAddresses] = useState<Address[]>(initialAddresses);
  const [selectedAddressId, setSelectedAddressId] = useState<string | null>(
    externalSelectedAddressId ?? (initialAddresses.length > 0 ? initialAddresses[0].id : null)
  );
  const [selectedShippingMethod, setSelectedShippingMethod] = useState<'boat' | 'plane'>(
    externalSelectedShippingMethod ?? 'boat'
  );
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [addressToDelete, setAddressToDelete] = useState<string | null>(null);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  const shippingOptions = {
    boat: {
      time: t('shippingBoatTime'),
      description: t('shippingBoatDescription'),
      icon: '⛵',
      title: t('shippingBoat')
    },
    plane: {
      time: t('shippingPlaneTime'),
      description: t('shippingPlaneDescription'),
      icon: '✈️',
      title: t('shippingPlane')
    }
  };

  const handleAddressAdded = useCallback((newAddress: Address) => {
    setAddresses(prev => [...prev, newAddress]);
    setSelectedAddressId(newAddress.id);
    onAddressChange?.(newAddress.id);
  }, [onAddressChange]);

  const handleDeleteClick = (addressId: string) => {
    setAddressToDelete(addressId);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!addressToDelete) return;

    setDeletingId(addressToDelete);
    setError('');

    try {
      const result = await deleteAddress(addressToDelete);

      if (result.success) {
        const updatedAddresses = addresses.filter(addr => addr.id !== addressToDelete);
        setAddresses(updatedAddresses);

        if (selectedAddressId === addressToDelete) {
          const nextAddressId = updatedAddresses.length > 0 ? updatedAddresses[0].id : null;
          setSelectedAddressId(nextAddressId);
          onAddressChange?.(nextAddressId || '');
        }

        setDeleteDialogOpen(false);
        setAddressToDelete(null);
      } else {
        setError(result.error || t('deleteError'));
      }
    } catch (err) {
      setError(t('deleteError'));
    } finally {
      setDeletingId(null);
    }
  };

  // Initialize mutation hooks for optimistic cart operations
  const addToCartMutation = useAddToCart();
  const updateQuantityMutation = useUpdateCartQuantity();
  const removeFromCartMutation = useRemoveFromCart();

  // Batch cart operations for enhanced UX during checkout
  const handleBatchCartOperations = async (operations: Array<{
    type: 'add' | 'update' | 'remove';
    productId: number;
    quantity?: number;
    variantId?: number;
    imageUrl?: string;
  }>) => {
    try {
      // Process operations in parallel for better performance
      const promises = operations.map(async (op) => {
        switch (op.type) {
          case 'add':
            return addToCartMutation.mutateAsync({
              productId: op.productId,
              variantId: op.variantId,
              quantity: op.quantity || 1,
              imageUrl: op.imageUrl,
            });
          case 'update':
            return updateQuantityMutation.mutateAsync({
              productId: op.productId,
              quantity: op.quantity || 0,
              variantId: op.variantId,
            });
          case 'remove':
            return removeFromCartMutation.mutateAsync({
              productId: op.productId,
              variantId: op.variantId,
            });
          default:
            throw new Error(`Unknown operation type: ${op.type}`);
        }
      });

      await Promise.allSettled(promises);
    } catch (error) {
      console.error('Batch cart operations failed:', error);
      setError(t('cartUpdateError'));
    }
  };

  // Enhanced error handling with user-friendly messages and retry logic
  const handleError = (error: unknown, context: string) => {
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';

    // Enhanced error classification for better UX
    if (errorMessage.includes('Network')) {
      setError(t('networkError'));
    } else if (errorMessage.includes('Authentication')) {
      setError(t('authError'));
    } else if (errorMessage.includes('Cart')) {
      setError(t('cartError'));
    } else {
      setError(`${context}: ${errorMessage}`);
    }

    console.error(`${context} error:`, error);
  };

  // Loading states with progress indicators
  const isAnyLoading = addToCartMutation.isPending ||
                       updateQuantityMutation.isPending ||
                       removeFromCartMutation.isPending;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!selectedAddressId) {
      setError(t('selectAddressError'));
      return;
    }

    if (items.length === 0) {
      setError(t('emptyCartError'));
      return;
    }

    setIsProcessing(true);

    try {
      const selectedAddress = addresses.find((addr) => addr.id === selectedAddressId);

      if (!selectedAddress) {
        throw new Error('Address not found');
      }

      let exchangeRate = 1;
      try {
        const response = await fetch(`/api/exchange-rate?from=CNY&to=${currency}`);
        const data = await response.json();
        exchangeRate = data.rate || 1;
      } catch (error) {
        console.error('Failed to get exchange rate:', error);
      }

      const result = await createOrder({
        items,
        shippingAddress: selectedAddress,
        userCurrency: currency,
        shippingCost: 0,
        shippingMethod: selectedShippingMethod,
      });

      if (result.success && result.orderId) {
        redirect(`/${locale}/payment?orderId=${result.orderId}`);
      } else {
        setError(result.error || t('orderError'));
      }
    } catch (err) {
      console.error('Checkout error:', err);
      setError(err instanceof Error ? err.message : t('orderError'));
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 animate-in fade-in slide-in-from-bottom-2 duration-500">
      <form onSubmit={handleSubmit} className="space-y-8">
        {error && (
          <div className="p-4 text-sm font-medium text-red-900 bg-red-50 border border-red-200 rounded-lg animate-in fade-in slide-in-from-top-2 duration-300 dark:bg-red-950 dark:text-red-100 dark:border-red-800">
            {error}
          </div>
        )}

        <AddressSelector
          addresses={addresses}
          selectedAddressId={selectedAddressId}
          onAddressSelect={(id) => {
            setSelectedAddressId(id);
            onAddressChange?.(id);
          }}
          onAddressAdded={handleAddressAdded}
          onDeleteClick={handleDeleteClick}
          locale={locale}
        />

        <div className="space-y-4 pt-4 border-t border-gray-200">
          <div className="flex items-center gap-2 mb-6">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Package className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">{t('selectShippingMethod')}</h2>
              <p className="text-sm text-muted-foreground mt-0.5">Choose your preferred delivery method</p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Object.entries(shippingOptions).map(([method, option], index) => (
              <div
                key={method}
                className="animate-in fade-in slide-in-from-bottom-2 duration-500"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div
                  className={`p-4 rounded-lg border cursor-pointer transition-all duration-200 group ${
                    selectedShippingMethod === method
                      ? 'border-blue-500 ring-2 ring-blue-500 bg-blue-50 shadow-md'
                      : 'border-gray-200 hover:border-blue-300 hover:bg-gray-50 hover:shadow-sm'
                  }`}
                  onClick={() => {
                    setSelectedShippingMethod(method as 'boat' | 'plane');
                    onShippingMethodChange?.(method as 'boat' | 'plane');
                  }}
                >
                  <div className="flex items-start gap-3">
                    <div className={`flex items-center h-5 w-5 rounded-full border flex-shrink-0 mt-0.5 transition-all duration-200 ${
                      selectedShippingMethod === method
                        ? 'bg-blue-600 border-blue-600'
                        : 'border-gray-300 group-hover:border-blue-400'
                    }`}>
                      {selectedShippingMethod === method && (
                        <div className="h-2 w-2 rounded-full bg-white m-auto"></div>
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="text-2xl">{option.icon}</span>
                        <h3 className="font-semibold text-gray-900 capitalize">{option.title}</h3>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">{option.description}</p>
                      <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-100">
                        ⏱️ {option.time}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {showSubmitButton && (
          <div className="pt-6 border-t border-gray-200">
            <Button
              type="submit"
              size="lg"
              className="w-full py-6 text-lg font-semibold transition-all duration-200 bg-blue-600 hover:bg-blue-700 text-white shadow-md hover:shadow-lg"
              disabled={isProcessing || isAnyLoading || !selectedAddressId}
            >
              {(isProcessing || isAnyLoading) ? (
                <span className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {isProcessing ? t('processing') : t('updatingCart')}
                </span>
              ) : (
                <span className="flex items-center justify-center">
                  <Plane className="mr-2 h-5 w-5" />
                  {t('placeOrder')}
                </span>
              )}
            </Button>
          </div>
        )}
      </form>

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleConfirmDelete}
        isLoading={deletingId !== null}
      />
    </div>
  );
}
