'use client';

// components/checkout/BankTransferDetails.tsx
// Refactored: Replaced two stacked cards with a space-saving tabbed interface.
// Added a clear confirmation action for the user.

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { <PERSON>Left, Co<PERSON>, Check, Hourglass, Globe } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';

// --- Type definitions for bank details ---
interface BankDetails {
  beneficiaryName: string;
  accountNumber: string;
  swiftCode: string;
  bankName: string;
  address: string;
  country: string;
  currency: string;
  processingFee: string;
}

interface GlobalBankDetails {
  beneficiaryName: string;
  currencies: string;
  accountNumber: string;
  swiftCode: string;
  bankCode: string;
  branchCode: string;
  bankName: string;
  bankAddress: string;
}

interface BankTransferDetailsProps {
  onBack: () => void;
  onConfirm: () => void;
  isProcessing: boolean;
  sepaBankDetails: BankDetails;
  globalBankDetails: GlobalBankDetails;
}

// --- Helper component for copyable fields ---
// Encapsulates the copy logic and UI state for better reusability.
const CopyableDetail = ({ label, value }: { label: string; value: string }) => {
  const [hasCopied, setHasCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(value);
      setHasCopied(true);
      setTimeout(() => setHasCopied(false), 2000); // Reset icon after 2 seconds
    } catch (err) {
      console.error('Failed to copy text:', err);
    }
  };

  return (
    <div className="flex items-start justify-between text-sm">
      <span className="text-muted-foreground">{label}</span>
      <button
        onClick={handleCopy}
        className="flex max-w-[60%] items-center gap-2 rounded-md px-1 py-0.5 text-right font-medium text-primary transition-colors hover:bg-accent"
      >
        <span className="truncate">{value}</span>
        {hasCopied ? (
          <Check className="h-4 w-4 flex-shrink-0 text-green-500" />
        ) : (
          <Copy className="h-4 w-4 flex-shrink-0" />
        )}
      </button>
    </div>
  );
};

// --- Main component ---
export function BankTransferDetails({
  onBack,
  onConfirm,
  isProcessing,
  sepaBankDetails,
  globalBankDetails,
}: BankTransferDetailsProps) {
  const t = useTranslations('checkout');

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('offlineBankTransfer')}</CardTitle>
        <CardDescription>
          {t('bankTransferInstructions')}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* The new tabbed interface for SEPA vs Global */}
        <Tabs defaultValue="sepa" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="sepa">{t('sepaForEU')}</TabsTrigger>
            <TabsTrigger value="global">
              <Globe className="mr-2 h-4 w-4" />
              {t('global')}
            </TabsTrigger>
          </TabsList>

          {/* SEPA Tab Content */}
          <TabsContent value="sepa" className="mt-4 space-y-4">
            <div className="space-y-3 rounded-md border p-4">
              <CopyableDetail label={t('beneficiary')} value={sepaBankDetails.beneficiaryName} />
              <CopyableDetail label={t('iban')} value={sepaBankDetails.accountNumber} />
              <CopyableDetail label={t('swiftBic')} value={sepaBankDetails.swiftCode} />
              <CopyableDetail label={t('bankName')} value={sepaBankDetails.bankName} />
            </div>
            <div className="rounded-md bg-primary/10 px-4 py-3 text-sm font-medium text-primary">
              {t('processingFee')} {sepaBankDetails.processingFee}
            </div>
          </TabsContent>

          {/* Global Tab Content */}
          <TabsContent value="global" className="mt-4 space-y-3 rounded-md border p-4">
            <CopyableDetail label={t('beneficiary')} value={globalBankDetails.beneficiaryName} />
            <CopyableDetail label={t('accountNo')} value={globalBankDetails.accountNumber} />
            <CopyableDetail label={t('swiftBic')} value={globalBankDetails.swiftCode} />
            <CopyableDetail label={t('bankCode')} value={globalBankDetails.bankCode} />
            <CopyableDetail label={t('branchCode')} value={globalBankDetails.branchCode} />
            <CopyableDetail label={t('bankName')} value={globalBankDetails.bankName} />
            <div className="flex items-start justify-between text-sm">
              <span className="text-muted-foreground">{t('currencies')}</span>
              <span className="max-w-[60%] text-right font-medium">{globalBankDetails.currencies}</span>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex flex-col sm:flex-row-reverse gap-3">
        <Button
          onClick={onConfirm}
          disabled={isProcessing}
          className="w-full sm:flex-1"
        >
          {isProcessing && <Hourglass className="mr-2 h-4 w-4 animate-spin" />}
          {isProcessing ? t('processing') : t('iHaveTransferredMoney')}
        </Button>
        <Button
          onClick={onBack}
          variant="outline"
          className="w-full sm:w-auto"
          disabled={isProcessing}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          {t('back')}
        </Button>
      </CardFooter>
    </Card>
  );
}