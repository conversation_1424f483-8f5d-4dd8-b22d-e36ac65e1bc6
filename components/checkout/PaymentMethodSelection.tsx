'use client';

// components/checkout/PaymentMethodSelection.tsx
// Refactored: Simplified design for the new layout. Selection is now immediate,
// removing the need for a separate "Continue" button.

import { useTranslations } from 'next-intl';
import { ArrowLeft, Smartphone, Landmark, CreditCard } from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

interface PaymentMethodSelectionProps {
  // The component now only needs one prop: a function to call when a method is selected.
  onSelect: (methodId: string) => void;
}

// The data for payment methods remains the same.

export function PaymentMethodSelection({ onSelect }: PaymentMethodSelectionProps) {
  const t = useTranslations('checkout');

  const paymentMethods = [
    {
      id: 'mobile_money',
      icon: <Smartphone className="h-7 w-7 text-primary" />,
      title: t('mobileMoney'),
      description: t('mobileMoneyDesc'),
    },
    {
      id: 'bank_transfer',
      icon: <Landmark className="h-7 w-7 text-primary" />,
      title: t('bankTransfer'),
      description: t('bankTransferDesc'),
    },
    {
      id: 'card_payment',
      icon: <CreditCard className="h-7 w-7 text-primary" />,
      title: t('cardPayment'),
      description: t('cardPaymentDesc'),
    },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('paymentMethod')}</CardTitle>
        <CardDescription>
          {t('selectPaymentMethod')}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {paymentMethods.map(method => (
            <button
              key={method.id}
              onClick={() => onSelect(method.id)}
              disabled={method.id === 'card_payment'} // Example of disabling an option
              className="flex w-full items-center gap-4 rounded-lg border p-4 text-left transition-all hover:bg-accent focus:outline-none focus:ring-2 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
            >
              {/* Icon */}
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                {method.icon}
              </div>

              {/* Text Content */}
              <div className="flex-1">
                <p className="font-semibold">{method.title}</p>
                <p className="text-sm text-muted-foreground">
                  {method.description}
                </p>
              </div>
            </button>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}