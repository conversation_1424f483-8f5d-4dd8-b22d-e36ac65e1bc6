'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { MapPin, Plus, Trash2 } from 'lucide-react';
import { AddAddressInline } from './AddAddressInline';
import type { Address } from '@/lib/types';

interface AddressSelectorProps {
  addresses: Address[];
  selectedAddressId: string | null;
  onAddressSelect: (id: string) => void;
  onAddressAdded: (address: Address) => void;
  onDeleteClick: (id: string) => void;
  locale: string;
}

export function AddressSelector({
  addresses,
  selectedAddressId,
  onAddressSelect,
  onAddressAdded,
  onDeleteClick,
  locale,
}: AddressSelectorProps) {
  const t = useTranslations('checkout');
  const [showAddForm, setShowAddForm] = useState(false);

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 mb-6">
        <div className="p-2 bg-blue-100 rounded-lg">
          <MapPin className="h-5 w-5 text-blue-600" />
        </div>
        <div>
          <h2 className="text-xl font-bold text-gray-900">{t('selectAddress')}</h2>
          <p className="text-sm text-muted-foreground mt-0.5">Choose where to deliver your order</p>
        </div>
      </div>

      {addresses.length === 0 && !showAddForm && (
        <div className="p-6 text-center rounded-lg border-2 border-dashed border-amber-200 bg-amber-50">
          <p className="font-semibold text-amber-900">{t('noAddressesFound')}</p>
          <p className="text-sm text-amber-700 mt-1">{t('addAddressToContinue')}</p>
        </div>
      )}

      <div className="grid grid-cols-1 gap-3">
        {addresses.map((address, index) => (
          <div
            key={address.id}
            className="animate-in fade-in slide-in-from-left-2 duration-500"
            style={{ animationDelay: `${index * 50}ms` }}
          >
            <div
              className={`p-4 rounded-lg border cursor-pointer transition-all duration-200 group ${
                selectedAddressId === address.id
                  ? 'border-blue-500 ring-2 ring-blue-500 bg-blue-50 shadow-md'
                  : 'border-gray-200 hover:border-blue-300 hover:bg-gray-50 hover:shadow-sm'
              }`}
              onClick={() => onAddressSelect(address.id)}
            >
              <div className="flex items-start gap-3">
                <div className={`flex items-center h-5 w-5 rounded-full border flex-shrink-0 mt-0.5 transition-all duration-200 ${
                  selectedAddressId === address.id
                    ? 'bg-blue-600 border-blue-600'
                    : 'border-gray-300 group-hover:border-blue-400'
                }`}>
                  {selectedAddressId === address.id && (
                    <div className="h-2 w-2 rounded-full bg-white m-auto"></div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex justify-between items-start gap-2">
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900">{address.fullName}</h3>
                      <p className="text-sm text-gray-600 mt-1 flex items-center gap-1">
                        <span>📞</span>
                        {address.phone}
                      </p>
                      {address.isWhatsApp && (
                        <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-100 mt-2">
                          ✓ WhatsApp
                        </span>
                      )}
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={(e) => {
                        e.stopPropagation();
                        onDeleteClick(address.id);
                      }}
                      className="h-8 w-8 text-destructive hover:text-destructive hover:bg-red-50 dark:hover:bg-red-950 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex-shrink-0"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {!showAddForm && (
        <div className="pt-4 border-t border-gray-200">
          <Button
            type="button"
            variant="outline"
            onClick={() => setShowAddForm(true)}
            className="w-full h-11 border-dashed border-blue-300 text-blue-600 hover:bg-blue-50 transition-colors duration-200"
          >
            <Plus className="h-4 w-4 mr-2" />
            {t('addNewAddress')}
          </Button>
        </div>
      )}

      {showAddForm && (
        <div className="pt-4 animate-in fade-in slide-in-from-top-2 duration-300">
          <AddAddressInline
            locale={locale}
            onAddressAdded={(newAddress) => {
              onAddressAdded(newAddress);
              setShowAddForm(false);
            }}
            onClose={() => setShowAddForm(false)}
          />
        </div>
      )}
    </div>
  );
}

