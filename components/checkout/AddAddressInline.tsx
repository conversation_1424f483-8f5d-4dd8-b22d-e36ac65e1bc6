'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { addAddress } from '@/lib/actions/user.actions';
import { Plus, X } from 'lucide-react';
import type { Address } from '@/lib/types';

interface AddAddressInlineProps {
  locale: string;
  onAddressAdded: (address: Address) => void;
  onClose: () => void;
}

export function AddAddressInline({ locale, onAddressAdded, onClose }: AddAddressInlineProps) {
  const t = useTranslations('checkout');

  const [formData, setFormData] = useState({
    fullName: '',
    phone: '',
    isWhatsApp: false,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
  };

  const handleAddAddress = async () => {
    if (!formData.fullName.trim() || !formData.phone.trim()) {
      setError('Full Name and Phone Number are required');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const addressData = {
        fullName: formData.fullName,
        phone: formData.phone,
        isWhatsApp: formData.isWhatsApp,
        addressLine1: 'TBD',
        addressLine2: '',
        city: 'TBD',
        state: '',
        postalCode: '',
        country: 'TBD',
        isDefault: false,
      };
      const result = await addAddress(addressData);

      if (result.success && result.address) {
        onAddressAdded(result.address);
        onClose();
      } else {
        setError(result.error || 'Failed to add address');
      }
    } catch (err) {
      setError('Failed to add address');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="border-2 border-dashed border-blue-300 bg-blue-50 animate-in fade-in slide-in-from-top-2 duration-300">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-bold flex items-center gap-2">
            <div className="p-1.5 bg-blue-200 rounded-lg">
              <Plus className="h-5 w-5 text-blue-600" />
            </div>
            {t('addNewAddress')}
          </CardTitle>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0 hover:bg-blue-200"
            disabled={isLoading}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-5">
          {error && (
            <div className="p-4 text-sm font-medium text-red-900 bg-red-50 border border-red-200 rounded-lg animate-in fade-in slide-in-from-top-2 duration-300 dark:bg-red-950 dark:text-red-100 dark:border-red-800">
              {error}
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <Label htmlFor="inline-fullName" className="text-sm font-semibold text-foreground">
                Full Name <span className="text-destructive">*</span>
              </Label>
              <Input
                id="inline-fullName"
                name="fullName"
                type="text"
                value={formData.fullName}
                onChange={handleChange}
                disabled={isLoading}
                placeholder="John Doe"
                className="transition-all duration-200"
              />
            </div>

            <div className="space-y-3">
              <Label htmlFor="inline-phone" className="text-sm font-semibold text-foreground">
                Phone Number <span className="text-destructive">*</span>
              </Label>
              <Input
                id="inline-phone"
                name="phone"
                type="tel"
                value={formData.phone}
                onChange={handleChange}
                disabled={isLoading}
                placeholder="+****************"
                className="transition-all duration-200"
              />
            </div>
          </div>

          <div className="flex items-center space-x-3 pt-2">
            <Checkbox
              id="inline-isWhatsApp"
              name="isWhatsApp"
              checked={formData.isWhatsApp}
              onCheckedChange={(checked) =>
                setFormData((prev) => ({ ...prev, isWhatsApp: checked as boolean }))
              }
              disabled={isLoading}
              className="transition-all duration-200"
            />
            <Label htmlFor="inline-isWhatsApp" className="text-sm font-medium cursor-pointer">
              This phone number is my WhatsApp number
            </Label>
          </div>

          <div className="flex justify-end space-x-3 pt-4 border-t border-blue-200">
            <Button 
              type="button" 
              variant="outline" 
              onClick={onClose} 
              disabled={isLoading}
              className="px-6 transition-all duration-200"
            >
              {t('cancel')}
            </Button>
            <Button 
              type="button"
              onClick={handleAddAddress}
              disabled={isLoading}
              className="px-6 transition-all duration-200"
            >
              {isLoading ? (
                <span className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {t('saving')}
                </span>
              ) : (
                t('addAddress')
              )}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
