'use client';

// components/checkout/OrderSubmitButton.tsx
// Dedicated submit button for order confirmation

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { useCartStore } from '@/hooks/use-cart-store';
import { createOrder } from '@/lib/actions/order.actions';
import { useCurrency } from '@/components/providers/CurrencyProvider';
import { Plane } from 'lucide-react';
import type { Address, CartData } from '@/lib/types';

interface OrderSubmitButtonProps {
  selectedAddressId: string | null;
  selectedShippingMethod: 'boat' | 'plane';
  addresses: Address[];
  locale: string;
  cartData?: CartData | null;
}

export function OrderSubmitButton({
  selectedAddressId,
  selectedShippingMethod,
  addresses,
  locale,
  cartData: propCartData
}: OrderSubmitButtonProps) {
  const t = useTranslations('checkout');
  const router = useRouter();

  // Check if this is a buy now checkout
  const isBuyNow = typeof window !== 'undefined' && new URLSearchParams(window.location.search).get('buyNow') === 'true';
  const cartStoreData = useCartStore((state) => isBuyNow ? state.buyNowData : state.data);
  const cartData = propCartData || cartStoreData;
  const items = cartData?.items || [];
  const clearCart = useCartStore((state) => isBuyNow ? state.clearBuyNowCart : state.clearCart);
  const { currency } = useCurrency();

  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async () => {
    setError('');

    if (!selectedAddressId) {
      setError(t('selectAddressError'));
      return;
    }

    if (items.length === 0) {
      setError(t('emptyCartError'));
      return;
    }

    setIsProcessing(true);

    try {
      const selectedAddress = addresses.find((addr) => addr.id === selectedAddressId);

      if (!selectedAddress) {
        throw new Error('Address not found');
      }

      // Create order using server action
      const result = await createOrder({
        items,
        shippingAddress: selectedAddress,
        userCurrency: currency,
        shippingCost: 0, // Will be calculated server-side
        shippingMethod: selectedShippingMethod,
      });

      if (result.success && result.orderId) {
        // Clear cart immediately after order creation to prevent duplicate orders
        clearCart();
        // Redirect to payment page with order ID
        router.push(`/${locale}/payment?orderId=${result.orderId}`);
      } else {
        throw new Error(result.error || 'Failed to create order');
      }
    } catch (err) {
      console.error('Order creation error:', err);
      setError(err instanceof Error ? err.message : t('orderError'));
      setIsProcessing(false);
    }
  };

  return (
    <div className="pt-6 border-t border-gray-200">
      {error && (
        <div className="mb-4 p-4 text-sm text-red-700 bg-red-50 rounded-lg">
          {error}
        </div>
      )}
      <Button
        onClick={handleSubmit}
        size="lg"
        className="w-full py-6 text-lg bg-green-600 hover:bg-green-700 text-white"
        disabled={isProcessing || !selectedAddressId}
      >
        {isProcessing ? (
          <span className="flex items-center justify-center">
            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {t('processing')}
          </span>
        ) : (
          <span className="flex items-center justify-center">
            <Plane className="mr-2 h-5 w-5" />
            {t('confirmAndPlaceOrder')}
          </span>
        )}
      </Button>
    </div>
  );
}