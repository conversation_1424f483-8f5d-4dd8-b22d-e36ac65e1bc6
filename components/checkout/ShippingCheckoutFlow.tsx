// components/checkout/ShippingCheckoutFlow.tsx
// Multi-step checkout flow with animations, analytics, and SWR-powered cart management

'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTranslations } from 'next-intl';
import { ShippingForm } from './ShippingForm';
import { ShippingOrderSummary } from './ShippingOrderSummary';
import { OrderSubmitButton } from './OrderSubmitButton';
import { Button } from '@/components/ui/button';
import { ArrowLeft, ArrowRight } from 'lucide-react';
import { useCart } from '@/hooks/queries/useCart';
import { usePrefetchCart } from '@/hooks/queries/usePrefetchCart';
import { useCartAnalytics } from '@/hooks/queries/useCartAnalytics';
import type { Address, CartData } from '@/lib/types';

interface ShippingCheckoutFlowProps {
  addresses: Address[];
  locale: string;
}

type CheckoutStep = 'shipping' | 'confirmation';

export function ShippingCheckoutFlow({ addresses, locale }: ShippingCheckoutFlowProps) {
  const t = useTranslations('checkout');
  const [currentStep, setCurrentStep] = useState<CheckoutStep>('shipping');
  const [selectedAddressId, setSelectedAddressId] = useState<string | null>(
    addresses.length > 0 ? addresses[0].id : null
  );
  const [selectedShippingMethod, setSelectedShippingMethod] = useState<'boat' | 'plane'>('boat');

  // SWR-powered cart management with prefetching and analytics
  const { data: cartData, isLoading: cartLoading, error: cartError } = useCart({
    enabled: true,
    revalidateOnFocus: false,
  });

  // Prefetch utilities for better UX
  const { backgroundRefresh } = usePrefetchCart();

  // Analytics hook for checkout flow
  const { trackCartOperation, startOperationTimer, endOperationTimer } = useCartAnalytics();

  // Background refresh cart data during checkout for data consistency
  useEffect(() => {
    const stopRefresh = backgroundRefresh(2 * 60 * 1000); // 2 minutes
    return stopRefresh;
  }, [backgroundRefresh]);

  // Track checkout start and cart state
  useEffect(() => {
    trackCartOperation('checkout_start');
    if (cartData) {
      trackCartOperation('checkout_start', {
        quantity: cartData.itemCount,
        success: true
      });
    }
  }, [trackCartOperation, cartData]);

  const handleNext = () => {
    if (currentStep === 'shipping') {
      const startTime = startOperationTimer();
      setCurrentStep('confirmation');
      // Scroll to top when transitioning to confirmation step
      if (typeof window !== 'undefined') {
        window.scrollTo({ top: 0 });
      }
      const duration = endOperationTimer(startTime, 'checkout_start');
      trackCartOperation('checkout_start', { duration, success: true });
    }
  };

  const handleBack = () => {
    if (currentStep === 'confirmation') {
      setCurrentStep('shipping');
      if (typeof window !== 'undefined') {
        window.scrollTo({ top: 0 });
      }
    }
  };

  // Enhanced step validation with cart state
  const getStepValidation = () => {
    if (cartError) return { valid: false, message: 'Cart data unavailable' };
    if (cartLoading) return { valid: false, message: 'Loading cart...' };
    if (!cartData?.items?.length) return { valid: false, message: 'Cart is empty' };
    if (!selectedAddressId) return { valid: false, message: 'Please select an address' };
    return { valid: true };
  };

  const stepValidation = getStepValidation();

  const stepVariants = {
    enter: (direction: number) => ({
      x: direction > 0 ? 300 : -300,
      opacity: 0,
    }),
    center: {
      zIndex: 1,
      x: 0,
      opacity: 1,
    },
    exit: (direction: number) => ({
      zIndex: 0,
      x: direction < 0 ? 300 : -300,
      opacity: 0,
    }),
  };

  const stepTransition = {
    x: { type: 'spring' as const, stiffness: 300, damping: 30 },
    opacity: { duration: 0.2 },
  };

  return (
    <div className={currentStep === 'confirmation' ? "flex flex-col gap-8" : "flex flex-col lg:flex-row gap-8"}>
      {/* Main Content */}
      <div className="flex-1">
        <AnimatePresence mode="wait" custom={currentStep === 'shipping' ? 1 : -1}>
          <motion.div
            key={currentStep}
            custom={currentStep === 'shipping' ? 1 : -1}
            variants={stepVariants}
            initial="enter"
            animate="center"
            exit="exit"
            transition={stepTransition}
            className={currentStep === 'confirmation' ? "min-h-[600px] pt-0" : "min-h-[600px]"}
          >
            {currentStep === 'shipping' ? (
              <ShippingForm
                addresses={addresses}
                locale={locale}
                selectedAddressId={selectedAddressId}
                selectedShippingMethod={selectedShippingMethod}
                onAddressChange={setSelectedAddressId}
                onShippingMethodChange={setSelectedShippingMethod}
                showSubmitButton={false}
              />
            ) : (
              <OrderConfirmationStep
                addresses={addresses}
                selectedAddressId={selectedAddressId}
                selectedShippingMethod={selectedShippingMethod}
                locale={locale}
                cartData={cartData}
              />
            )}
          </motion.div>
        </AnimatePresence>

        {/* Navigation Buttons */}
        <motion.div
          className="flex justify-between mt-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          {currentStep === 'confirmation' && (
            <Button
              variant="outline"
              onClick={handleBack}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              {t('back')}
            </Button>
          )}

          {currentStep === 'shipping' && (
            <div></div> // Spacer for flex justify-between
          )}

          {currentStep === 'shipping' && (
            <Button
              onClick={handleNext}
              disabled={!stepValidation.valid}
              className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
              title={!stepValidation.valid ? stepValidation.message : undefined}
            >
              {cartLoading ? (
                <>
                  <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {t('loading')}
                </>
              ) : (
                <>
                  {t('continueToConfirmation')}
                  <ArrowRight className="h-4 w-4" />
                </>
              )}
            </Button>
          )}
        </motion.div>
      </div>

    </div>
  );
}

// Order Confirmation Step Component with enhanced cart data
function OrderConfirmationStep({
  addresses,
  selectedAddressId,
  selectedShippingMethod,
  locale,
  cartData
}: {
  addresses: Address[];
  selectedAddressId: string | null;
  selectedShippingMethod: 'boat' | 'plane';
  locale: string;
  cartData?: CartData | null; // Cart data from SWR
}) {
  const t = useTranslations('checkout');
  const selectedAddress = addresses.find(addr => addr.id === selectedAddressId);

  // Use enhanced cart data for confirmation display
  const items = cartData?.items || [];
  const totals = cartData?.totals;

  const shippingOptions = {
    boat: {
      time: t('shippingBoatTime'),
      description: t('shippingBoatDescription'),
      icon: '⛵',
      title: t('shippingBoat')
    },
    plane: {
      time: t('shippingPlaneTime'),
      description: t('shippingPlaneDescription'),
      icon: '✈️',
      title: t('shippingPlane')
    }
  };

  return (
    <div className="space-y-8">
      {/* Order Summary at the top */}
      <div className="max-w-2xl mx-auto">
        <ShippingOrderSummary />
      </div>

      {/* Address and Shipping Details */}
      <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
        {/* Shipping Address */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
              📍 {t('shippingAddress')}
            </h3>
            {selectedAddress && (
              <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <h4 className="font-medium text-gray-900">{selectedAddress.fullName}</h4>
                <div className="text-sm text-gray-700 mt-1">
                  <p className="font-medium">{selectedAddress.fullName}</p>
                  <p>{selectedAddress.phone}</p>
                  {selectedAddress.isWhatsApp && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                      WhatsApp
                    </span>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Shipping Method */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
              🚚 {t('shippingMethod')}
            </h3>
            <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
              <div className="flex items-center gap-3">
                <span className="text-2xl">{shippingOptions[selectedShippingMethod].icon}</span>
                <div>
                  <h4 className="font-medium text-gray-900">{shippingOptions[selectedShippingMethod].title}</h4>
                  <p className="text-sm text-gray-600 mt-1">{shippingOptions[selectedShippingMethod].description}</p>
                  <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full mt-2 inline-block">
                    {shippingOptions[selectedShippingMethod].time}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Place Order Button */}
      <div className="max-w-2xl mx-auto">
        <OrderSubmitButton
          selectedAddressId={selectedAddressId}
          selectedShippingMethod={selectedShippingMethod}
          addresses={addresses}
          locale={locale}
          cartData={cartData}
        />
      </div>
    </div>
  );
}
