'use client';

// components/checkout/OrderSummary.tsx
// New Component: A responsive summary that is a collapsible accordion on mobile
// and a fixed sidebar on desktop.

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { ShoppingCart, ChevronDown, ChevronUp } from 'lucide-react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import type { CartItem } from '@/lib/types';

interface OrderSummaryProps {
  items: CartItem[];
  subtotal: number;
  currency: string;
  shippingCost: number;
  taxes: number;
}

// A simple helper to format currency consistently
const formatCurrency = (amount: number, currency: string) => {
  const symbols: { [key: string]: string } = {
    USD: '$',
    EUR: '€',
    CNY: '¥',
  };
  return `${symbols[currency] || '$'}${amount.toFixed(2)}`;
};

export function OrderSummary({
  items,
  subtotal,
  currency,
  shippingCost,
  taxes,
}: OrderSummaryProps) {
  const t = useTranslations('checkout');
  const total = subtotal + shippingCost + taxes;

  const summaryContent = (
    <div className="space-y-4">
      {/* Item List */}
      <div className="max-h-64 space-y-3 overflow-y-auto pr-2">
        {items.map((item, index) => (
          <div key={`${item.productId}-${item.variantId || 'no-variant'}-${index}`} className="flex items-start justify-between gap-4">
            <div className="flex items-start gap-3">
              <img
                src={item.imageUrl || '/placeholder.svg'}
                alt={item.productName}
                className="h-12 w-12 rounded-md object-cover"
              />
              <div>
                <p className="text-sm font-medium leading-tight">
                  {item.productName}
                </p>
                {item.variantName && (
                  <p className="text-xs text-muted-foreground">
                    {item.variantName}
                  </p>
                )}
                 <p className="text-xs text-muted-foreground">
                  {t('qty')} {item.quantity}
                </p>
              </div>
            </div>
            <p className="text-sm font-medium">
              {formatCurrency(item.price * item.quantity, currency)}
            </p>
          </div>
        ))}
      </div>

      <Separator />

      {/* Price Breakdown */}
      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span className="text-muted-foreground">{t('subtotal')}</span>
          <span>{formatCurrency(subtotal, currency)}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-muted-foreground">{t('shipping')}</span>
          <span>
            {shippingCost > 0 ? formatCurrency(shippingCost, currency) : t('free')}
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-muted-foreground">{t('taxes')}</span>
          <span>
            {taxes > 0 ? formatCurrency(taxes, currency) : t('calculatedAtNextStep')}
          </span>
        </div>
      </div>

      <Separator />

      {/* Total */}
      <div className="flex justify-between text-lg font-semibold">
        <span>{t('total')}</span>
        <span>{formatCurrency(total, currency)}</span>
      </div>
    </div>
  );

  return (
    <>
      {/* 
        Mobile View: A collapsible accordion.
        - `md:hidden` makes this block visible only on screens *smaller* than 768px.
      */}
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm md:hidden mb-6">
        <Accordion type="single" collapsible>
          <AccordionItem value="order-summary" className="border-none">
            <AccordionTrigger className="p-4">
              <div className="flex w-full items-center justify-between">
                <div className="flex items-center gap-3">
                  <ShoppingCart className="h-5 w-5 text-primary" />
                  <span className="font-semibold">{t('showOrderSummary')}</span>
                </div>
                <span className="text-lg font-bold">
                  {formatCurrency(total, currency)}
                </span>
              </div>
            </AccordionTrigger>
            <AccordionContent className="p-4 pt-0">
              {summaryContent}
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>

      {/* 
        Desktop View: A standard, always-visible card.
        - `hidden` makes it invisible by default.
        - `md:block` makes it visible on screens *larger* than 768px.
      */}
      <div className="hidden md:block">
        <Card>
          <CardHeader>
            <CardTitle>{t('orderSummary')}</CardTitle>
          </CardHeader>
          <CardContent>
            {summaryContent}
          </CardContent>
        </Card>
      </div>
    </>
  );
}