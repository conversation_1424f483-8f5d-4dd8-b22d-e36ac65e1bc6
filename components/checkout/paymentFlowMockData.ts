// Mock data for payment flow components

export const mockCustomerInfo = {
  name: '<PERSON><PERSON>ope<PERSON>' as const,
  phone: '*********' as const,
  whatsapp: '************' as const,
  country: 'Cameroun' as const,
  shippingAddress: 'Cameroun | Douala | Douala bar,face station MRS' as const,
};

export const mockSepaBankDetails = {
  beneficiaryName: 'Brainstorm (Group) Holding Limited' as const,
  accountNumber: '**********************' as const,
  swiftCode: 'CHASIE4L' as const,
  bankName: 'J.P. MORGAN BANK LUXEMBOURG S.A., DUBLIN BRANCH' as const,
  address: '200 Capital Dock 79 Sir John Rogersons Quay Dublin 2 D02 RK57' as const,
  country: 'IE' as const,
  currency: 'EUR' as const,
  processingFee: '0%' as const,
};

export const mockGlobalBankDetails = {
  beneficiaryName: 'Brainstorm (Group) Holding Limited' as const,
  currencies: 'EUR, USD, HKD, GBP, CNH, CAD, SGD, JPY, AUD, NZD' as const,
  accountNumber: '***********' as const,
  swiftCode: 'CHASHKHH' as const,
  bankCode: '007' as const,
  branchCode: '863' as const,
  bankName: 'JPMorgan Chase Bank N.A., Hong Kong Branch' as const,
  bankAddress: '18/F, 20/F, 22-29/F, CHATER HOUSE, 8 CONNAUGHT ROAD CENTRAL, HONG KONG' as const,
};

export const mockOrderNumber = '**************' as const;
export const mockDefaultCountryCode = '+237' as const;