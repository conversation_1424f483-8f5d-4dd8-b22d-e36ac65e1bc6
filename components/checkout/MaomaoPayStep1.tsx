'use client';

// components/checkout/MaomaoPayStep1.tsx
// Refactored: Streamlined UI to focus on the primary action (entering a phone number).
// Combines all information into a single, clean card.

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface CustomerInfo {
  name: string;
}

interface MaomaoPayStep1Props {
  onBack: () => void;
  onConfirm: (phoneNumber: string, countryCode: string) => void;
  customerInfo: CustomerInfo;
  defaultCountryCode: string;
}

export function MaomaoPayStep1({
  onBack,
  onConfirm,
  customerInfo,
  defaultCountryCode,
}: MaomaoPayStep1Props) {
  const t = useTranslations('checkout');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [countryCode, setCountryCode] = useState(defaultCountryCode);

  const handleConfirm = () => {
    // Basic validation to ensure the phone number is not empty
    if (phoneNumber.trim()) {
      onConfirm(phoneNumber.trim(), countryCode);
    }
  };

  // A simple validation check to enable/disable the confirm button
  const isPhoneNumberValid = phoneNumber.trim().length >= 9;

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('payWithMobileMoney')}</CardTitle>
        <CardDescription>
          {t('enterPhoneNumber')}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Phone Number Input Group */}
        <div>
          <label htmlFor="phone" className="text-sm font-medium text-muted-foreground">
            {t('phoneNumber')}
          </label>
          <div className="mt-1 flex items-center rounded-md border">
            <Select value={countryCode} onValueChange={setCountryCode}>
              <SelectTrigger
                aria-label={t('phoneNumber')}
                className="w-24 border-0 border-r rounded-r-none focus:ring-0"
              >
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {/* Add more country codes as needed */}
                <SelectItem value="+237">+237</SelectItem>
                <SelectItem value="+33">+33</SelectItem>
                <SelectItem value="+1">+1</SelectItem>
              </SelectContent>
            </Select>
            <Input
              id="phone"
              type="tel"
              placeholder="e.g. 699887766"
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
              className="flex-1 border-0 rounded-l-none shadow-none focus-visible:ring-0"
            />
          </div>
        </div>

        {/* Displaying customer name for confirmation, without being obtrusive */}
        <div className="text-sm text-muted-foreground">
          {t('payingAs')} <span className="font-medium text-foreground">{customerInfo.name}</span>
        </div>
      </CardContent>
      <CardFooter className="flex flex-col sm:flex-row gap-3">
        <Button
          onClick={onBack}
          variant="outline"
          className="w-full sm:w-auto"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          {t('back')}
        </Button>
        <Button
          onClick={handleConfirm}
          disabled={!isPhoneNumberValid}
          className="w-full sm:flex-1"
        >
          {t('confirmPayment')}
        </Button>
      </CardFooter>
    </Card>
  );
}