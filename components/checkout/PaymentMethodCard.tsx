'use client';

import { cn } from '@/lib/utils';
import { Card } from '@/components/ui/card';
import type { ReactNode } from 'react';

interface PaymentMethodCardProps {
  id: string;
  icon: ReactNode;
  title: string;
  description: string;
  isSelected: boolean;
  onClick: () => void;
}

export function PaymentMethodCard({
  icon,
  title,
  description,
  isSelected,
  onClick,
}: PaymentMethodCardProps) {
  return (
    <Card
      className={cn(
        'flex items-center p-4 cursor-pointer transition-all border-2',
        'hover:bg-accent/50',
        isSelected
          ? 'border-primary bg-primary/5'
          : 'border-border'
      )}
      onClick={onClick}
    >
      <div className="flex items-center gap-4 w-full">
        <div className="text-3xl flex-shrink-0">
          {icon}
        </div>
        <div className="flex-1">
          <h3 className="font-semibold text-base">{title}</h3>
          <p className="text-sm text-muted-foreground">{description}</p>
        </div>
      </div>
    </Card>
  );
}