// components/ui/quantity-selector.tsx
'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Minus, Plus } from 'lucide-react';

interface QuantitySelectorProps {
  quantity: number;
  onChange: (quantity: number) => void;
  min?: number;
  max?: number;
  disabled?: boolean;
  size?: 'sm' | 'default';
}

export function QuantitySelector({
  quantity,
  onChange,
  min = 1,
  max = 10000,
  disabled = false,
  size = 'default'
}: QuantitySelectorProps) {
  const handleDecrement = () => {
    if (quantity > min && !disabled) {
      onChange(quantity - 1);
    }
  };

  const handleIncrement = () => {
    if (quantity < max && !disabled) {
      onChange(quantity + 1);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value) || min;
    const clampedValue = Math.max(min, Math.min(max, value));
    onChange(clampedValue);
  };

  const buttonSize = size === 'sm' ? 'h-7 w-7' : 'h-8 w-8';
  const inputSize = size === 'sm' ? 'w-12 h-7 text-xs' : 'w-12 h-8 text-sm';
  const iconSize = size === 'sm' ? 'h-3 w-3' : 'h-4 w-4';

  return (
    <div className="flex items-center gap-1">
      <Button
        variant="outline"
        size="icon"
        onClick={handleDecrement}
        disabled={quantity <= min || disabled}
        className={buttonSize}
      >
        <Minus className={iconSize} />
      </Button>
      <Input
        type="number"
        min={min}
        max={max}
        value={quantity}
        onChange={handleInputChange}
        className={`text-center ${inputSize}`}
        disabled={disabled}
      />
      <Button
        variant="outline"
        size="icon"
        onClick={handleIncrement}
        disabled={quantity >= max || disabled}
        className={buttonSize}
      >
        <Plus className={iconSize} />
      </Button>
    </div>
  );
}