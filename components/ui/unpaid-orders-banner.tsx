'use client';

import { useTranslations } from 'next-intl';
import { AlertTriangle, X } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { useState } from 'react';
import { useUnpaidOrdersContext } from '@/components/providers/UnpaidOrdersProvider';

interface UnpaidOrdersBannerProps {
  locale: string;
  className?: string;
}

export function UnpaidOrdersBanner({ locale, className }: UnpaidOrdersBannerProps) {
  const t = useTranslations('account');
  const { count: unpaidCount } = useUnpaidOrdersContext();
  const [dismissed, setDismissed] = useState(false);

  if (dismissed || unpaidCount === 0) return null;

  const message = t('unpaidOrdersReminder', { count: unpaidCount });

  return (
    <div className={`w-full ${className}`}>
      <Alert className="border-amber-200 bg-amber-50 text-amber-800 dark:border-amber-800 dark:bg-amber-950 dark:text-amber-200 rounded-none border-x-0 border-t-0">
        <AlertTriangle className="h-4 w-4 flex-shrink-0" />
        <AlertDescription className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-4">
          <span className="flex-1 text-xs text-center sm:text-left sm:text-sm">{message}</span>
          <div className="flex items-center justify-center sm:justify-end gap-2 flex-shrink-0">
            <Button asChild size="sm" variant="outline" className="border-amber-300 text-amber-800 hover:bg-amber-100 dark:border-amber-700 dark:text-amber-200 dark:hover:bg-amber-900">
              <Link href={`/${locale}/account/orders`}>
                {t('viewUnpaidOrders')}
              </Link>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setDismissed(true)}
              className="h-8 w-8 p-0 text-amber-600 hover:bg-amber-100 dark:text-amber-400 dark:hover:bg-amber-900"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </AlertDescription>
      </Alert>
    </div>
  );
}