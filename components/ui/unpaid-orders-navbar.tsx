'use client';

import { useTranslations } from 'next-intl';
import { AlertTriangle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';
import { useUnpaidOrdersContext } from '@/components/providers/UnpaidOrdersProvider';

interface UnpaidOrdersNavbarProps {
  locale: string;
}

export function UnpaidOrdersNavbar({ locale }: UnpaidOrdersNavbarProps) {
  const t = useTranslations('account');
  const { count: unpaidCount, isLoading } = useUnpaidOrdersContext();

  if (isLoading || unpaidCount === 0) return null;

  return (
    <Link
      href={`/${locale}/account/orders`}
      className="flex items-center gap-2 px-3 py-2 rounded-md text-sm bg-amber-50 text-amber-800 border border-amber-200 hover:bg-amber-100 dark:bg-amber-950 dark:text-amber-200 dark:border-amber-800 dark:hover:bg-amber-900 transition-colors"
    >
      <AlertTriangle className="h-4 w-4" />
      <span className="hidden sm:inline">{t('unpaidOrders')}</span>
      <Badge variant="secondary" className="bg-amber-200 text-amber-900 dark:bg-amber-800 dark:text-amber-100">
        {unpaidCount}
      </Badge>
    </Link>
  );
}