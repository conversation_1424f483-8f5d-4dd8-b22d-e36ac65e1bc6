'use client';

import { useRouter } from 'next/navigation';
import { useLoading } from '@/components/providers/LoadingProvider';
import { cn } from '@/lib/utils';

interface LoadingLinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
  onClick?: (e: React.MouseEvent<HTMLAnchorElement>) => void;
  prefetch?: boolean;
  replace?: boolean;
  scroll?: boolean;
  shallow?: boolean;
}

export function LoadingLink({
  href,
  children,
  className,
  onClick,
  prefetch = true,
  replace = false,
  scroll = true,
  shallow = false,
  ...props
}: LoadingLinkProps & React.AnchorHTMLAttributes<HTMLAnchorElement>) {
  const router = useRouter();
  const { setLoading } = useLoading();

  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    // Don't set loading for external links or same page anchors
    if (href.startsWith('http') || href.startsWith('#') || href === window.location.pathname) {
      onClick?.(e);
      return;
    }

    // Prevent default navigation
    e.preventDefault();

    // Set loading state
    setLoading(true);

    // Call custom onClick if provided
    onClick?.(e);

    // Navigate programmatically
    if (replace) {
      router.replace(href, { scroll });
    } else {
      router.push(href, { scroll });
    }

    // Note: Loading will be cleared by the page change or by a timeout in the provider
  };

  return (
    <a
      href={href}
      className={cn('cursor-pointer', className)}
      onClick={handleClick}
      {...props}
    >
      {children}
    </a>
  );
}