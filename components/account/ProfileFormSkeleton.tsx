'use client';

// components/account/ProfileFormSkeleton.tsx
// Skeleton loader for profile form

import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

export function ProfileFormSkeleton() {
  return (
    <div className="space-y-6">
      {/* Email field skeleton */}
      <div className="space-y-3">
        <Skeleton className="h-5 w-16" />
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-4 w-48" />
      </div>

      {/* Name and phone fields skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-3">
          <Skeleton className="h-5 w-20" />
          <Skeleton className="h-10 w-full" />
        </div>
        <div className="space-y-3">
          <Skeleton className="h-5 w-16" />
          <Skeleton className="h-10 w-full" />
        </div>
      </div>

      {/* Currency field skeleton */}
      <div className="space-y-3">
        <Skeleton className="h-5 w-32" />
        <Skeleton className="h-10 w-full" />
      </div>

      {/* Save button skeleton */}
      <div className="flex justify-end pt-4">
        <Skeleton className="h-10 w-32" />
      </div>
    </div>
  );
}

export function ProfileCardSkeleton() {
  return (
    <Card className="border-0 shadow-sm">
      <CardHeader className="pb-4">
        <Skeleton className="h-7 w-48" />
        <Skeleton className="h-4 w-64 mt-1" />
      </CardHeader>
      <CardContent>
        <ProfileFormSkeleton />
      </CardContent>
    </Card>
  );
}