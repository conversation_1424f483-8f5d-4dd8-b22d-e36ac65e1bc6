'use client';

// components/account/ProfileForm.tsx
// Profile editing form with SWR optimistic updates

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useCurrency } from '@/components/providers/CurrencyProvider';
import { useProfile } from '@/hooks/queries';
import { CURRENCIES } from '@/lib/constants';
import type { UserProfile } from '@/lib/types';
import { ProfileFormSkeleton } from './ProfileFormSkeleton';

interface ProfileFormProps {
  locale: string;
}

export function ProfileForm({ locale }: ProfileFormProps) {
  const t = useTranslations('account');
  const { setCurrency } = useCurrency();

  // Use SWR hook for profile data and optimistic updates
  const { data: user, isLoading, error, updateProfile: updateProfileOptimistic } = useProfile();

  const [formData, setFormData] = useState({
    fullName: '',
    phone: '',
    preferredCurrency: 'USD',
  });
  const [isUpdating, setIsUpdating] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  // Sync form data with user data when it loads
  useEffect(() => {
    if (user) {
      setFormData({
        fullName: user.fullName || '',
        phone: user.phone || '',
        preferredCurrency: user.preferredCurrency || 'USD',
      });
    }
  }, [user]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setFormData((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) return;

    setIsUpdating(true);
    setMessage(null);

    // Store original currency for rollback
    const originalCurrency = user.preferredCurrency || 'USD';

    try {
      // Update global currency state immediately (optimistic)
      if (formData.preferredCurrency !== user.preferredCurrency) {
        setCurrency(formData.preferredCurrency);
      }

      // Optimistic update via SWR
      await updateProfileOptimistic(formData);

      // Success message will be shown by the hook
      setMessage({ type: 'success', text: t('profileUpdated') });
    } catch (error) {
      // Rollback currency change on error
      if (formData.preferredCurrency !== originalCurrency) {
        setCurrency(originalCurrency);
      }
      setMessage({ type: 'error', text: t('updateError') });
    } finally {
      setIsUpdating(false);
    }
  };

  // Show loading skeleton while data is being fetched
  if (isLoading) {
    return <ProfileFormSkeleton />;
  }

  // Show error state
  if (error) {
    return (
      <div className="p-4 rounded-lg text-sm font-medium bg-red-50 text-red-900 border border-red-200 dark:bg-red-950 dark:text-red-100 dark:border-red-800">
        {t('loadError')}
      </div>
    );
  }

  // Show empty state if no user data
  if (!user) {
    return (
      <div className="p-4 rounded-lg text-sm font-medium bg-yellow-50 text-yellow-900 border border-yellow-200 dark:bg-yellow-950 dark:text-yellow-100 dark:border-yellow-800">
        {t('noProfileData')}
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {message && (
        <div
          className={`p-4 rounded-lg text-sm font-medium animate-in fade-in slide-in-from-top-2 duration-300 ${
            message.type === 'success'
              ? 'bg-emerald-50 text-emerald-900 border border-emerald-200 dark:bg-emerald-950 dark:text-emerald-100 dark:border-emerald-800'
              : 'bg-red-50 text-red-900 border border-red-200 dark:bg-red-950 dark:text-red-100 dark:border-red-800'
          }`}
        >
          {message.text}
        </div>
      )}

      <div className="space-y-3">
        <Label htmlFor="email" className="text-sm font-semibold text-foreground">{t('email')}</Label>
        <Input
          id="email"
          type="email"
          value={user.email}
          disabled
          className="bg-muted/50 border-muted cursor-not-allowed"
        />
        <p className="text-xs text-muted-foreground">{t('emailCannotChange')}</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-3">
          <Label htmlFor="fullName" className="text-sm font-semibold text-foreground">{t('fullName')}</Label>
          <Input
            id="fullName"
            name="fullName"
            type="text"
            value={formData.fullName}
            onChange={handleChange}
            required
            disabled={isUpdating}
            className="transition-all duration-200"
          />
        </div>

        <div className="space-y-3">
          <Label htmlFor="phone" className="text-sm font-semibold text-foreground">{t('phone')}</Label>
          <Input
            id="phone"
            name="phone"
            type="tel"
            value={formData.phone}
            onChange={handleChange}
            placeholder="+1234567890"
            disabled={isUpdating}
            className="transition-all duration-200"
          />
        </div>
      </div>

      <div className="space-y-3">
        <Label htmlFor="preferredCurrency" className="text-sm font-semibold text-foreground">{t('preferredCurrency')}</Label>
        <select
          id="preferredCurrency"
          name="preferredCurrency"
          value={formData.preferredCurrency}
          onChange={handleChange}
          disabled={isUpdating}
          className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200"
        >
          {Object.entries(CURRENCIES).map(([code, info]) => (
            <option key={code} value={code}>
              {info.symbol} {info.name} ({code})
            </option>
          ))}
        </select>
      </div>

      <div className="flex justify-end pt-4">
        <Button
          type="submit"
          disabled={isUpdating}
          className="px-8 transition-all duration-200"
        >
          {isUpdating ? t('saving') : t('saveChanges')}
        </Button>
      </div>
    </form>
  );
}

