'use client';

// components/account/PreviouslyBoughtItem.tsx
// Previously bought item with reorder functionality using React Query mutations

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { Heart, RotateCcw, Package, Loader2 } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ImageWithFallback } from '@/components/ui/image-with-fallback';
import { useAddToCart } from '@/hooks/mutations/useAddToCart';
import { useAddToWishlist } from '@/hooks/mutations/useAddToWishlist';
import { formatCurrency } from '@/lib/utils';
import type { WishlistItem as WishlistItemType } from '@/lib/actions/wishlist.actions';

interface PreviouslyBoughtItemProps {
  item: WishlistItemType;
  locale: string;
  viewMode: 'grid' | 'list';
}

export function PreviouslyBoughtItem({ item, locale, viewMode }: PreviouslyBoughtItemProps) {
  const t = useTranslations('account');
  const tProducts = useTranslations('products');

  // React Query mutations with optimistic updates
  const addToCartMutation = useAddToCart();
  const addToWishlistMutation = useAddToWishlist();

  const productName = item.product.translations[0]?.name || item.product.originalName || 'Product';
  const productSlug = item.product.translations[0]?.slug || `product-${item.productId}`;
  const imageUrl = item.product.productImages[0]?.imageUrl;
  const currentPrice = item.currentPrice;

  const handleReorder = () => {
    addToCartMutation.mutate({
      productId: item.productId,
      variantId: item.variantId,
      quantity: 1,
    });
  };

  const handleAddToWishlist = () => {
    addToWishlistMutation.mutate({
      productId: item.productId,
      variantId: item.variantId,
    });
  };

  if (viewMode === 'list') {
    return (
      <Card className="hover:shadow-md transition-shadow">
        <CardContent className="p-4">
          <div className="flex gap-4">
            {/* Image */}
            <div className="relative w-20 h-20 flex-shrink-0 rounded-md overflow-hidden bg-muted">
              {imageUrl ? (
                <ImageWithFallback
                  src={imageUrl}
                  alt={productName}
                  className="object-cover"
                  sizes="80px"
                  fallbackText="No image"
                />
              ) : (
                <div className="flex h-full items-center justify-center text-xs text-muted-foreground">
                  {t('noImage')}
                </div>
              )}
              
              {/* Previously bought badge */}
              <Badge variant="secondary" className="absolute bottom-1 left-1 text-xs">
                <Package className="h-3 w-3 mr-1" />
                {t('bought')}
              </Badge>
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <Link href={`/${locale}/products/${productSlug}`}>
                    <h3 className="font-semibold text-sm hover:text-primary transition-colors line-clamp-2">
                      {productName}
                    </h3>
                  </Link>
                  
                  {item.variant && (
                    <p className="text-xs text-muted-foreground mt-1">
                      {item.variant.originalVariantType}: {item.variant.originalVariantName}
                    </p>
                  )}

                  <div className="flex items-center gap-2 mt-2">
                    <span className="font-bold text-sm">
                      {formatCurrency(currentPrice, item.currentCurrency)}
                    </span>
                    
                    {item.priceWhenAdded !== currentPrice && (
                      <span className="text-xs text-muted-foreground line-through">
                        {formatCurrency(item.priceWhenAdded, item.currencyWhenAdded)}
                      </span>
                    )}
                  </div>

                  {item.notes && (
                    <p className="text-xs text-muted-foreground mt-1">
                      {item.notes}
                    </p>
                  )}
                </div>

                {/* Actions */}
                <div className="flex items-center gap-1 ml-4">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleReorder}
                    disabled={addToCartMutation.isPending}
                  >
                    {addToCartMutation.isPending ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <RotateCcw className="h-4 w-4" />
                    )}
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={handleAddToWishlist}
                    disabled={addToWishlistMutation.isPending}
                  >
                    {addToWishlistMutation.isPending ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Heart className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Grid view
  return (
    <Card className="group hover:shadow-lg transition-all duration-200">
      <CardContent className="p-4">
        {/* Image */}
        <div className="relative aspect-square mb-4 rounded-md overflow-hidden bg-muted">
          {imageUrl ? (
            <ImageWithFallback
              src={imageUrl}
              alt={productName}
              className="object-cover group-hover:scale-105 transition-transform duration-200"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              fallbackText="No image"
            />
          ) : (
            <div className="flex h-full items-center justify-center text-muted-foreground">
              {t('noImage')}
            </div>
          )}

          {/* Previously bought badge */}
          <Badge variant="secondary" className="absolute top-2 right-2 text-xs">
            <Package className="h-3 w-3 mr-1" />
            {t('bought')}
          </Badge>

          {/* Add to wishlist button */}
          <Button
            size="sm"
            variant="ghost"
            className="absolute top-2 left-2 h-8 w-8 p-0 bg-white/80 hover:bg-white"
            onClick={handleAddToWishlist}
            disabled={addToWishlistMutation.isPending}
          >
            {addToWishlistMutation.isPending ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Heart className="h-4 w-4" />
            )}
          </Button>
        </div>

        {/* Content */}
        <div className="space-y-3">
          <div>
            <Link href={`/${locale}/products/${productSlug}`}>
              <h3 className="font-semibold text-sm hover:text-primary transition-colors line-clamp-2">
                {productName}
              </h3>
            </Link>
            
            {item.variant && (
              <p className="text-xs text-muted-foreground mt-1">
                {item.variant.originalVariantType}: {item.variant.originalVariantName}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="font-bold">
                {formatCurrency(currentPrice, item.currentCurrency)}
              </span>
              {item.priceWhenAdded !== currentPrice && (
                <span className="text-xs text-muted-foreground line-through">
                  {formatCurrency(item.priceWhenAdded, item.currencyWhenAdded)}
                </span>
              )}
            </div>

            {item.notes && (
              <p className="text-xs text-muted-foreground">
                {item.notes}
              </p>
            )}

            <div className="flex gap-2">
              <Button
                size="sm"
                className="flex-1"
                onClick={handleReorder}
                disabled={addToCartMutation.isPending}
              >
                {addToCartMutation.isPending ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <RotateCcw className="h-4 w-4 mr-2" />
                )}
                {addToCartMutation.isPending ? t('adding') : t('reorder')}
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
