'use client';

// components/account/AddressList.tsx
// Address management component with SWR optimistic updates

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { Plus, Trash2, Edit } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { DeleteConfirmationDialog } from '@/components/ui/delete-confirmation-dialog';
import { useAddresses } from '@/hooks/queries';
import { AddressForm } from './AddressForm';
import type { Address } from '@/lib/types';

interface AddressListProps {
  locale: string;
}

export function AddressList({ locale }: AddressListProps) {
  const t = useTranslations('account');
  const tCheckout = useTranslations('checkout');

  // Use SWR hook for addresses data and optimistic operations
  const { data: addresses, isLoading, error, deleteAddress: deleteAddressOptimistic } = useAddresses();

  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingAddress, setEditingAddress] = useState<Address | undefined>(undefined);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [addressToDelete, setAddressToDelete] = useState<string | null>(null);

  const handleDeleteClick = (addressId: string) => {
    setAddressToDelete(addressId);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!addressToDelete) return;

    setDeletingId(addressToDelete);

    try {
      // Use optimistic delete from SWR hook
      await deleteAddressOptimistic(addressToDelete);
      setDeleteDialogOpen(false);
      setAddressToDelete(null);
    } catch (error) {
      // Error handling is done by the hook
      console.error('Failed to delete address:', error);
    } finally {
      setDeletingId(null);
    }
  };

  const handleAdd = () => {
    setEditingAddress(undefined);
    setIsDialogOpen(true);
  };

  const handleEdit = (address: Address) => {
    setEditingAddress(address);
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setEditingAddress(undefined);
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Array.from({ length: 2 }).map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="p-5 border border-gray-200 rounded-lg bg-gray-50 dark:bg-gray-800 dark:border-gray-700">
                <div className="space-y-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="h-5 w-16 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="h-4 w-24 bg-gray-300 dark:bg-gray-600 rounded"></div>
                    <div className="h-4 w-20 bg-gray-300 dark:bg-gray-600 rounded"></div>
                    <div className="h-4 w-16 bg-gray-300 dark:bg-gray-600 rounded"></div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="text-center py-12 px-4 rounded-lg border border-red-200 bg-red-50 dark:bg-red-950 dark:border-red-800">
        <p className="text-red-600 dark:text-red-400 font-medium">{t('loadError')}</p>
      </div>
    );
  }

  const addressList = addresses || [];

  return (
    <div className="space-y-6">
      {addressList.length === 0 ? (
        <div className="text-center py-12 px-4 rounded-lg border border-dashed border-muted-foreground/20 bg-muted/30">
          <p className="text-muted-foreground font-medium">{t('noAddresses')}</p>
          <p className="text-sm text-muted-foreground mt-1">Add your first address to get started</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {addressList.map((address, index) => (
            <div
              key={address.id}
              className="animate-in fade-in slide-in-from-bottom-2 duration-500"
              style={{ animationDelay: `${index * 50}ms` }}
            >
              <Card className="p-5 border-0 shadow-sm hover:shadow-md transition-all duration-300 group">
                <div className="space-y-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      {address.isDefault && (
                        <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-100 mb-2">
                          {t('default')}
                        </span>
                      )}
                    </div>
                    <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleEdit(address)}
                        disabled={deletingId === address.id}
                        className="h-8 w-8 hover:bg-muted"
                      >
                        <Edit className="h-4 w-4 text-muted-foreground hover:text-foreground" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDeleteClick(address.id)}
                        disabled={deletingId === address.id}
                        className="h-8 w-8 hover:bg-red-50 dark:hover:bg-red-950"
                      >
                        <Trash2 className="h-4 w-4 text-destructive" />
                      </Button>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <p className="font-semibold text-foreground text-base">{address.fullName}</p>
                    <p className="text-sm text-muted-foreground flex items-center">
                      <span className="mr-2">📞</span>
                      {address.phone}
                    </p>
                    {address.isWhatsApp && (
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-100">
                        ✓ WhatsApp
                      </span>
                    )}
                  </div>
                </div>
              </Card>
            </div>
          ))}
        </div>
      )}

      <Button
        variant="outline"
        className="w-full h-11 border-dashed hover:bg-muted transition-colors duration-200"
        onClick={handleAdd}
      >
        <Plus className="h-4 w-4 mr-2" />
        {t('addNewAddress')}
      </Button>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {editingAddress ? t('editAddress') : t('addNewAddress')}
            </DialogTitle>
          </DialogHeader>
          <AddressForm
            address={editingAddress}
            onClose={handleCloseDialog}
            locale={locale}
          />
        </DialogContent>
      </Dialog>

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleConfirmDelete}
        isLoading={deletingId !== null}
      />
    </div>
  );
}

