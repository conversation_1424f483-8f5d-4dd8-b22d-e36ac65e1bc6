'use client';

// components/account/AccountNav.tsx
// Account sidebar navigation

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Home, Package, User, Heart } from 'lucide-react';
import { cn } from '@/lib/utils';

interface AccountNavProps {
  locale: string;
}

export function AccountNav({ locale }: AccountNavProps) {
  const t = useTranslations('account');
  const pathname = usePathname();

  const navItems = [
    {
      href: `/${locale}/account`,
      label: t('dashboard'),
      icon: Home,
    },
    {
      href: `/${locale}/account/orders`,
      label: t('orders'),
      icon: Package,
    },
    {
      href: `/${locale}/account/favorites`,
      label: t('favorites'),
      icon: Heart,
    },
    {
      href: `/${locale}/account/profile`,
      label: t('profile'),
      icon: User,
    },
  ];

  return (
    <nav className="flex flex-row justify-center border-b lg:flex-col lg:justify-start lg:border-b-0 lg:space-y-2">
      {navItems.map((item) => {
        const Icon = item.icon;
        const isActive = pathname === item.href;

        return (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              'flex items-center justify-center gap-2 px-4 py-3 lg:gap-3 lg:px-4 lg:py-3 rounded-none lg:rounded-lg transition-colors border-b-2 lg:border-b-0 whitespace-nowrap',
              isActive
                ? 'border-primary text-primary lg:bg-primary lg:text-primary-foreground lg:border-b-0'
                : 'border-transparent hover:bg-muted lg:border-transparent lg:hover:bg-muted'
            )}
          >
            <Icon className="hidden lg:block h-5 w-5" />
            <span className="font-medium">{item.label}</span>
          </Link>
        );
      })}
    </nav>
  );
}

