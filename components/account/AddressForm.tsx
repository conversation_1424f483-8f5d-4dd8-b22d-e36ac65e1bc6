'use client';

// components/account/AddressForm.tsx
// Form for adding/editing addresses with SWR optimistic updates

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { useAddresses } from '@/hooks/queries';
import type { Address } from '@/lib/types';

interface AddressFormProps {
  address?: Address;
  onClose: () => void;
  locale: string;
}

export function AddressForm({ address, onClose, locale }: AddressFormProps) {
  const t = useTranslations('account');
  const isEditing = !!address;

  // Use SWR hook for optimistic address operations
  const { addAddress: addAddressOptimistic, updateAddress: updateAddressOptimistic } = useAddresses();

  const [formData, setFormData] = useState({
    fullName: address?.fullName || '',
    phone: address?.phone || '',
    isWhatsApp: address?.isWhatsApp || false,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setMessage(null);

    try {
      const addressData = {
        fullName: formData.fullName,
        phone: formData.phone,
        isWhatsApp: formData.isWhatsApp,
        addressLine1: 'TBD',
        addressLine2: '',
        city: 'TBD',
        state: '',
        postalCode: '',
        country: 'TBD',
        isDefault: isEditing ? address!.isDefault || false : false,
      };

      if (isEditing) {
        // Use optimistic update for editing
        await updateAddressOptimistic(address!.id, addressData);
        setMessage({ type: 'success', text: t('addressUpdated') });
      } else {
        // Use optimistic add for new addresses
        await addAddressOptimistic(addressData);
        setMessage({ type: 'success', text: t('addressAdded') });
      }

      // Close dialog after successful operation (delay for user to see success message)
      setTimeout(() => {
        onClose();
      }, 1000);
    } catch (error) {
      setMessage({ type: 'error', text: t('saveError') });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {message && (
        <div
          className={`p-4 rounded-lg text-sm font-medium animate-in fade-in slide-in-from-top-2 duration-300 ${
            message.type === 'success'
              ? 'bg-emerald-50 text-emerald-900 border border-emerald-200 dark:bg-emerald-950 dark:text-emerald-100 dark:border-emerald-800'
              : 'bg-red-50 text-red-900 border border-red-200 dark:bg-red-950 dark:text-red-100 dark:border-red-800'
          }`}
        >
          {message.text}
        </div>
      )}

      <div className="space-y-4 bg-muted/30 p-4 rounded-lg border border-muted">
        <p className="text-sm font-semibold text-foreground">Contact Information</p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <Label htmlFor="fullName" className="text-sm font-semibold text-foreground">
              Full Name <span className="text-destructive">*</span>
            </Label>
            <Input
              id="fullName"
              name="fullName"
              type="text"
              value={formData.fullName}
              onChange={handleChange}
              required
              disabled={isLoading}
              placeholder="John Doe"
              className="transition-all duration-200"
            />
          </div>

          <div className="space-y-3">
            <Label htmlFor="phone" className="text-sm font-semibold text-foreground">
              Phone Number <span className="text-destructive">*</span>
            </Label>
            <Input
              id="phone"
              name="phone"
              type="tel"
              value={formData.phone}
              onChange={handleChange}
              required
              disabled={isLoading}
              placeholder="+****************"
              className="transition-all duration-200"
            />
          </div>
        </div>

        <div className="flex items-center space-x-3 pt-2">
          <Checkbox
            id="isWhatsApp"
            name="isWhatsApp"
            checked={formData.isWhatsApp}
            onCheckedChange={(checked) =>
              setFormData((prev) => ({ ...prev, isWhatsApp: checked as boolean }))
            }
            disabled={isLoading}
            className="transition-all duration-200"
          />
          <Label htmlFor="isWhatsApp" className="text-sm font-medium cursor-pointer">
            This phone number is my WhatsApp number
          </Label>
        </div>
      </div>

      <div className="flex justify-end space-x-3 pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={onClose}
          disabled={isLoading}
          className="px-6 transition-all duration-200"
        >
          {t('cancel')}
        </Button>
        <Button
          type="submit"
          disabled={isLoading}
          className="px-6 transition-all duration-200"
        >
          {isLoading ? t('saving') : isEditing ? t('saveChanges') : t('addAddress')}
        </Button>
      </div>
    </form>
  );
}