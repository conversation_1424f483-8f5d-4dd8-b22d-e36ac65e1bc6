'use client';

// components/account/EmptyWishlist.tsx
// Empty state for wishlist with promotional content

import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { Heart, ShoppingBag, Star, TrendingUp } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface EmptyWishlistProps {
  locale: string;
}

export function EmptyWishlist({ locale }: EmptyWishlistProps) {
  const t = useTranslations('account');
  const tProducts = useTranslations('products');

  const benefits = [
    {
      icon: Heart,
      title: t('saveForLater'),
      description: t('saveForLaterDesc'),
    },
    {
      icon: TrendingUp,
      title: t('trackPrices'),
      description: t('trackPricesDesc'),
    },
    {
      icon: Star,
      title: t('quickReorder'),
      description: t('quickReorderDesc'),
    },
  ];

  return (
    <div className="space-y-8">
      {/* Main empty state */}
      <Card>
        <CardContent className="text-center py-16">
          <div className="max-w-md mx-auto">
            <div className="relative mb-6">
              <div className="w-24 h-24 mx-auto bg-muted rounded-full flex items-center justify-center">
                <Heart className="h-12 w-12 text-muted-foreground" />
              </div>
              <div className="absolute -top-2 -right-2 w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-bold">0</span>
              </div>
            </div>
            
            <h2 className="text-2xl font-bold mb-4">{t('emptyWishlistTitle')}</h2>
            <p className="text-muted-foreground mb-8">
              {t('emptyWishlistDescription')}
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href={`/${locale}/products`}>
                <Button size="lg" className="w-full sm:w-auto">
                  <ShoppingBag className="h-5 w-5 mr-2" />
                  {t('startShopping')}
                </Button>
              </Link>
              <Link href={`/${locale}/products?featured=true`}>
                <Button variant="outline" size="lg" className="w-full sm:w-auto">
                  <Star className="h-5 w-5 mr-2" />
                  {tProducts('featuredProducts')}
                </Button>
              </Link>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Benefits section */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {benefits.map((benefit, index) => {
          const Icon = benefit.icon;
          return (
            <Card key={index} className="text-center">
              <CardContent className="p-6">
                <div className="w-12 h-12 mx-auto mb-4 bg-primary/10 rounded-full flex items-center justify-center">
                  <Icon className="h-6 w-6 text-primary" />
                </div>
                <h3 className="font-semibold mb-2">{benefit.title}</h3>
                <p className="text-sm text-muted-foreground">{benefit.description}</p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* How to use wishlist */}
      <Card className="bg-gradient-to-r from-primary/5 to-secondary/5">
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold mb-4">{t('howToUseWishlist')}</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-0.5">
                  1
                </div>
                <div>
                  <p className="font-medium">{t('step1Title')}</p>
                  <p className="text-sm text-muted-foreground">{t('step1Desc')}</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-0.5">
                  2
                </div>
                <div>
                  <p className="font-medium">{t('step2Title')}</p>
                  <p className="text-sm text-muted-foreground">{t('step2Desc')}</p>
                </div>
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-0.5">
                  3
                </div>
                <div>
                  <p className="font-medium">{t('step3Title')}</p>
                  <p className="text-sm text-muted-foreground">{t('step3Desc')}</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-0.5">
                  4
                </div>
                <div>
                  <p className="font-medium">{t('step4Title')}</p>
                  <p className="text-sm text-muted-foreground">{t('step4Desc')}</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
