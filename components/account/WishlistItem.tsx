'use client';

// components/account/WishlistItem.tsx
// Individual wishlist item with ordering promotion features using React Query mutations

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { Heart, ShoppingCart, TrendingDown, TrendingUp, Loader2 } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ImageWithFallback } from '@/components/ui/image-with-fallback';
import { useAddToCart } from '@/hooks/mutations/useAddToCart';
import { useRemoveFromWishlist } from '@/hooks/mutations/useRemoveFromWishlist';
import { formatCurrency, formatDate } from '@/lib/utils';
import type { WishlistItem as WishlistItemType } from '@/lib/actions/wishlist.actions';

interface WishlistItemProps {
  item: WishlistItemType;
  locale: string;
  viewMode: 'grid' | 'list';
}

export function WishlistItem({ item, locale, viewMode }: WishlistItemProps) {
  const t = useTranslations('account');
  const tProducts = useTranslations('products');

  // React Query mutations with optimistic updates
  const addToCartMutation = useAddToCart();
  const removeFromWishlistMutation = useRemoveFromWishlist();

  const productName = item.product.translations[0]?.name || item.product.originalName || 'Product';
  const productSlug = item.product.translations[0]?.slug || `product-${item.productId}`;
  const imageUrl = item.product.productImages[0]?.imageUrl;
  const currentPrice = item.currentPrice;
  const priceChange = currentPrice - item.priceWhenAdded;
  const priceChangePercent = item.priceWhenAdded > 0 ? (priceChange / item.priceWhenAdded) * 100 : 0;

  const handleRemoveFromWishlist = () => {
    removeFromWishlistMutation.mutate({
      productId: item.productId,
      variantId: item.variantId,
    });
  };

  const handleAddToCart = () => {
    addToCartMutation.mutate({
      productId: item.productId,
      variantId: item.variantId,
      quantity: 1,
    });
  };

  if (viewMode === 'list') {
    return (
      <Card className="hover:shadow-md transition-shadow">
        <CardContent className="p-4">
          <div className="flex gap-4">
            {/* Image */}
            <div className="relative w-20 h-20 flex-shrink-0 rounded-md overflow-hidden bg-muted">
              {imageUrl ? (
                <ImageWithFallback
                  src={imageUrl}
                  alt={productName}
                  className="object-cover"
                  sizes="80px"
                  fallbackText="No image"
                />
              ) : (
                <div className="flex h-full items-center justify-center text-xs text-muted-foreground">
                  {t('noImage')}
                </div>
              )}
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <Link href={`/${locale}/products/${productSlug}`}>
                    <h3 className="font-semibold text-sm hover:text-primary transition-colors line-clamp-2">
                      {productName}
                    </h3>
                  </Link>
                  
                  {item.variant && (
                    <p className="text-xs text-muted-foreground mt-1">
                      {item.variant.originalVariantType}: {item.variant.originalVariantName}
                    </p>
                  )}

                  <div className="flex items-center gap-2 mt-2">
                    <span className="font-bold text-sm">
                      {formatCurrency(currentPrice, item.currentCurrency)}
                    </span>
                    
                    {priceChange !== 0 && (
                      <Badge variant={priceChange < 0 ? 'default' : 'secondary'} className="text-xs">
                        {priceChange < 0 ? (
                          <TrendingDown className="h-3 w-3 mr-1" />
                        ) : (
                          <TrendingUp className="h-3 w-3 mr-1" />
                        )}
                        {priceChangePercent > 0 ? '+' : ''}{priceChangePercent.toFixed(1)}%
                      </Badge>
                    )}
                  </div>

                  <p className="text-xs text-muted-foreground mt-1">
                    {t('addedOn')} {formatDate(item.createdAt)}
                  </p>
                </div>

                {/* Actions */}
                <div className="flex items-center gap-1 ml-4">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleAddToCart}
                    disabled={addToCartMutation.isPending}
                  >
                    {addToCartMutation.isPending ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <ShoppingCart className="h-4 w-4" />
                    )}
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={handleRemoveFromWishlist}
                    disabled={removeFromWishlistMutation.isPending}
                  >
                    {removeFromWishlistMutation.isPending ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Heart className="h-4 w-4 text-red-500 fill-current" />
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Grid view
  return (
    <Card className="group hover:shadow-lg transition-all duration-200">
      <CardContent className="p-4">
        {/* Image */}
        <div className="relative aspect-square mb-4 rounded-md overflow-hidden bg-muted">
          {imageUrl ? (
            <ImageWithFallback
              src={imageUrl}
              alt={productName}
              className="object-cover group-hover:scale-105 transition-transform duration-200"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              fallbackText="No image"
            />
          ) : (
            <div className="flex h-full items-center justify-center text-muted-foreground">
              {t('noImage')}
            </div>
          )}

          {/* Price change badge */}
          {priceChange !== 0 && (
            <Badge 
              variant={priceChange < 0 ? 'default' : 'secondary'} 
              className="absolute top-2 right-2 text-xs"
            >
              {priceChange < 0 ? (
                <TrendingDown className="h-3 w-3 mr-1" />
              ) : (
                <TrendingUp className="h-3 w-3 mr-1" />
              )}
              {priceChangePercent > 0 ? '+' : ''}{priceChangePercent.toFixed(1)}%
            </Badge>
          )}

          {/* Remove button */}
          <Button
            size="sm"
            variant="ghost"
            className="absolute top-2 left-2 h-8 w-8 p-0 bg-white/80 hover:bg-white"
            onClick={handleRemoveFromWishlist}
            disabled={removeFromWishlistMutation.isPending}
          >
            {removeFromWishlistMutation.isPending ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Heart className="h-4 w-4 text-red-500 fill-current" />
            )}
          </Button>
        </div>

        {/* Content */}
        <div className="space-y-3">
          <div>
            <Link href={`/${locale}/products/${productSlug}`}>
              <h3 className="font-semibold text-sm hover:text-primary transition-colors line-clamp-2">
                {productName}
              </h3>
            </Link>
            
            {item.variant && (
              <p className="text-xs text-muted-foreground mt-1">
                {item.variant.originalVariantType}: {item.variant.originalVariantName}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="font-bold">
                {formatCurrency(currentPrice, item.currentCurrency)}
              </span>
              <span className="text-xs text-muted-foreground">
                {formatDate(item.createdAt)}
              </span>
            </div>

            <Button
              size="sm"
              className="w-full"
              onClick={handleAddToCart}
              disabled={addToCartMutation.isPending}
            >
              {addToCartMutation.isPending ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <ShoppingCart className="h-4 w-4 mr-2" />
              )}
              {addToCartMutation.isPending ? t('adding') : tProducts('addToCart')}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
