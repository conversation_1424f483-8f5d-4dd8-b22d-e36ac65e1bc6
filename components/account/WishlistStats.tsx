'use client';

// components/account/WishlistStats.tsx
// Statistics cards for wishlist overview

import { useTranslations } from 'next-intl';
import { Heart, DollarSign, TrendingUp } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { formatCurrency } from '@/lib/utils';

interface WishlistStatsProps {
  totalItems: number;
  totalValue: number;
  avgPrice: number;
  currency: string;
}

export function WishlistStats({ totalItems, totalValue, avgPrice, currency }: WishlistStatsProps) {
  const t = useTranslations('account');

  const stats = [
    {
      label: t('totalItems'),
      value: totalItems.toString(),
      icon: Heart,
      color: 'text-red-500',
      bgColor: 'bg-red-500/10',
    },
    {
      label: t('totalValue'),
      value: formatCurrency(totalValue, currency),
      icon: DollarSign,
      color: 'text-green-500',
      bgColor: 'bg-green-500/10',
    },
    {
      label: t('avgPrice'),
      value: formatCurrency(avgPrice, currency),
      icon: TrendingUp,
      color: 'text-blue-500',
      bgColor: 'bg-blue-500/10',
    },
  ];

  return (
    <>
      {/* Mobile View */}
      <div className="lg:hidden">
        <Card>
          <CardContent className="p-3">
            <div className="flex items-center justify-around divide-x divide-border">
              <div className="flex-1 text-center px-2">
                <p className="text-xs text-muted-foreground">{t('totalItems')}</p>
                <p className="text-lg font-bold">{totalItems}</p>
              </div>
              <div className="flex-1 text-center px-2">
                <p className="text-xs text-muted-foreground">{currency}</p>
                <p className="text-xs text-muted-foreground">{t('totalValue')}</p>
                <p className="text-base font-bold">{new Intl.NumberFormat('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(totalValue)}</p>
              </div>
              <div className="flex-1 text-center px-2">
                <p className="text-xs text-muted-foreground">{currency}</p>
                <p className="text-xs text-muted-foreground">{t('avgPrice')}</p>
                <p className="text-base font-bold">{new Intl.NumberFormat('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(avgPrice)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Desktop View */}
      <div className="hidden lg:grid lg:grid-cols-3 gap-4">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className={`p-3 rounded-full ${stat.bgColor}`}>
                    <Icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">{stat.label}</p>
                    <p className="text-2xl font-bold">{stat.value}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </>
  );
}