'use client';

// components/account/WishlistClient.tsx
// Powerful wishlist screen with ordering promotion features using React Query and SWR

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { Heart, ShoppingCart, Package, Share2, Grid, List, Loader2 } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { WishlistItem } from '@/components/account/WishlistItem';
import { PreviouslyBoughtItem } from '@/components/account/PreviouslyBoughtItem';
import { WishlistStats } from '@/components/account/WishlistStats';
import { EmptyWishlist } from '@/components/account/EmptyWishlist';
import { useWishlist } from '@/hooks/queries/useWishlist';
import { useAddToCart } from '@/hooks/mutations/useAddToCart';
import { usePrefetchCart } from '@/hooks/queries/usePrefetchCart';
import { usePrefetchProduct } from '@/hooks/queries/usePrefetchProduct';
import type { WishlistItem as WishlistItemType } from '@/lib/actions/wishlist.actions';

interface WishlistClientProps {
  locale: string;
  initialWishlistItems?: WishlistItemType[];
  initialPreviouslyBoughtItems?: WishlistItemType[];
  userCurrency?: string;
}

type ViewMode = 'grid' | 'list';
type SortOption = 'newest' | 'oldest' | 'price-low' | 'price-high' | 'name';

export function WishlistClient({
  locale,
  initialWishlistItems = [],
  initialPreviouslyBoughtItems = [],
  userCurrency = 'USD'
}: WishlistClientProps) {
  const t = useTranslations('account');
  const tProducts = useTranslations('products');

  // React Query hooks with SWR-like prefetching
  const {
    wishlistItems,
    previouslyBoughtItems,
    isLoading,
    isWishlistLoading,
    error,
    backgroundRefresh
  } = useWishlist(userCurrency, initialWishlistItems, initialPreviouslyBoughtItems);

  // Prefetching utilities
  const { prefetchOnInteraction: prefetchCartOnInteraction } = usePrefetchCart();
  const { prefetchOnInteraction: prefetchProductOnInteraction } = usePrefetchProduct();

  // Mutations
  const addToCartMutation = useAddToCart();

  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [sortBy, setSortBy] = useState<SortOption>('newest');
  const [activeTab, setActiveTab] = useState('wishlist');

  // Background refresh and prefetching
  useEffect(() => {
    if (wishlistItems.length > 0) {
      // Prefetch cart data when wishlist loads
      const prefetchHandlers = prefetchCartOnInteraction(200, 150);
      // Could attach these to cart buttons in the future
    }
  }, [wishlistItems.length, prefetchCartOnInteraction]);

  // Enable background refresh for real-time updates
  useEffect(() => {
    const cleanup = backgroundRefresh(true);
    return cleanup;
  }, [backgroundRefresh]);

  // Handle bulk add to cart with prefetching
  const handleBulkAddToCart = async () => {
    // Add all wishlist items to cart with optimistic updates
    const promises = wishlistItems.map(item =>
      addToCartMutation.mutateAsync({
        productId: item.productId,
        variantId: item.variantId,
        quantity: 1,
      })
    );

    await Promise.allSettled(promises);
  };

  // Sort items based on selected option
  const sortItems = (items: WishlistItemType[], sortOption: SortOption) => {
    return [...items].sort((a, b) => {
      switch (sortOption) {
        case 'newest':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'oldest':
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        case 'price-low':
          return a.currentPrice - b.currentPrice;
        case 'price-high':
          return b.currentPrice - a.currentPrice;
        case 'name':
          const nameA = a.product.translations[0]?.name || a.product.originalName || '';
          const nameB = b.product.translations[0]?.name || b.product.originalName || '';
          return nameA.localeCompare(nameB);
        default:
          return 0;
      }
    });
  };

  const sortedWishlistItems = sortItems(wishlistItems, sortBy);
  const sortedPreviousItems = sortItems(previouslyBoughtItems, sortBy);

  // Calculate stats
  const totalWishlistValue = wishlistItems.reduce((sum, item) => sum + item.currentPrice, 0);
  const avgItemPrice = wishlistItems.length > 0 ? totalWishlistValue / wishlistItems.length : 0;

  return (
    <div className="space-y-6">
      {/* Header with Stats */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Heart className="h-8 w-8 text-red-500" />
            {t('favorites')}
          </h1>
          <p className="text-muted-foreground mt-2">
            {t('favoritesDescription')}
          </p>
        </div>
        
        {wishlistItems.length > 0 && (
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Share2 className="h-4 w-4 mr-2" />
              {t('shareWishlist')}
            </Button>
            <Button
              size="sm"
              onClick={handleBulkAddToCart}
              disabled={addToCartMutation.isPending}
            >
              {addToCartMutation.isPending ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <ShoppingCart className="h-4 w-4 mr-2" />
              )}
              {addToCartMutation.isPending ? t('adding') : t('addAllToCart')}
            </Button>
          </div>
        )}
      </div>

      {/* Stats Cards */}
      {wishlistItems.length > 0 && (
        <WishlistStats
          totalItems={wishlistItems.length}
          totalValue={totalWishlistValue}
          avgPrice={avgItemPrice}
          currency={wishlistItems[0]?.currentCurrency || 'USD'}
        />
      )}

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
          <TabsList className="grid w-full sm:w-auto grid-cols-2">
            <TabsTrigger value="wishlist" className="flex items-center gap-2">
              <Heart className="h-4 w-4" />
              {t('wishlist')} ({wishlistItems.length})
            </TabsTrigger>
            <TabsTrigger value="previously-bought" className="flex items-center gap-2">
              <Package className="h-4 w-4" />
              {t('previouslyBought')} ({previouslyBoughtItems.length})
            </TabsTrigger>
          </TabsList>

          {/* Controls */}
          <div className="flex items-center gap-2">
            <Select value={sortBy} onValueChange={(value: SortOption) => setSortBy(value)}>
              <SelectTrigger className="w-[180px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="newest">{t('sortNewest')}</SelectItem>
                <SelectItem value="oldest">{t('sortOldest')}</SelectItem>
                <SelectItem value="price-low">{t('sortPriceLow')}</SelectItem>
                <SelectItem value="price-high">{t('sortPriceHigh')}</SelectItem>
                <SelectItem value="name">{t('sortName')}</SelectItem>
              </SelectContent>
            </Select>

            <div className="flex border rounded-md">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('grid')}
                className="rounded-r-none"
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('list')}
                className="rounded-l-none"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Wishlist Tab */}
        <TabsContent value="wishlist" className="space-y-6">
          {isWishlistLoading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">{t('loading')}</span>
            </div>
          ) : wishlistItems.length === 0 ? (
            <EmptyWishlist locale={locale} />
          ) : (
            <div className={
              viewMode === 'grid'
                ? 'grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
                : 'space-y-4'
            }>
              {sortedWishlistItems.map((item) => {
                const prefetchHandlers = prefetchProductOnInteraction(
                  item.product.translations[0]?.slug || `product-${item.productId}`,
                  locale,
                  userCurrency,
                  200,
                  100
                );

                return (
                  <div
                    key={`${item.productId}-${item.variantId || 'no-variant'}`}
                    {...prefetchHandlers}
                  >
                    <WishlistItem
                      item={item}
                      locale={locale}
                      viewMode={viewMode}
                    />
                  </div>
                );
              })}
            </div>
          )}
        </TabsContent>

        {/* Previously Bought Tab */}
        <TabsContent value="previously-bought" className="space-y-6">
          {previouslyBoughtItems.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <Package className="h-12 w-12 mx-auto mb-4 opacity-50 text-muted-foreground" />
                <p className="text-muted-foreground mb-4">{t('noPreviousOrders')}</p>
                <Link href={`/${locale}/products`}>
                  <Button>{t('startShopping')}</Button>
                </Link>
              </CardContent>
            </Card>
          ) : (
            <div className={
              viewMode === 'grid'
                ? 'grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
                : 'space-y-4'
            }>
              {sortedPreviousItems.map((item) => {
                const prefetchHandlers = prefetchProductOnInteraction(
                  item.product.translations[0]?.slug || `product-${item.productId}`,
                  locale,
                  userCurrency,
                  200,
                  100
                );

                return (
                  <div
                    key={`prev-${item.productId}-${item.variantId || 'no-variant'}`}
                    {...prefetchHandlers}
                  >
                    <PreviouslyBoughtItem
                      item={item}
                      locale={locale}
                      viewMode={viewMode}
                    />
                  </div>
                );
              })}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Promotional Section */}
      {(wishlistItems.length > 0 || previouslyBoughtItems.length > 0) && (
        <Card className="bg-gradient-to-r from-primary/10 to-secondary/10 border-primary/20">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div>
                <h3 className="text-lg font-semibold mb-2">{t('readyToOrder')}</h3>
                <p className="text-muted-foreground">
                  {t('orderPromotionText', { count: wishlistItems.length + previouslyBoughtItems.length })}
                </p>
              </div>
              <div className="flex gap-2">
                <Link href={`/${locale}/products`}>
                  <Button variant="outline">
                    {t('continueShopping')}
                  </Button>
                </Link>
                <Link href={`/${locale}/cart`}>
                  <Button>
                    <ShoppingCart className="h-4 w-4 mr-2" />
                    {t('viewCart')}
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
