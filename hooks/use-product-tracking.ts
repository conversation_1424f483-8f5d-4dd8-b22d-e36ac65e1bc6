import { useEffect, useRef } from 'react';
import { trackProductView, trackCartAdd } from '@/lib/actions/user-activity.actions';
import type { ProductWithDetails } from '@/lib/types';

export function useProductTracking(product: ProductWithDetails, selectedVariants: Record<number, number>) {
  const hasTrackedViewRef = useRef<boolean>(false);

  // Track product view and check wishlist status on component mount
  // Non-blocking: don't await, let it run in background
  useEffect(() => {
    const trackView = async () => {
      if (hasTrackedViewRef.current) {
        return; // Already tracked for this session
      }

      try {
        // Fire and forget - don't block rendering
        trackProductView(product.id, product.marketplace);
        hasTrackedViewRef.current = true;
      } catch (error: unknown) {
        // Silently fail - activity tracking shouldn't break the UI
        console.error('Failed to track product view:', error);
      }
    };

    trackView();
  }, [product.id, product.marketplace]);

  const trackCartAddition = async (productId: number, marketplace: string) => {
    try {
      // Fire and forget - don't block cart operations
      trackCartAdd(productId, marketplace);
    } catch (error: unknown) {
      console.error('Failed to track cart addition:', error);
    }
  };

  return { trackCartAddition };
}