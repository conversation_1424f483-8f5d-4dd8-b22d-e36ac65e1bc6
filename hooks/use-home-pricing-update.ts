'use client';

import { useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { getFeaturedProducts } from '@/lib/actions/product.actions';
import { useCurrency } from '@/components/providers/CurrencyProvider';
import { DEFAULT_CURRENCY } from '@/lib/constants';
import type { ProductListItem } from '@/lib/types';

interface UseHomePricingUpdateProps {
  locale: string;
  initialFeaturedProducts: ProductListItem[];
}

export function useHomePricingUpdate({
  locale,
  initialFeaturedProducts,
}: UseHomePricingUpdateProps) {
  const { currency: userCurrency } = useCurrency();
  const [featuredProducts, setFeaturedProducts] = useState<ProductListItem[]>(initialFeaturedProducts);

  // Only fetch when currency changes from default
  const { data: updatedProducts, isLoading: isUpdatingPricing } = useQuery({
    queryKey: ['featured-products', locale, userCurrency, 8],
    queryFn: () => getFeaturedProducts(locale, 8, userCurrency),
    enabled: userCurrency !== DEFAULT_CURRENCY,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    retry: 2,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  });

  // Update products when pricing data is available
  useEffect(() => {
    if (updatedProducts) {
      setFeaturedProducts(updatedProducts);
    }
  }, [updatedProducts]);

  return {
    featuredProducts,
    isUpdatingPricing,
  };
}