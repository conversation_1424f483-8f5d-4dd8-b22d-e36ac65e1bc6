import { ProductWithDetails } from '@/lib/types';
import { useState } from 'react';

type Offer = ProductWithDetails['offers'][0];

export function useQuantityManagement(
  initialQuantity: number,
  currentOffer: Offer | undefined,
  lowestOffer: Offer
) {
  const [quantity, setQuantity] = useState(initialQuantity);

  const handleSetQuantity = (value: number | ((prev: number) => number)) => {
    const minQuantity = currentOffer?.min_quantity || lowestOffer?.min_quantity || 1;
    if (typeof value === 'function') {
      setQuantity(prev => Math.max(Number(minQuantity), value(prev)));
    } else {
      setQuantity(Math.max(Number(minQuantity), value));
    }
  };

  return { quantity, setQuantity, handleSetQuantity };
}