'use client';

// hooks/use-prefetch-home.ts
// Prefetching utilities for home screen interactions

import { usePrefetchProduct } from './queries/usePrefetchProduct';
import { useQueryClient } from '@tanstack/react-query';
import { getCategories, getProducts, getProductBySlug } from '@/lib/actions/product.actions';
import type { PaginatedResponse, ProductListItem } from '@/lib/types';

export function usePrefetchHome() {
  const { prefetchOnInteraction } = usePrefetchProduct();
  const queryClient = useQueryClient();

  // Prefetch categories data on hover/interaction
  const prefetchCategories = async (locale: string) => {
    await queryClient.prefetchQuery({
      queryKey: ['categories', locale] as const,
      queryFn: () => getCategories(locale),
      staleTime: 10 * 60 * 1000, // 10 minutes
    });
  };

  // Enhanced prefetch for product cards with related data
  const prefetchProductCard = (
    productId: number,
    slug: string,
    locale: string,
    currency?: string
  ) => {
    return prefetchOnInteraction(slug, locale, currency, 150, 100); // Faster on mobile
  };

  // Prefetch category page data
  const prefetchCategoryPage = async (categoryId: number, locale: string, currency?: string) => {
    // Prefetch category products
    await queryClient.prefetchInfiniteQuery({
      queryKey: ['products', JSON.stringify({ categoryId }), locale, currency] as const,
      queryFn: ({ pageParam }) =>
        getProducts(
          { categoryId, cursor: pageParam as string | undefined },
          locale,
          undefined,
          undefined,
          currency
        ),
      initialPageParam: undefined as string | undefined,
      getNextPageParam: (lastPage: PaginatedResponse<ProductListItem>) => lastPage.pagination?.nextCursor,
      staleTime: 5 * 60 * 1000,
    });
  };

  // Cross-platform prefetching for category links
  const prefetchCategoryOnInteraction = (
    categoryId: number,
    locale: string,
    currency?: string,
    hoverDelay: number = 200,
    touchDelay: number = 150
  ) => {
    let timeoutId: NodeJS.Timeout;
    let hasPrefetched = false;

    const performPrefetch = () => {
      if (hasPrefetched) return;
      hasPrefetched = true;
      prefetchCategoryPage(categoryId, locale, currency);
    };

    const handleMouseEnter = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(performPrefetch, hoverDelay);
    };

    const handleMouseLeave = () => {
      clearTimeout(timeoutId);
    };

    const handleTouchStart = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(performPrefetch, touchDelay);
    };

    const handleFocus = () => {
      clearTimeout(timeoutId);
      performPrefetch();
    };

    return {
      onMouseEnter: handleMouseEnter,
      onMouseLeave: handleMouseLeave,
      onTouchStart: handleTouchStart,
      onFocus: handleFocus,
    };
  };

  // Prefetch multiple products (useful for featured products section)
  const prefetchProductBatch = async (
    products: Array<{ id: number; slug: string }>,
    locale: string,
    currency?: string
  ) => {
    const prefetchPromises = products.map(product =>
      queryClient.prefetchQuery({
        queryKey: ['product', product.slug, locale, currency] as const,
        queryFn: () => getProductBySlug(product.slug, locale, currency),
        staleTime: 5 * 60 * 1000,
      })
    );

    await Promise.allSettled(prefetchPromises); // Don't fail if some prefetching fails
  };

  return {
    prefetchCategories,
    prefetchProductCard,
    prefetchCategoryPage,
    prefetchCategoryOnInteraction,
    prefetchProductBatch,
  };
}