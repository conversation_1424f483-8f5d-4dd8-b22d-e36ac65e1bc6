'use client';

// hooks/use-cart-store.ts
// Zustand store for shopping cart with Firestore persistence and lazy loading

import { create } from 'zustand';
import type { CartState, CartData } from '@/lib/types';
import { validateCartItem } from '@/lib/types';
import { getCart as fetchCart, addToCart as serverAddToCart, updateCartQuantity, removeFromCart as serverRemoveFromCart, clearCart as serverClearCart, getProductMinQuantity, getBuyNowCart, clearBuyNowCart } from '@/lib/actions/cart.actions';

interface CartStore extends CartState {
  // Internal state
  isInitialized: boolean;
  initialize: () => Promise<void>;
  // New: Lazy initialization control
  shouldInitialize: boolean;
  setShouldInitialize: (should: boolean) => void;
  // Buy now cart state
  buyNowData: CartData | null;
  initializeBuyNowCart: () => Promise<void>;
  clearBuyNowCart: () => Promise<void>;
}

export const useCartStore = create<CartStore>((set, get) => ({
  // Initial state
  data: null,
  isLoading: false,
  error: null,
  isInitialized: false,
  shouldInitialize: false, // Start with false - only initialize when needed
  buyNowData: null,

  // New: Control lazy initialization
  setShouldInitialize: (shouldInitialize: boolean) => {
    set({ shouldInitialize });
    // If should initialize and not yet initialized, do it now
    if (shouldInitialize && !get().isInitialized && !get().isLoading) {
      get().initialize();
    }
  },

  // Initialize cart from server - now lazy
  initialize: async () => {
    // Prevent multiple concurrent initializations
    if (get().isInitialized || get().isLoading) return;

    set({ isLoading: true, error: null });

    try {
      const cartData = await fetchCart();

      // Validate cart items and log warnings for missing variant names
      if (cartData?.items) {
        cartData.items.forEach((item, index) => {
          const validationError = validateCartItem(item);
          if (validationError) {
            console.warn(`Cart item ${index + 1}: ${validationError}`, item);
          }
        });
      }

      set({
        data: cartData,
        isLoading: false,
        isInitialized: true,
      });
    } catch (error) {
      console.error('Failed to initialize cart:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to load cart',
        isLoading: false,
        isInitialized: true,
      });
    }
  },

  // Refresh cart data from server
  refreshCart: async () => {
    set({ isLoading: true, error: null });

    try {
      const cartData = await fetchCart();
      set({ data: cartData, isLoading: false });
    } catch (error) {
      console.error('Failed to refresh cart:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to refresh cart',
        isLoading: false,
      });
    }
  },

  // Add item to cart (server-side)
  addItem: async (item) => {
    // Ensure cart is initialized before adding
    if (!get().isInitialized) {
      await get().initialize();
    }

    set({ isLoading: true, error: null });

    try {
      const result = await serverAddToCart({
        productId: item.productId,
        variantId: item.variantId,
        quantity: item.quantity,
        imageUrl: item.imageUrl,
      });

      if (result.success) {
        // Refresh cart data after successful addition
        await get().refreshCart();
      } else {
        set({
          error: result.error || 'Failed to add item to cart',
          isLoading: false,
        });
      }
    } catch (error) {
      console.error('Failed to add item to cart:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to add item to cart',
        isLoading: false,
      });
    }
  },

  // Remove item from cart (server-side)
  removeItem: async (productId, variantId) => {
    set({ isLoading: true, error: null });

    try {
      const result = await serverRemoveFromCart({ productId, variantId });

      if (result.success) {
        await get().refreshCart();
      } else {
        set({
          error: result.error || 'Failed to remove item from cart',
          isLoading: false,
        });
      }
    } catch (error) {
      console.error('Failed to remove item from cart:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to remove item from cart',
        isLoading: false,
      });
    }
  },

  // Update item quantity with optimistic local updates
  updateQuantity: async (productId, quantity, variantId) => {
    // Simply call server action - UI will handle optimistic updates locally
    try {
      const result = await updateCartQuantity({ productId, quantity, variantId });

      if (result.success) {
        // Refresh cart data after successful update
        await get().refreshCart();
      } else {
        set({
          error: result.error || 'Failed to update item quantity',
        });
      }
    } catch (error) {
      console.error('Failed to update item quantity:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to update item quantity',
      });
    }
  },

  // Clear cart (server-side)
  clearCart: async () => {
    set({ isLoading: true, error: null });

    try {
      const result = await serverClearCart();

      if (result.success) {
        set({
          data: {
            items: [],
            totals: { subtotal: 0, total: 0, currency: 'USD' },
            itemCount: 0,
          },
          isLoading: false,
        });
        // Note: Cart count will be reset in cart page component to avoid hook usage in non-React context
      } else {
        set({
          error: result.error || 'Failed to clear cart',
          isLoading: false,
        });
      }
    } catch (error) {
      console.error('Failed to clear cart:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to clear cart',
        isLoading: false,
      });
    }
  },

  // Initialize buy now cart from server
  initializeBuyNowCart: async () => {
    set({ isLoading: true, error: null });

    try {
      const buyNowData = await getBuyNowCart();
      set({ buyNowData, isLoading: false });
    } catch (error) {
      console.error('Failed to load buy now cart:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to load buy now cart',
        isLoading: false,
      });
    }
  },

  // Clear buy now cart (server-side)
  clearBuyNowCart: async () => {
    set({ isLoading: true, error: null });

    try {
      const result = await clearBuyNowCart();

      if (result.success) {
        set({
          buyNowData: null,
          isLoading: false,
        });
      } else {
        set({
          error: result.error || 'Failed to clear buy now cart',
          isLoading: false,
        });
      }
    } catch (error) {
      console.error('Failed to clear buy now cart:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to clear buy now cart',
        isLoading: false,
      });
    }
  },
}));
