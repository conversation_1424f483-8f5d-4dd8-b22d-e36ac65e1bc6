import { useToast } from '@/components/providers/ToastProvider';
import { useWishlistStatus } from '@/hooks/queries/useWishlistStatus';
import { useAddToWishlist } from '@/hooks/mutations/useAddToWishlist';
import { useRemoveFromWishlist } from '@/hooks/mutations/useRemoveFromWishlist';
import type { ProductWithDetails, TranslationFunction } from '@/lib/types';

export function useWishlist(product: ProductWithDetails, selectedVariants: Record<number, number>, t: TranslationFunction) {
  const { showToast } = useToast();

  // Get first variant ID
  const firstVariantId = Object.keys(selectedVariants)[0];
  const variantId = firstVariantId ? Number(firstVariantId) : undefined;

  // Use React Query for wishlist status with automatic caching and synchronization
  const { data: isWishlisted = false, isLoading: isWishlistLoading, error: wishlistError } = useWishlistStatus(
    product.id,
    variantId
  );

  // React Query mutations
  const { mutate: addToWishlistMutation } = useAddToWishlist();
  const { mutate: removeFromWishlistMutation } = useRemoveFromWishlist();

  // Handle wishlist toggle with enhanced optimistic updates and error handling
  const handleWishlistToggle = () => {
    if (isWishlistLoading || wishlistError) return; // Prevent spam clicks during loading/error

    // Optimistic update - React Query will automatically invalidate and refetch
    const wasWishlisted = isWishlisted;

    if (wasWishlisted) {
      removeFromWishlistMutation(
        { productId: product.id, variantId },
        {
          onSuccess: () => {
            showToast('success', t('wishlistRemoveSuccess', { productName: product.translations[0]?.name || product.original_name || 'Product' }));
          },
          onError: (error) => {
            const errorMessage = error instanceof Error ? error.message : t('wishlistRemoveError');
            showToast('error', errorMessage);
          },
        }
      );
    } else {
      addToWishlistMutation(
        { productId: product.id, variantId },
        {
          onSuccess: () => {
            showToast('success', t('wishlistAddSuccess', { productName: product.translations[0]?.name || product.original_name || 'Product' }));
          },
          onError: (error) => {
            const errorMessage = error instanceof Error ? error.message : t('wishlistAddError');
            showToast('error', errorMessage);
          },
        }
      );
    }
  };

  return { isWishlisted, isWishlistLoading, handleWishlistToggle };
}