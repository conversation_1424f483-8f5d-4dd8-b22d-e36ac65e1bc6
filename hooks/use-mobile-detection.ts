import { useState, useEffect } from 'react';

export function useMobileDetection() {
  const [isMobile, setIsMobile] = useState(false);
  const [maskOpacity, setMaskOpacity] = useState(0);

  // Check if device is mobile
  useEffect(() => {
    const checkDeviceType = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkDeviceType();
    window.addEventListener('resize', checkDeviceType);
    return () => window.removeEventListener('resize', checkDeviceType);
  }, []);

  // Hide header on mobile
  useEffect(() => {
    const header = document.querySelector('header');
    if (header) {
      header.style.display = isMobile ? 'none' : '';
    }

    // Cleanup: restore header when component unmounts
    return () => {
      const header = document.querySelector('header');
      if (header) header.style.display = '';
    };
  }, [isMobile]);

  // Scroll mask effect for mobile
  useEffect(() => {
    if (!isMobile) return;

    const handleScroll = () => {
      const scrollY = window.scrollY;
      const maxScroll = 200;
      const opacity = Math.min(scrollY / maxScroll, 1);
      setMaskOpacity(opacity);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [isMobile]);

  return { isMobile, maskOpacity };
}