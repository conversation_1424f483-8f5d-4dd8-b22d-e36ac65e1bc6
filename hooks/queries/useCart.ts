'use client';

// hooks/queries/useCart.ts
// SWR-based cart hook with optimistic updates and prefetching

import useS<PERSON> from 'swr';
import { useQueryClient } from '@tanstack/react-query';
import { getCart, addToCart as serverAddToCart, updateCartQuantity, removeFromCart as serverRemoveFromCart, clearCart as serverClearCart } from '@/lib/actions/cart.actions';
import type { CartData, CartItem } from '@/lib/types';
import { useToast } from '@/components/providers/ToastProvider';

interface UseCartOptions {
  enabled?: boolean;
  revalidateOnFocus?: boolean;
  revalidateOnReconnect?: boolean;
  dedupingInterval?: number;
}

interface UseCartReturn {
  data: CartData | null | undefined;
  error: Error | undefined;
  isLoading: boolean;
  isValidating: boolean;
  mutate: (data?: CartData | null | Promise<CartData | null> | ((current: CartData | null | undefined) => CartData | null | undefined)) => Promise<CartData | null | undefined>;
  refresh: () => Promise<CartData | null | undefined>;
  // Optimistic operations
  addItem: (item: CartItem) => Promise<void>;
  updateQuantity: (productId: number, quantity: number, variantId?: number) => Promise<void>;
  removeItem: (productId: number, variantId?: number) => Promise<void>;
  clearCart: () => Promise<void>;
}

export function useCart(options: UseCartOptions = {}): UseCartReturn {
  const queryClient = useQueryClient();
  const { showToast } = useToast();

  const {
    enabled = true,
    revalidateOnFocus = false,
    revalidateOnReconnect = true,
    dedupingInterval = 5000,
  } = options;

  // SWR hook for cart data - only fetch when enabled
  const { data, error, isLoading, isValidating, mutate } = useSWR<CartData | null>(
    enabled ? 'cart' : null,
    getCart,
    {
      revalidateOnFocus,
      revalidateOnReconnect,
      dedupingInterval,
      errorRetryCount: 3,
      errorRetryInterval: 1000,
      shouldRetryOnError: (error: Error & { status?: number }) => {
        // Don't retry on 4xx errors
        if (error.status && error.status >= 400 && error.status < 500) {
          return false;
        }
        return true;
      },
      onError: (error) => {
        console.error('Cart fetch error:', error);
      },
    }
  );

  // Refresh cart data
  const refresh = async () => {
    return mutate();
  };

  // Optimistic add item
  const addItem = async (item: CartItem) => {
    try {
      // Optimistic update
      await mutate(
        (current) => {
          if (!current) return current;
          const existingIndex = current.items.findIndex(
            (cartItem) => cartItem.productId === item.productId && cartItem.variantId === item.variantId
          );

          if (existingIndex >= 0) {
            // Update existing item quantity
            const newItems = [...current.items];
            newItems[existingIndex] = {
              ...newItems[existingIndex],
              quantity: newItems[existingIndex].quantity + item.quantity,
            };
            return {
              ...current,
              items: newItems,
              totals: {
                ...current.totals,
                subtotal: current.totals.subtotal + (item.price * item.quantity),
              },
              itemCount: current.itemCount + item.quantity,
            };
          } else {
            // Add new item
            return {
              ...current,
              items: [...current.items, item],
              totals: {
                ...current.totals,
                subtotal: current.totals.subtotal + (item.price * item.quantity),
              },
              itemCount: current.itemCount + item.quantity,
            };
          }
        },
        false // Don't revalidate immediately
      );

      // Server sync
      const result = await serverAddToCart({
        productId: item.productId,
        variantId: item.variantId,
        quantity: item.quantity,
        imageUrl: item.imageUrl,
      });

      if (result.success) {
        // Invalidate React Query cache for mutations
        queryClient.invalidateQueries({ queryKey: ['cart'] });
        showToast('success', 'Item added to cart successfully');
        // Revalidate SWR after successful server sync
        await mutate();
      } else {
        // Revert optimistic update on failure
        await mutate();
        showToast('error', result.error || 'Failed to add item to cart');
      }
    } catch (error) {
      // Revert optimistic update on error
      await mutate();
      console.error('Failed to add item to cart:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to add item to cart';
      showToast('error', errorMessage);
    }
  };

  // Optimistic update quantity
  const updateQuantity = async (productId: number, quantity: number, variantId?: number) => {
    try {
      // Optimistic update
      await mutate(
        (current) => {
          if (!current) return current;
          const itemIndex = current.items.findIndex(
            (item) => item.productId === productId && item.variantId === variantId
          );

          if (itemIndex === -1) return current;

          const oldItem = current.items[itemIndex];
          const quantityDiff = quantity - oldItem.quantity;
          const priceDiff = quantityDiff * oldItem.price;

          const newItems = [...current.items];
          newItems[itemIndex] = { ...oldItem, quantity };

          return {
            ...current,
            items: newItems,
            totals: {
              ...current.totals,
              subtotal: current.totals.subtotal + priceDiff,
            },
            itemCount: current.itemCount + quantityDiff,
          };
        },
        false
      );

      // Server sync
      const result = await updateCartQuantity({ productId, quantity, variantId });

      if (result.success) {
        queryClient.invalidateQueries({ queryKey: ['cart'] });
        showToast('success', 'Cart quantity updated');
        await mutate();
      } else {
        await mutate();
        showToast('error', result.error || 'Failed to update cart quantity');
      }
    } catch (error) {
      await mutate();
      console.error('Failed to update cart quantity:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to update cart quantity';
      showToast('error', errorMessage);
    }
  };

  // Optimistic remove item
  const removeItem = async (productId: number, variantId?: number) => {
    try {
      // Optimistic update
      await mutate(
        (current) => {
          if (!current) return current;
          const itemIndex = current.items.findIndex(
            (item) => item.productId === productId && item.variantId === variantId
          );

          if (itemIndex === -1) return current;

          const removedItem = current.items[itemIndex];
          const newItems = current.items.filter(
            (_, index) => index !== itemIndex
          );

          return {
            ...current,
            items: newItems,
            totals: {
              ...current.totals,
              subtotal: current.totals.subtotal - (removedItem.price * removedItem.quantity),
            },
            itemCount: current.itemCount - removedItem.quantity,
          };
        },
        false
      );

      // Server sync
      const result = await serverRemoveFromCart({ productId, variantId });

      if (result.success) {
        queryClient.invalidateQueries({ queryKey: ['cart'] });
        showToast('success', 'Item removed from cart');
        await mutate();
      } else {
        await mutate();
        showToast('error', result.error || 'Failed to remove item from cart');
      }
    } catch (error) {
      await mutate();
      console.error('Failed to remove item from cart:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to remove item from cart';
      showToast('error', errorMessage);
    }
  };

  // Optimistic clear cart
  const clearCart = async () => {
    try {
      // Optimistic update
      await mutate(
        (current) => {
          if (!current) return current;
          return {
            ...current,
            items: [],
            totals: { subtotal: 0, total: 0, currency: current.totals.currency },
            itemCount: 0,
          };
        },
        false
      );

      // Server sync
      const result = await serverClearCart();

      if (result.success) {
        queryClient.invalidateQueries({ queryKey: ['cart'] });
        showToast('success', 'Cart cleared');
        await mutate();
      } else {
        await mutate();
        showToast('error', result.error || 'Failed to clear cart');
      }
    } catch (error) {
      await mutate();
      console.error('Failed to clear cart:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to clear cart';
      showToast('error', errorMessage);
    }
  };

  return {
    data,
    error,
    isLoading,
    isValidating,
    mutate,
    refresh,
    addItem,
    updateQuantity,
    removeItem,
    clearCart,
  };
}