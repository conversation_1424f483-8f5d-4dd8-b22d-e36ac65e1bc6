'use client';

// hooks/queries/useProfile.ts
// SWR-based profile hook with optimistic updates and caching

import useSWR from 'swr';
import { useQueryClient } from '@tanstack/react-query';
import { getUserProfile, updateProfile } from '@/lib/actions/user.actions';
import type { UserProfile } from '@/lib/types';
import { useToast } from '@/components/providers/ToastProvider';

interface UseProfileOptions {
  enabled?: boolean;
  revalidateOnFocus?: boolean;
  revalidateOnReconnect?: boolean;
  dedupingInterval?: number;
}

interface UseProfileReturn {
  data: UserProfile | null | undefined;
  error: Error | undefined;
  isLoading: boolean;
  isValidating: boolean;
  mutate: (data?: UserProfile | null | Promise<UserProfile | null> | ((current: UserProfile | null | undefined) => UserProfile | null | undefined)) => Promise<UserProfile | null | undefined>;
  refresh: () => Promise<UserProfile | null | undefined>;
  // Optimistic operations
  updateProfile: (updates: {
    fullName?: string;
    phone?: string;
    preferredCurrency?: string;
  }) => Promise<void>;
}

export function useProfile(options: UseProfileOptions = {}): UseProfileReturn {
  const queryClient = useQueryClient();
  const { showToast } = useToast();

  const {
    enabled = true,
    revalidateOnFocus = false,
    revalidateOnReconnect = true,
    dedupingInterval = 30000, // 30 seconds for profile data
  } = options;

  // SWR hook for profile data
  const { data, error, isLoading, isValidating, mutate } = useSWR<UserProfile | null>(
    enabled ? 'profile' : null,
    getUserProfile,
    {
      revalidateOnFocus,
      revalidateOnReconnect,
      dedupingInterval,
      errorRetryCount: 3,
      errorRetryInterval: 1000,
      shouldRetryOnError: (error: Error & { status?: number }) => {
        // Don't retry on 4xx errors (auth issues)
        if (error.status && error.status >= 400 && error.status < 500) {
          return false;
        }
        return true;
      },
      onError: (error) => {
        console.error('Profile fetch error:', error);
      },
    }
  );

  // Refresh profile data
  const refresh = async () => {
    return mutate();
  };

  // Optimistic profile update
  const updateProfileOptimistic = async (updates: {
    fullName?: string;
    phone?: string;
    preferredCurrency?: string;
  }) => {
    try {
      // Store original data for rollback
      const originalData = data;

      // Optimistic update - apply changes immediately
      await mutate(
        (current) => {
          if (!current) return current;
          return {
            ...current,
            fullName: updates.fullName ?? current.fullName,
            phone: updates.phone ?? current.phone,
            preferredCurrency: updates.preferredCurrency ?? current.preferredCurrency,
            updated: new Date(),
          };
        },
        false // Don't revalidate immediately
      );

      // Server sync
      const result = await updateProfile(updates);

      if (result.success) {
        // Invalidate React Query cache for mutations
        queryClient.invalidateQueries({ queryKey: ['profile'] });
        showToast('success', 'Profile updated successfully');
        // Revalidate SWR after successful server sync
        await mutate();
      } else {
        // Rollback optimistic update on failure
        await mutate(originalData, false);
        showToast('error', result.error || 'Failed to update profile');
      }
    } catch (error) {
      // Rollback optimistic update on error
      await mutate(data, false);
      console.error('Failed to update profile:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to update profile';
      showToast('error', errorMessage);
    }
  };

  return {
    data,
    error,
    isLoading,
    isValidating,
    mutate,
    refresh,
    updateProfile: updateProfileOptimistic,
  };
}