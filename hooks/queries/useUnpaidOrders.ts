// hooks/queries/useUnpaidOrders.ts
// Centralized hook for managing unpaid orders count with caching and real-time updates

'use client';

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { getUserOrders } from '@/lib/actions/order.actions';
import { OrderStatus } from '@/app/generated/prisma';
import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import {
  UseUnpaidOrdersOptions,
  UseUnpaidOrdersReturn,
  ORDER_STATUS_CHANGE_EVENT,
  calculateRetryDelay,
  UNPAID_ORDERS_CONFIG,
} from '@/lib/types/unpaid-orders';

/**
 * Centralized hook for unpaid orders count management
 * Features:
 * - React Query caching
 * - Background polling for real-time updates
 * - Optimistic updates
 * - Error handling with retry logic
 */
export function useUnpaidOrders(options: UseUnpaidOrdersOptions = {}): UseUnpaidOrdersReturn {
  const {
    enabled = true,
    refetchInterval,
    staleTime = UNPAID_ORDERS_CONFIG.defaultStaleTime,
  } = options;

  const queryClient = useQueryClient();
  const pathname = usePathname();

  // Disable on product detail pages to avoid unnecessary API calls
  const isProductDetailPage = pathname?.includes('/products/') && !pathname?.includes('/products?');
  const shouldEnable = enabled && !isProductDetailPage;

  const {
    data,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['unpaid-orders-count'],
    queryFn: async () => {
      const ordersData = await getUserOrders();
      const count = ordersData?.orders?.filter(order => order.status === OrderStatus.unpaid).length || 0;
      return count;
    },
    enabled: shouldEnable,
    staleTime,
    refetchInterval: false,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    retry: (failureCount, error) => {
      // Retry up to configured max attempts with exponential backoff
      if (failureCount >= UNPAID_ORDERS_CONFIG.maxRetryAttempts) return false;

      // Don't retry on authentication errors
      if (error?.message?.includes('Not authenticated')) return false;

      return true;
    },
    retryDelay: calculateRetryDelay,
  });

  // Invalidate query when order status changes
  const invalidate = () => {
    queryClient.invalidateQueries({ queryKey: ['unpaid-orders-count'] });
  };

  // Listen for order status updates (can be extended with WebSocket/SSE)
  useEffect(() => {
    const handleOrderStatusChange = () => {
      invalidate();
    };

    // Listen for custom events (can be dispatched from payment components)
    window.addEventListener(ORDER_STATUS_CHANGE_EVENT, handleOrderStatusChange);

    return () => {
      window.removeEventListener(ORDER_STATUS_CHANGE_EVENT, handleOrderStatusChange);
    };
  }, []);

  return {
    count: data || 0,
    isLoading,
    error: error as Error | null,
    refetch,
    invalidate,
  };
}

/**
 * Hook for optimistic updates when order status changes
 */
export function useOptimisticUnpaidOrdersUpdate() {
  const queryClient = useQueryClient();

  const updateOrderStatus = (orderId: string, newStatus: OrderStatus) => {
    // Optimistically update the cache
    queryClient.setQueryData(['unpaid-orders-count'], (oldCount: number = 0) => {
      if (newStatus === OrderStatus.paid && oldCount > 0) {
        return oldCount - 1;
      }
      if (newStatus === OrderStatus.unpaid) {
        return oldCount + 1;
      }
      return oldCount;
    });

    // Dispatch event for other components to react
    window.dispatchEvent(new CustomEvent('order-status-changed', {
      detail: { orderId, newStatus }
    }));
  };

  return { updateOrderStatus };
}