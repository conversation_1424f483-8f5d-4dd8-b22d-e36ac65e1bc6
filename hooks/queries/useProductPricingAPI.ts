// hooks/queries/useProductPricingAPI.ts
// Client-side hook for fetching personalized product pricing via API

'use client';

import { useQuery } from '@tanstack/react-query';
import { useCurrency } from '@/components/providers/CurrencyProvider';

interface PricingOffer {
  id: string;
  price_low: number;
  price_high: number | null;
  currency: string;
  min_quantity: number | null;
  display_price: number;
  display_currency: string;
  exchange_rate: number;
}

interface PricingResponse {
  productId: number;
  offers: PricingOffer[];
  currency: string;
}

async function fetchProductPricing(
  slug: string,
  currency: string,
  locale: string
): Promise<PricingResponse> {
  const params = new URLSearchParams({
    currency,
    locale,
  });

  const response = await fetch(`/api/products/${slug}/pricing?${params}`);
  
  if (!response.ok) {
    throw new Error('Failed to fetch product pricing');
  }

  return response.json();
}

export function useProductPricingAPI(
  slug: string,
  locale: string = 'en',
  enabled: boolean = true
) {
  const { currency } = useCurrency();

  return useQuery<PricingResponse>({
    queryKey: ['productPricingAPI', slug, currency, locale],
    queryFn: () => fetchProductPricing(slug, currency, locale),
    enabled: enabled && !!slug,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  });
}
