// hooks/queries/useWishlistStatusAPI.ts
// Client-side hook for fetching and updating wishlist status via API

'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

interface WishlistStatusResponse {
  productId: number;
  isWishlisted: boolean;
  requiresAuth: boolean;
}

interface WishlistUpdateResponse {
  productId: number;
  isWishlisted: boolean;
  success: boolean;
}

async function fetchWishlistStatus(productId: number): Promise<WishlistStatusResponse> {
  const response = await fetch(`/api/user/wishlist/status/${productId}`);
  
  if (!response.ok) {
    throw new Error('Failed to fetch wishlist status');
  }

  return response.json();
}

async function updateWishlistStatus(
  productId: number,
  action: 'add' | 'remove'
): Promise<WishlistUpdateResponse> {
  const response = await fetch(`/api/user/wishlist/status/${productId}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ action }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to update wishlist');
  }

  return response.json();
}

export function useWishlistStatusAPI(productId: number, enabled: boolean = true) {
  return useQuery<WishlistStatusResponse>({
    queryKey: ['wishlistStatus', productId],
    queryFn: () => fetchWishlistStatus(productId),
    enabled: enabled && productId > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    retry: 1, // Only retry once for auth-related queries
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  });
}

export function useWishlistToggleAPI() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ productId, action }: { productId: number; action: 'add' | 'remove' }) =>
      updateWishlistStatus(productId, action),
    onMutate: async ({ productId, action }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['wishlistStatus', productId] });

      // Snapshot the previous value
      const previousStatus = queryClient.getQueryData<WishlistStatusResponse>(['wishlistStatus', productId]);

      // Optimistically update to the new value
      queryClient.setQueryData<WishlistStatusResponse>(['wishlistStatus', productId], (old) => ({
        ...old!,
        isWishlisted: action === 'add',
      }));

      // Return a context object with the snapshotted value
      return { previousStatus };
    },
    onError: (err, { productId }, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousStatus) {
        queryClient.setQueryData(['wishlistStatus', productId], context.previousStatus);
      }
    },
    onSettled: (data, error, { productId }) => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ['wishlistStatus', productId] });
    },
  });
}
