'use client';

import { useQuery } from '@tanstack/react-query';
import { getPersonalizedProductPricing } from '@/lib/actions/client/product-client.actions';

type PricingResponse = Awaited<ReturnType<typeof getPersonalizedProductPricing>>;

export function useProductPricing(
  productId: number,
  currency: string,
  locale: string = 'en',
  enabled: boolean = true
) {
  return useQuery<PricingResponse>({
    queryKey: ['productPricing', productId, currency, locale],
    queryFn: () => getPersonalizedProductPricing(productId, currency, locale),
    enabled: enabled && productId > 0,
    staleTime: 10 * 60 * 1000,
    gcTime: 30 * 60 * 1000,
    retry: 2,
  });
}

