'use client';

// hooks/queries/usePrefetchCart.ts
// Prefetching utilities for cart data to improve UX

import { useQueryClient } from '@tanstack/react-query';
import { getCart } from '@/lib/actions/cart.actions';

export function usePrefetchCart() {
  const queryClient = useQueryClient();

  // Prefetch cart data for better UX
  const prefetchCart = async () => {
    await queryClient.prefetchQuery({
      queryKey: ['cart'],
      queryFn: getCart,
      staleTime: 30 * 1000, // Keep fresh for 30 seconds
    });
  };

  // Prefetch cart on hover/interaction (universal for desktop and mobile)
  const prefetchOnInteraction = (
    hoverDelay: number = 150,
    touchDelay: number = 100
  ) => {
    let timeoutId: NodeJS.Timeout;
    let hasPrefetched = false;

    const performPrefetch = () => {
      if (hasPrefetched) return;
      hasPrefetched = true;
      prefetchCart();
    };

    const handleMouseEnter = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(performPrefetch, hoverDelay);
    };

    const handleMouseLeave = () => {
      clearTimeout(timeoutId);
    };

    const handleTouchStart = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(performPrefetch, touchDelay);
    };

    const handleFocus = () => {
      clearTimeout(timeoutId);
      performPrefetch();
    };

    return {
      onMouseEnter: handleMouseEnter,
      onMouseLeave: handleMouseLeave,
      onTouchStart: handleTouchStart,
      onFocus: handleFocus,
    };
  };

  // Prefetch cart when cart icon comes into view
  const prefetchOnVisible = (
    rootMargin: string = '50px'
  ) => {
    let hasPrefetched = false;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !hasPrefetched) {
            hasPrefetched = true;
            prefetchCart();
            observer.disconnect(); // Only prefetch once
          }
        });
      },
      { rootMargin }
    );

    return {
      ref: (element: HTMLElement | null) => {
        if (element) {
          observer.observe(element);
        } else {
          observer.disconnect();
        }
      },
    };
  };

  // Background refresh for cart data (useful for long sessions)
  const backgroundRefresh = (interval: number = 5 * 60 * 1000) => { // 5 minutes default
    const refresh = () => {
      queryClient.invalidateQueries({ queryKey: ['cart'], refetchType: 'none' });
      prefetchCart();
    };

    const intervalId = setInterval(refresh, interval);

    return () => clearInterval(intervalId);
  };

  return {
    prefetchCart,
    prefetchOnInteraction,
    prefetchOnVisible,
    backgroundRefresh,
  };
}