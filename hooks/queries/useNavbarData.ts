'use client';

// hooks/queries/useNavbarData.ts
// SWR utilities for navbar data fetching and caching

import useSWR from 'swr';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { checkAdminAccess } from '@/lib/actions/auth.actions';
import { getCart } from '@/lib/actions/cart.actions';
import { useAuth } from '@/components/providers/FirebaseAuthProvider';

interface UserProfile {
  displayName: string | null;
  email: string | null;
  photoURL: string | null;
  hasAdminAccess: boolean;
  uid: string;
}

interface CartSummary {
  itemCount: number;
  total: number;
  currency: string;
}

// SWR hook for user profile data with admin access
export function useUserProfile() {
  const { user } = useAuth();

  return useSWR<UserProfile | null>(
    user ? ['user-profile', user.uid] : null,
    async () => {
      if (!user) return null;

      const hasAdminAccess = await checkAdminAccess();

      return {
        displayName: user.displayName,
        email: user.email,
        photoURL: user.photoURL,
        hasAdminAccess,
        uid: user.uid,
      };
    },
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: true,
      dedupingInterval: 30000, // 30 seconds
      errorRetryCount: 2,
      shouldRetryOnError: (error) => {
        // Don't retry on 4xx errors
        if (error.status && error.status >= 400 && error.status < 500) {
          return false;
        }
        return true;
      },
      onError: (error) => {
        console.error('User profile fetch error:', error);
      },
    }
  );
}

// TanStack Query hook for real-time cart summary (count and totals only)
export function useCartSummary() {
  return useQuery({
    queryKey: ['cart-summary'],
    queryFn: async (): Promise<CartSummary> => {
      const cart = await getCart();
      if (!cart) {
        return {
          itemCount: 0,
          total: 0,
          currency: 'USD',
        };
      }
      return {
        itemCount: cart.itemCount,
        total: cart.totals.total,
        currency: cart.totals.currency,
      };
    },
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false,
    refetchInterval: 60000, // Refresh every minute for real-time updates
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors
      if (error instanceof Error && 'status' in error && typeof error.status === 'number') {
        if (error.status >= 400 && error.status < 500) {
          return false;
        }
      }
      return failureCount < 2;
    },
  });
}

// SWR hook for search suggestions
export function useSearchSuggestions(query: string, enabled: boolean = false) {
  return useSWR<{ suggestions: string[]; products: unknown[] }>(
    enabled && query.length >= 2 ? ['search-suggestions', query] : null,
    async () => {
      // This would typically call an API endpoint for search suggestions
      // For now, return empty arrays as placeholder
      const response = await fetch(`/api/search/suggestions?q=${encodeURIComponent(query)}`);
      if (!response.ok) {
        throw new Error('Failed to fetch search suggestions');
      }
      return response.json();
    },
    {
      revalidateOnFocus: false,
      dedupingInterval: 5000, // 5 seconds
      errorRetryCount: 1,
      shouldRetryOnError: false, // Don't retry search failures
    }
  );
}


// Hook to prefetch navbar-critical data
export function useNavbarPrefetch() {
  const queryClient = useQueryClient();

  const prefetchCartSummary = () => {
    queryClient.prefetchQuery({
      queryKey: ['cart-summary'],
      queryFn: async (): Promise<CartSummary> => {
        const cart = await getCart();
        if (!cart) {
          return {
            itemCount: 0,
            total: 0,
            currency: 'USD',
          };
        }
        return {
          itemCount: cart.itemCount,
          total: cart.totals.total,
          currency: cart.totals.currency,
        };
      },
      staleTime: 30000,
    });
  };

  const prefetchUserProfile = async (userId: string) => {
    await queryClient.prefetchQuery({
      queryKey: ['user-profile', userId],
      queryFn: async (): Promise<UserProfile | null> => {
        const hasAdminAccess = await checkAdminAccess();
        // Note: This assumes we have access to user data, but in reality
        // we'd need to pass the full user object or fetch it
        return null; // Placeholder - would need user data
      },
      staleTime: 60000, // 1 minute
    });
  };

  return {
    prefetchCartSummary,
    prefetchUserProfile,
  };
}

// Declare global window interface for Google Analytics
declare global {
  interface Window {
    gtag: (command: string, targetId: string, config?: Record<string, unknown>) => void;
  }
}

// Hook for navbar analytics tracking
export function useNavbarAnalytics() {
  const trackSearchInteraction = (query: string, action: 'start' | 'submit' | 'clear') => {
    // Track search interactions
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'search_interaction', {
        search_term: query,
        action,
      });
    }
  };

  const trackNavigationClick = (destination: string, context: string) => {
    // Track navigation clicks
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'navigation_click', {
        destination,
        context,
      });
    }
  };

  const trackCartInteraction = (action: 'hover' | 'click' | 'prefetch') => {
    // Track cart interactions
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'cart_interaction', {
        action,
      });
    }
  };

  return {
    trackSearchInteraction,
    trackNavigationClick,
    trackCartInteraction,
  };
}