'use client';

import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { getBatchProductPricing } from '@/lib/actions/client/product-client.actions';
import type { BatchPricingResponse } from '@/lib/actions/product-pricing.actions';

// Add to window for persistence
declare global {
  interface Window {
    __REACT_QUERY_PERSISTENCE__?: {
      batchPricing: Record<string, BatchPricingResponse>;
    };
  }
}

interface UseBatchPricingOptions {
  enabled?: boolean;
  staleTime?: number;
  gcTime?: number;
}

export function useBatchPricing(
  productIds: number[],
  currency: string,
  locale: string = 'en',
  options: UseBatchPricingOptions = {}
) {
  const {
    enabled = true,
    staleTime = 10 * 60 * 1000,
    gcTime = 30 * 60 * 1000,
  } = options;

  const queryKey = React.useMemo(
    () => ['batchPricing', productIds.sort().join(','), currency, locale],
    [productIds, currency, locale]
  );

  const query = useQuery<BatchPricingResponse | null>({
    queryKey,
    queryFn: () => getBatchProductPricing(productIds, currency, locale),
    enabled: enabled && productIds.length > 0,
    staleTime,
    gcTime,
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors, but retry network errors up to 2 times
      if (error instanceof Error && 'status' in error && typeof error.status === 'number') {
        if (error.status >= 400 && error.status < 500) {
          return false;
        }
      }
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
    // Background refresh for pricing data every 3 minutes
    refetchInterval: 3 * 60 * 1000,
    refetchIntervalInBackground: true,
    // Non-critical: don't block rendering
    networkMode: 'offlineFirst',
    // Persist to sessionStorage for offline/resume experience
    initialData: () => {
      if (typeof window !== 'undefined') {
        try {
          const persisted = sessionStorage.getItem(`react-query-cache-${queryKey.join('-')}`);
          return persisted ? JSON.parse(persisted) : undefined;
        } catch {
          return undefined;
        }
      }
      return undefined;
    },
  });

  // Persist successful data to sessionStorage
  React.useEffect(() => {
    if (query.data && typeof window !== 'undefined') {
      try {
        sessionStorage.setItem(
          `react-query-cache-${queryKey.join('-')}`,
          JSON.stringify(query.data)
        );
      } catch (error) {
        console.warn('Failed to persist pricing data:', error);
      }
    }
  }, [query.data, queryKey]);

  return query;
}

