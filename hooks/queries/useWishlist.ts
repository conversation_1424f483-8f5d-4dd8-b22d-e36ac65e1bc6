'use client';

// hooks/queries/useWishlist.ts
// React Query hooks for wishlist data fetching with SWR-like UX improvements

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useEffect } from 'react';
import { getWishlistItems, getPreviouslyBoughtItems, type WishlistItem } from '@/lib/actions/wishlist.actions';
import { usePrefetchCart } from './usePrefetchCart';

export function useWishlist(userCurrency?: string, initialWishlistItems?: WishlistItem[], initialPreviouslyBoughtItems?: WishlistItem[]) {
  const queryClient = useQueryClient();
  const { prefetchCart } = usePrefetchCart();

  // Set initial data from server if provided
  useEffect(() => {
    if (initialWishlistItems) {
      queryClient.setQueryData(['wishlist', userCurrency], initialWishlistItems);
    }
    if (initialPreviouslyBoughtItems) {
      queryClient.setQueryData(['previouslyBought', userCurrency], initialPreviouslyBoughtItems);
    }
  }, [queryClient, userCurrency, initialWishlistItems, initialPreviouslyBoughtItems]);

  const wishlistQuery = useQuery({
    queryKey: ['wishlist', userCurrency] as const,
    queryFn: () => getWishlistItems(userCurrency),
    staleTime: 5 * 60 * 1000, // 5 minutes - wishlist doesn't change often
    gcTime: 15 * 60 * 1000, // Keep in memory for 15 minutes
    enabled: !!userCurrency,
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors
      if (error instanceof Error && 'status' in error && typeof error.status === 'number') {
        if (error.status >= 400 && error.status < 500) {
          return false;
        }
      }
      return failureCount < 3;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
    refetchOnMount: false, // We'll prefetch instead
  });

  const previouslyBoughtQuery = useQuery({
    queryKey: ['previouslyBought', userCurrency] as const,
    queryFn: () => getPreviouslyBoughtItems(userCurrency),
    staleTime: 10 * 60 * 1000, // 10 minutes - previously bought changes less frequently
    gcTime: 30 * 60 * 1000, // Keep in memory for 30 minutes
    enabled: !!userCurrency,
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors
      if (error instanceof Error && 'status' in error && typeof error.status === 'number') {
        if (error.status >= 400 && error.status < 500) {
          return false;
        }
      }
      return failureCount < 3;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
    refetchOnMount: false,
  });

  // Prefetch cart when wishlist items are loaded (for add to cart actions)
  const prefetchCartOnWishlistLoad = () => {
    if (wishlistQuery.data && wishlistQuery.data.length > 0) {
      prefetchCart();
    }
  };

  // Background refetching for real-time updates
  const backgroundRefresh = (enabled: boolean = true) => {
    if (!enabled) return;

    const refresh = () => {
      queryClient.invalidateQueries({ queryKey: ['wishlist'], refetchType: 'none' });
      wishlistQuery.refetch();
    };

    // Refresh every 2 minutes when tab is active
    const intervalId = setInterval(refresh, 2 * 60 * 1000);

    return () => clearInterval(intervalId);
  };

  // Real-time updates using visibility change
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && wishlistQuery.data) {
        // Refetch when user returns to tab
        wishlistQuery.refetch();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [wishlistQuery]);

  // Optimistic update utilities
  const optimisticAddToWishlist = (productId: number, variantId?: number) => {
    queryClient.setQueryData(['wishlistStatus', productId, variantId], true);
  };

  const optimisticRemoveFromWishlist = (productId: number, variantId?: number) => {
    queryClient.setQueryData(['wishlistStatus', productId, variantId], false);
  };

  return {
    // Data
    wishlistItems: wishlistQuery.data || [],
    previouslyBoughtItems: previouslyBoughtQuery.data || [],

    // Loading states
    isLoading: wishlistQuery.isLoading || previouslyBoughtQuery.isLoading,
    isWishlistLoading: wishlistQuery.isLoading,
    isPreviouslyBoughtLoading: previouslyBoughtQuery.isLoading,

    // Error states
    error: wishlistQuery.error || previouslyBoughtQuery.error,
    wishlistError: wishlistQuery.error,
    previouslyBoughtError: previouslyBoughtQuery.error,

    // Refetch functions
    refetch: () => {
      wishlistQuery.refetch();
      previouslyBoughtQuery.refetch();
    },

    // Utility functions
    prefetchCartOnLoad: prefetchCartOnWishlistLoad,
    backgroundRefresh,

    // Optimistic updates
    optimisticAddToWishlist,
    optimisticRemoveFromWishlist,
  };
}