'use client';

import { useQuery } from '@tanstack/react-query';
import { getRelatedProducts } from '@/lib/actions/product.actions';

type RelatedProductsResponse = Awaited<ReturnType<typeof getRelatedProducts>>;

export function useRelatedProducts(
  productId: number,
  locale: string = 'en',
  currency?: string,
  limit: number = 6,
  enabled: boolean = true
) {
  return useQuery<RelatedProductsResponse>({
    queryKey: ['relatedProducts', productId, locale, currency, limit],
    queryFn: () => getRelatedProducts(productId, locale, limit, currency),
    enabled: enabled && productId > 0,
    staleTime: 10 * 60 * 1000, // 10 minutes - related products change less frequently
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    refetchInterval: 15 * 60 * 1000, // Background refetch every 15 minutes for related products
    refetchIntervalInBackground: true,
    // Non-critical: don't block initial page render
    networkMode: 'offlineFirst',
  });
}