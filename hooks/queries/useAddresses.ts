'use client';

// hooks/queries/useAddresses.ts
// SWR-based addresses hook with optimistic updates and caching

import useS<PERSON> from 'swr';
import { useQueryClient } from '@tanstack/react-query';
import { getUserAddresses, addAddress, updateAddress, deleteAddress } from '@/lib/actions/user.actions';
import type { Address } from '@/lib/types';
import { useToast } from '@/components/providers/ToastProvider';

interface UseAddressesOptions {
  enabled?: boolean;
  revalidateOnFocus?: boolean;
  revalidateOnReconnect?: boolean;
  dedupingInterval?: number;
}

interface UseAddressesReturn {
  data: Address[] | null | undefined;
  error: Error | undefined;
  isLoading: boolean;
  isValidating: boolean;
  mutate: (data?: Address[] | null | Promise<Address[] | null> | ((current: Address[] | null | undefined) => Address[] | null | undefined)) => Promise<Address[] | null | undefined>;
  refresh: () => Promise<Address[] | null | undefined>;
  // Optimistic operations
  addAddress: (address: Omit<Address, 'id'>) => Promise<void>;
  updateAddress: (addressId: string, updates: Partial<Address>) => Promise<void>;
  deleteAddress: (addressId: string) => Promise<void>;
}

export function useAddresses(options: UseAddressesOptions = {}): UseAddressesReturn {
  const queryClient = useQueryClient();
  const { showToast } = useToast();

  const {
    enabled = true,
    revalidateOnFocus = false,
    revalidateOnReconnect = true,
    dedupingInterval = 30000, // 30 seconds for addresses
  } = options;

  // SWR hook for addresses data
  const { data, error, isLoading, isValidating, mutate } = useSWR<Address[] | null>(
    enabled ? 'addresses' : null,
    getUserAddresses,
    {
      revalidateOnFocus,
      revalidateOnReconnect,
      dedupingInterval,
      errorRetryCount: 3,
      errorRetryInterval: 1000,
      shouldRetryOnError: (error: Error & { status?: number }) => {
        // Don't retry on 4xx errors (auth issues)
        if (error.status && error.status >= 400 && error.status < 500) {
          return false;
        }
        return true;
      },
      onError: (error) => {
        console.error('Addresses fetch error:', error);
      },
    }
  );

  // Refresh addresses data
  const refresh = async () => {
    return mutate();
  };

  // Optimistic add address
  const addAddressOptimistic = async (address: Omit<Address, 'id'>) => {
    try {
      // Generate temporary ID for optimistic update
      const tempId = `temp-${crypto.randomUUID()}`;
      const newAddress: Address = { ...address, id: tempId };

      // Optimistic update
      await mutate(
        (current) => {
          if (!current) return [newAddress];
          return [...current, newAddress];
        },
        false
      );

      // Server sync
      const result = await addAddress(address);

      if (result.success && result.address) {
        // Replace temp address with real one
        await mutate(
          (current) => {
            if (!current) return [result.address!];
            return current.map(addr =>
              addr.id === tempId ? result.address! : addr
            );
          },
          false
        );

        queryClient.invalidateQueries({ queryKey: ['addresses'] });
        showToast('success', 'Address added successfully');
        await mutate();
      } else {
        // Remove optimistic address on failure
        await mutate(
          (current) => current?.filter(addr => addr.id !== tempId) || null,
          false
        );
        showToast('error', result.error || 'Failed to add address');
      }
    } catch (error) {
      // Remove optimistic address on error
      await mutate(
        (current) => current?.filter(addr => !addr.id.startsWith('temp-')) || null,
        false
      );
      console.error('Failed to add address:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to add address';
      showToast('error', errorMessage);
    }
  };

  // Optimistic update address
  const updateAddressOptimistic = async (addressId: string, updates: Partial<Address>) => {
    try {
      // Store original address for rollback
      const originalAddresses = data;
      const originalAddress = data?.find(addr => addr.id === addressId);

      // Optimistic update
      await mutate(
        (current) => {
          if (!current) return current;
          return current.map(addr =>
            addr.id === addressId ? { ...addr, ...updates } : addr
          );
        },
        false
      );

      // Server sync
      const result = await updateAddress(addressId, updates);

      if (result.success) {
        queryClient.invalidateQueries({ queryKey: ['addresses'] });
        showToast('success', 'Address updated successfully');
        await mutate();
      } else {
        // Rollback optimistic update on failure
        await mutate(originalAddresses, false);
        showToast('error', result.error || 'Failed to update address');
      }
    } catch (error) {
      // Rollback optimistic update on error
      await mutate(data, false);
      console.error('Failed to update address:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to update address';
      showToast('error', errorMessage);
    }
  };

  // Optimistic delete address
  const deleteAddressOptimistic = async (addressId: string) => {
    try {
      // Store original addresses for rollback
      const originalAddresses = data;

      // Optimistic update
      await mutate(
        (current) => current?.filter(addr => addr.id !== addressId) || null,
        false
      );

      // Server sync
      const result = await deleteAddress(addressId);

      if (result.success) {
        queryClient.invalidateQueries({ queryKey: ['addresses'] });
        showToast('success', 'Address deleted successfully');
        await mutate();
      } else {
        // Rollback optimistic update on failure
        await mutate(originalAddresses, false);
        showToast('error', result.error || 'Failed to delete address');
      }
    } catch (error) {
      // Rollback optimistic update on error
      await mutate(data, false);
      console.error('Failed to delete address:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete address';
      showToast('error', errorMessage);
    }
  };

  return {
    data,
    error,
    isLoading,
    isValidating,
    mutate,
    refresh,
    addAddress: addAddressOptimistic,
    updateAddress: updateAddressOptimistic,
    deleteAddress: deleteAddressOptimistic,
  };
}