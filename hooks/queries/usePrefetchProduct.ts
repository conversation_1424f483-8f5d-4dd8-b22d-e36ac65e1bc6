'use client';

import { useQueryClient } from '@tanstack/react-query';
import { getProductBySlug, getRelatedProducts } from '@/lib/actions/product.actions';

export function usePrefetchProduct() {
  const queryClient = useQueryClient();

  const prefetchProduct = async (
    slug: string,
    locale: string = 'en',
    currency?: string
  ) => {
    // Prefetch product details
    await queryClient.prefetchQuery({
      queryKey: ['product', slug, locale, currency],
      queryFn: () => getProductBySlug(slug, locale, currency),
      staleTime: 5 * 60 * 1000,
    });

    // Get the prefetched product to extract ID for related products
    const product = await getProductBySlug(slug, locale, currency);

    if (product) {
      // Prefetch related products
      await queryClient.prefetchQuery({
        queryKey: ['relatedProducts', product.id, locale, currency, 6],
        queryFn: () => getRelatedProducts(product.id, locale, 6, currency),
        staleTime: 10 * 60 * 1000,
      });
    }
  };

  // Cross-platform prefetching that works on both desktop and mobile
  const prefetchOnInteraction = (
    slug: string,
    locale: string = 'en',
    currency?: string,
    hoverDelay: number = 100,
    touchDelay: number = 50
  ) => {
    let timeoutId: NodeJS.Timeout;
    let hasPrefetched = false;

    const performPrefetch = () => {
      if (hasPrefetched) return; // Prevent duplicate prefetches
      hasPrefetched = true;
      prefetchProduct(slug, locale, currency);
    };

    const handleMouseEnter = () => {
      // Clear existing timeout
      clearTimeout(timeoutId);

      // Debounce prefetching on hover (desktop)
      timeoutId = setTimeout(performPrefetch, hoverDelay);
    };

    const handleMouseLeave = () => {
      clearTimeout(timeoutId);
    };

    const handleTouchStart = () => {
      // Clear existing timeout
      clearTimeout(timeoutId);

      // Faster prefetching on touch (mobile)
      timeoutId = setTimeout(performPrefetch, touchDelay);
    };

    const handleTouchEnd = () => {
      // Don't clear timeout on touch end - let the prefetch complete
      // This ensures mobile users get prefetching benefit
    };

    const handleFocus = () => {
      // Immediate prefetch on keyboard focus (accessibility)
      clearTimeout(timeoutId);
      performPrefetch();
    };

    return {
      // Desktop interactions
      onMouseEnter: handleMouseEnter,
      onMouseLeave: handleMouseLeave,

      // Mobile interactions
      onTouchStart: handleTouchStart,
      onTouchEnd: handleTouchEnd,

      // Accessibility
      onFocus: handleFocus,
    };
  };

  // Legacy method for backward compatibility
  const prefetchOnHover = (
    slug: string,
    locale: string = 'en',
    currency?: string,
    delay: number = 100
  ) => {
    return prefetchOnInteraction(slug, locale, currency, delay, delay);
  };

  return {
    prefetchProduct,
    prefetchOnHover, // Legacy method
    prefetchOnInteraction, // Universal method that works on all devices
  };
}