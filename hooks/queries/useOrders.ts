'use client';

import { useInfiniteQuery, useQueryClient } from '@tanstack/react-query';
import { getUserOrders } from '@/lib/actions/order.actions';
import { ORDERS_PER_PAGE } from '@/lib/constants';

export function useOrders(enabled: boolean = true) {
  const queryClient = useQueryClient();

  return useInfiniteQuery({
    queryKey: ['orders'],
    queryFn: ({ pageParam }) =>
      getUserOrders(pageParam as string | undefined, ORDERS_PER_PAGE),
    initialPageParam: undefined as string | undefined,
    getNextPageParam: (lastPage) => lastPage?.pagination?.nextCursor,
    enabled,
    staleTime: 5 * 60 * 1000, // Keep fresh for 5 minutes
    gcTime: 15 * 60 * 1000, // Keep in memory for 15 minutes
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors
      if (error instanceof Error && 'status' in error && typeof error.status === 'number') {
        if (error.status >= 400 && error.status < 500) {
          return false;
        }
      }
      return failureCount < 3;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
    refetchOnMount: false, // Don't refetch on mount to avoid unnecessary requests
  });
}

