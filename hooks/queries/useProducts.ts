'use client';

import { useInfiniteQuery, useQueryClient } from '@tanstack/react-query';
import { getProducts } from '@/lib/actions/product.actions';
import type { ProductFiltersState, PaginatedResponse, ProductListItem } from '@/lib/types';

export function useProducts(
  filters: ProductFiltersState,
  locale: string,
  currency: string,
  enabled: boolean = true
) {
  const queryClient = useQueryClient();

  const query = useInfiniteQuery<PaginatedResponse<ProductListItem>>({
    queryKey: ['products', JSON.stringify(filters), locale, currency],
    queryFn: ({ pageParam }) =>
      getProducts(
        { ...filters, cursor: pageParam as string | undefined },
        locale,
        undefined,
        undefined,
        currency
      ),
    initialPageParam: undefined as string | undefined,
    getNextPageParam: (lastPage: PaginatedResponse<ProductListItem>) => lastPage.pagination?.nextCursor,
    enabled,
    staleTime: 5 * 60 * 1000, // Keep cached data fresh for 5 minutes
    gcTime: 15 * 60 * 1000, // Keep in memory for 15 minutes
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors, but retry network errors up to 3 times
      if (error instanceof Error && 'status' in error && typeof error.status === 'number') {
        if (error.status >= 400 && error.status < 500) {
          return false;
        }
      }
      return failureCount < 3;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
    refetchOnMount: false, // Don't refetch on mount to avoid unnecessary requests
  });

  return {
    ...query,
    // Optimistically update filters in cache before navigation
    updateFilters: (newFilters: Partial<ProductFiltersState>) => {
      const updatedFilters = { ...filters, ...newFilters };
      const queryKey = ['products', JSON.stringify(updatedFilters), locale, currency];

      // Prefetch the new query to make filter changes instant
      queryClient.prefetchInfiniteQuery({
        queryKey,
        queryFn: ({ pageParam }) =>
          getProducts(
            { ...updatedFilters, cursor: pageParam as string | undefined },
            locale,
            undefined,
            undefined,
            currency
          ),
        initialPageParam: undefined as string | undefined,
        getNextPageParam: (lastPage: PaginatedResponse<ProductListItem>) => lastPage.pagination?.nextCursor,
        staleTime: 5 * 60 * 1000,
      });
    },
  };
}

