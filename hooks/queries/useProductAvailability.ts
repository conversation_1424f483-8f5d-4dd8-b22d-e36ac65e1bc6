// hooks/queries/useProductAvailability.ts
// Client-side hook for fetching real-time product availability

'use client';

import { useQuery } from '@tanstack/react-query';

interface VariantAvailability {
  id: string;
  name: string;
  type: string;
  available: boolean;
  quantity: number | null;
  minQuantity: number | null;
}

interface OfferAvailability {
  id: string;
  available: boolean;
  quantity: number | null;
  minQuantity: number | null;
}

interface AvailabilityResponse {
  productId: number;
  available: boolean;
  quantity: number | null;
  variants: VariantAvailability[];
  offers: OfferAvailability[];
  lastUpdated: string;
}

async function fetchProductAvailability(
  slug: string,
  locale: string
): Promise<AvailabilityResponse> {
  const params = new URLSearchParams({
    locale,
  });

  const response = await fetch(`/api/products/${slug}/availability?${params}`);
  
  if (!response.ok) {
    throw new Error('Failed to fetch product availability');
  }

  return response.json();
}

export function useProductAvailability(
  slug: string,
  locale: string = 'en',
  enabled: boolean = true
) {
  return useQuery<AvailabilityResponse>({
    queryKey: ['productAvailability', slug, locale],
    queryFn: () => fetchProductAvailability(slug, locale),
    enabled: enabled && !!slug,
    staleTime: 2 * 60 * 1000, // 2 minutes - availability changes frequently
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: true, // Refetch when user returns to tab
    refetchOnReconnect: true,
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes for real-time updates
  });
}
