'use client';

import { useQuery } from '@tanstack/react-query';
import { getProductBySlug } from '@/lib/actions/product.actions';

type ProductDetailsResponse = Awaited<ReturnType<typeof getProductBySlug>>;

export function useProductDetails(
  slug: string,
  locale: string = 'en',
  currency?: string,
  enabled: boolean = true
) {
  return useQuery<ProductDetailsResponse>({
    queryKey: ['product', slug, locale, currency],
    queryFn: () => getProductBySlug(slug, locale, currency),
    enabled: enabled && !!slug,
    staleTime: 5 * 60 * 1000, // 5 minutes - product details don't change frequently
    gcTime: 15 * 60 * 1000, // 15 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: false, // Product details rarely need refetching on focus
    refetchOnReconnect: false, // Avoid unnecessary refetches on reconnect
    refetchInterval: 10 * 60 * 1000, // Background refetch every 10 minutes for stale data
    refetchIntervalInBackground: true, // Continue refetching when tab is not active
  });
}