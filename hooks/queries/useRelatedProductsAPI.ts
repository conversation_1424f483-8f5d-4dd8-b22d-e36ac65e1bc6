// hooks/queries/useRelatedProductsAPI.ts
// Client-side hook for fetching related products with personalized pricing via API

'use client';

import { useQuery } from '@tanstack/react-query';
import { useCurrency } from '@/components/providers/CurrencyProvider';
import type { ProductListItem } from '@/lib/types';

interface RelatedProductsResponse {
  data: ProductListItem[];
  currency: string;
}

async function fetchRelatedProducts(
  slug: string,
  currency: string,
  locale: string,
  limit: number = 6
): Promise<RelatedProductsResponse> {
  const params = new URLSearchParams({
    currency,
    locale,
    limit: limit.toString(),
  });

  const response = await fetch(`/api/products/${slug}/related?${params}`);
  
  if (!response.ok) {
    throw new Error('Failed to fetch related products');
  }

  return response.json();
}

export function useRelatedProductsAPI(
  slug: string,
  locale: string = 'en',
  limit: number = 6,
  enabled: boolean = true
) {
  const { currency } = useCurrency();

  return useQuery<RelatedProductsResponse>({
    queryKey: ['relatedProductsAPI', slug, currency, locale, limit],
    queryFn: () => fetchRelatedProducts(slug, currency, locale, limit),
    enabled: enabled && !!slug,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  });
}
