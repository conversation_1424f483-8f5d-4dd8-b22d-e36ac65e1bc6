'use client';

// hooks/queries/useBatchCartOperations.ts
// Batch operations for cart with optimistic updates

import { useQueryClient } from '@tanstack/react-query';
import { useMutation } from '@tanstack/react-query';
import { addToCart, updateCartQuantity, removeFromCart } from '@/lib/actions/cart.actions';
import { useToast } from '@/components/providers/ToastProvider';
import type { CartItem } from '@/lib/types';

interface BatchCartOperation {
  type: 'add' | 'update' | 'remove';
  productId: number;
  quantity?: number;
  variantId?: number;
  imageUrl?: string;
}

interface BatchCartResult {
  success: boolean;
  results: Array<{
    operation: BatchCartOperation;
    success: boolean;
    error?: string;
  }>;
}

export function useBatchCartOperations() {
  const queryClient = useQueryClient();
  const { showToast } = useToast();

  const mutation = useMutation({
    mutationFn: async (operations: BatchCartOperation[]): Promise<BatchCartResult> => {
      const results = await Promise.allSettled(
        operations.map(async (op) => {
          try {
            let result;
            switch (op.type) {
              case 'add':
                result = await addToCart({
                  productId: op.productId,
                  variantId: op.variantId,
                  quantity: op.quantity || 1,
                  imageUrl: op.imageUrl,
                });
                break;
              case 'update':
                result = await updateCartQuantity({
                  productId: op.productId,
                  quantity: op.quantity || 0,
                  variantId: op.variantId,
                });
                break;
              case 'remove':
                result = await removeFromCart({
                  productId: op.productId,
                  variantId: op.variantId,
                });
                break;
              default:
                throw new Error(`Unknown operation type: ${op.type}`);
            }

            return {
              operation: op,
              success: result.success,
              error: result.error,
            };
          } catch (error) {
            return {
              operation: op,
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error',
            };
          }
        })
      );

      const processedResults = results.map((result, index) => ({
        operation: operations[index],
        success: result.status === 'fulfilled' ? result.value.success : false,
        error: result.status === 'rejected'
          ? (result.reason instanceof Error ? result.reason.message : 'Batch operation failed')
          : result.value.error,
      }));

      return {
        success: processedResults.every(r => r.success),
        results: processedResults,
      };
    },
    onMutate: async (operations: BatchCartOperation[]) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['cart'] });

      // Snapshot previous value
      const previousData = queryClient.getQueryData(['cart']);

      // Optimistically update cart data
      queryClient.setQueryData(['cart'], (current: { items: CartItem[]; totals: { subtotal: number; total: number; currency: string }; itemCount: number }) => {
        if (!current) return current;

        const newData = { ...current };
        const itemsMap = new Map();

        // Create a map of existing items
        newData.items.forEach((item: CartItem, index: number) => {
          const key = `${item.productId}-${item.variantId || ''}`;
          itemsMap.set(key, { item, index });
        });

        operations.forEach((op) => {
          const key = `${op.productId}-${op.variantId || ''}`;

          switch (op.type) {
            case 'add': {
              const existing = itemsMap.get(key);
              if (existing) {
                // Update existing item quantity
                const updatedItem = {
                  ...existing.item,
                  quantity: existing.item.quantity + (op.quantity || 1),
                };
                newData.items[existing.index] = updatedItem;
                newData.totals.subtotal += updatedItem.price * (op.quantity || 1);
                newData.itemCount += op.quantity || 1;
              } else {
                // Add new item (simplified - would need full item data)
                // For simplicity, we'll just update totals
                newData.itemCount += op.quantity || 1;
              }
              break;
            }
            case 'update': {
              const existing = itemsMap.get(key);
              if (existing) {
                const quantityDiff = (op.quantity || 0) - existing.item.quantity;
                const priceDiff = quantityDiff * existing.item.price;

                const updatedItem = {
                  ...existing.item,
                  quantity: op.quantity || 0,
                };
                newData.items[existing.index] = updatedItem;
                newData.totals.subtotal += priceDiff;
                newData.itemCount += quantityDiff;
              }
              break;
            }
            case 'remove': {
              const existing = itemsMap.get(key);
              if (existing) {
                const removedItem = newData.items[existing.index];
                newData.items.splice(existing.index, 1);
                newData.totals.subtotal -= removedItem.price * removedItem.quantity;
                newData.itemCount -= removedItem.quantity;

                // Update indices for remaining items
                itemsMap.forEach((value, mapKey) => {
                  if (value.index > existing.index) {
                    value.index--;
                  }
                });
              }
              break;
            }
          }
        });

        return newData;
      });

      return { previousData };
    },
    onError: (error, variables, context) => {
      // Revert optimistic update on error
      if (context?.previousData) {
        queryClient.setQueryData(['cart'], context.previousData);
      }

      console.error('Batch cart operation error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Batch operation failed';
      showToast('error', errorMessage);
    },
    onSuccess: (data) => {
      // Invalidate and refetch cart data
      queryClient.invalidateQueries({ queryKey: ['cart'] });

      const successCount = data.results.filter(r => r.success).length;
      const totalCount = data.results.length;

      if (data.success) {
        showToast('success', `Successfully processed ${successCount} of ${totalCount} cart operations`);
      } else {
        showToast('error', `Completed ${successCount} of ${totalCount} cart operations with some failures`);
      }
    },
    retry: (failureCount, error) => {
      // Don't retry on client errors (4xx)
      if (error instanceof Error && 'status' in error && typeof error.status === 'number') {
        if (error.status >= 400 && error.status < 500) {
          return false;
        }
      }
      return failureCount < 2; // Retry up to 2 times for batch operations
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
  });

  return {
    ...mutation,
    batchOperations: mutation.mutate,
    batchOperationsAsync: mutation.mutateAsync,
  };
}