'use client';

// hooks/queries/useCartAnalytics.ts
// Analytics and performance monitoring for cart operations

import { useEffect, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { performanceMonitor } from '@/lib/utils/performance-monitor';

interface CartAnalyticsEvent {
  action: 'view' | 'add_item' | 'remove_item' | 'update_quantity' | 'clear_cart' | 'checkout_start';
  productId?: number;
  variantId?: number;
  quantity?: number;
  timestamp: number;
  duration?: number;
  success?: boolean;
  error?: string;
}

interface UseCartAnalyticsOptions {
  enabled?: boolean;
  trackPerformance?: boolean;
}

export function useCartAnalytics(options: UseCartAnalyticsOptions = {}) {
  const { enabled = true, trackPerformance = true } = options;
  const queryClient = useQueryClient();
  const lastFetchTime = useRef<number>(0);

  // Track cart view events
  const trackCartView = (itemCount: number, totalValue?: number) => {
    if (!enabled) return;

    const event: CartAnalyticsEvent = {
      action: 'view',
      timestamp: Date.now(),
    };

    // Send to analytics service
    if (typeof window !== 'undefined') {
      const gtag = (window as Window & { gtag?: (...args: unknown[]) => void }).gtag;
      if (gtag) {
        gtag('event', 'cart_view', {
          event_category: 'cart',
          event_label: `items_${itemCount}`,
          value: totalValue,
        });
      }
    }

    // Log performance metrics
    if (trackPerformance && lastFetchTime.current > 0) {
      const fetchDuration = Date.now() - lastFetchTime.current;
      performanceMonitor.trackQuery(`cart_fetch_duration_${fetchDuration}`);
    }
  };

  // Track cart operations with performance monitoring
  const trackCartOperation = (
    action: CartAnalyticsEvent['action'],
    data?: {
      productId?: number;
      variantId?: number;
      quantity?: number;
      duration?: number;
      success?: boolean;
      error?: string;
    }
  ) => {
    if (!enabled) return;

    const event: CartAnalyticsEvent = {
      action,
      timestamp: Date.now(),
      ...data,
    };

    // Send to analytics service
    if (typeof window !== 'undefined') {
      const gtag = (window as Window & { gtag?: (...args: unknown[]) => void }).gtag;
      if (gtag) {
        gtag('event', 'cart_operation', {
          event_category: 'cart',
          event_label: action,
          custom_parameters: {
            product_id: data?.productId,
            variant_id: data?.variantId,
            quantity: data?.quantity,
            success: data?.success,
          },
        });
      }
    }

    // Track performance for operations
    if (trackPerformance && data?.duration) {
      performanceMonitor.trackQuery(`cart_${action}_duration_${data.duration}`);
    }

    // Track errors
    if (data?.error) {
      performanceMonitor.trackQuery(`cart_operation_error_${data.error}_${action}_${data.productId || ''}_${data.variantId || ''}`);
    }
  };

  // Monitor query cache performance
  useEffect(() => {
    if (!enabled || !trackPerformance) return;

    const unsubscribe = queryClient.getQueryCache().subscribe((event) => {
      if (event.query.queryKey[0] === 'cart') {
        if (event.type === 'added') {
          lastFetchTime.current = Date.now();
        }
      }
    });

    return unsubscribe;
  }, [queryClient, enabled, trackPerformance]);

  // Track cart state changes for analytics
  const trackCartStateChange = (previousItemCount: number, newItemCount: number, totalValue?: number) => {
    if (!enabled) return;

    const itemDifference = newItemCount - previousItemCount;

    if (itemDifference > 0) {
      // Items added
      trackCartOperation('add_item', { quantity: itemDifference });
    } else if (itemDifference < 0) {
      // Items removed
      trackCartOperation('remove_item', { quantity: Math.abs(itemDifference) });
    }

    // Track cart value for business metrics
    if (trackPerformance && totalValue !== undefined) {
      performanceMonitor.trackQuery(`cart_total_value_${totalValue}`);
    }
  };

  // Performance monitoring helpers
  const startOperationTimer = () => {
    return performance.now();
  };

  const endOperationTimer = (startTime: number, action: CartAnalyticsEvent['action']) => {
    const duration = performance.now() - startTime;
    return duration;
  };

  return {
    trackCartView,
    trackCartOperation,
    trackCartStateChange,
    startOperationTimer,
    endOperationTimer,
  };
}