'use client';

import { useQueryClient } from '@tanstack/react-query';
import { getOrderById } from '@/lib/actions/order.actions';

export function usePrefetchOrderDetails() {
  const queryClient = useQueryClient();

  const prefetchOrderDetails = async (orderId: string, locale: string) => {
    await queryClient.prefetchQuery({
      queryKey: ['order', orderId, locale],
      queryFn: () => getOrderById(orderId),
      staleTime: 5 * 60 * 1000, // 5 minutes
    });
  };

  // Cross-platform prefetching for order details
  const prefetchOnInteraction = (
    orderId: string,
    locale: string,
    hoverDelay: number = 150,
    touchDelay: number = 100
  ) => {
    let timeoutId: NodeJS.Timeout;
    let hasPrefetched = false;

    const performPrefetch = () => {
      if (hasPrefetched) return;
      hasPrefetched = true;
      prefetchOrderDetails(orderId, locale);
    };

    const handleMouseEnter = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(performPrefetch, hoverDelay);
    };

    const handleMouseLeave = () => {
      clearTimeout(timeoutId);
    };

    const handleTouchStart = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(performPrefetch, touchDelay);
    };

    const handleFocus = () => {
      clearTimeout(timeoutId);
      performPrefetch();
    };

    return {
      onMouseEnter: handleMouseEnter,
      onMouseLeave: handleMouseLeave,
      onTouchStart: handleTouchStart,
      onFocus: handleFocus,
    };
  };

  return {
    prefetchOrderDetails,
    prefetchOnInteraction,
  };
}