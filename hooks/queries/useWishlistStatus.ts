'use client';

import { useQuery } from '@tanstack/react-query';
import { isInWishlist } from '@/lib/actions/wishlist.actions';

export function useWishlistStatus(
  productId: number,
  variantId?: number,
  enabled: boolean = true
) {
  return useQuery({
    queryKey: ['wishlistStatus', productId, variantId],
    queryFn: () => isInWishlist({ productId, variantId }),
    enabled: enabled && productId > 0,
    staleTime: 5 * 60 * 1000,
    gcTime: 15 * 60 * 1000,
    // Non-critical: don't block rendering, allow stale data
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  });
}

