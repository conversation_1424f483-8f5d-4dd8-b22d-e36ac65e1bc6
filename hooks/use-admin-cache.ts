'use client';

import { useState, useEffect, useCallback } from 'react';

interface CachedAdminState {
  isAdmin: boolean;
  lastChecked: number;
  userId: string;
}

const CACHE_KEY = 'admin-status-cache';
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export function useAdminCache() {
  const [cachedAdmin, setCachedAdmin] = useState<CachedAdminState | null>(null);

  // Load cache from localStorage on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem(CACHE_KEY);
      if (stored) {
        const parsed: CachedAdminState = JSON.parse(stored);
        setCachedAdmin(parsed);
      }
    } catch (error) {
      console.warn('Failed to load admin cache from localStorage:', error);
    }
  }, []);

  // Get cached admin status (checks expiry)
  const getCachedAdminStatus = useCallback((userId?: string): boolean => {
    if (!cachedAdmin || !userId) return false;

    // Check if cache is for current user
    if (cachedAdmin.userId !== userId) return false;

    // Check if cache is expired
    if (Date.now() > cachedAdmin.lastChecked + CACHE_DURATION) {
      return false;
    }

    return cachedAdmin.isAdmin;
  }, [cachedAdmin]);

  // Update cache with new admin status
  const updateAdminCache = useCallback((isAdmin: boolean, userId: string) => {
    const newCache: CachedAdminState = {
      isAdmin,
      lastChecked: Date.now(),
      userId,
    };

    setCachedAdmin(newCache);

    try {
      localStorage.setItem(CACHE_KEY, JSON.stringify(newCache));
    } catch (error) {
      console.warn('Failed to save admin cache to localStorage:', error);
    }
  }, []);

  // Clear cache (used on logout)
  const clearAdminCache = useCallback(() => {
    setCachedAdmin(null);
    try {
      localStorage.removeItem(CACHE_KEY);
    } catch (error) {
      console.warn('Failed to clear admin cache from localStorage:', error);
    }
  }, []);

  // Check if cache needs refresh
  const shouldRefreshCache = useCallback((userId?: string): boolean => {
    if (!cachedAdmin || !userId) return true;
    if (cachedAdmin.userId !== userId) return true;
    return Date.now() > cachedAdmin.lastChecked + CACHE_DURATION;
  }, [cachedAdmin]);

  return {
    getCachedAdminStatus,
    updateAdminCache,
    clearAdminCache,
    shouldRefreshCache,
  };
}