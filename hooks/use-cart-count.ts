'use client';

// hooks/use-cart-count.ts
// LocalStorage-based cart count management for instant navbar display

import { useState, useEffect, useCallback } from 'react';

const CART_COUNT_KEY = 'maomao_cart_count';
const CART_LAST_SYNC_KEY = 'maomao_cart_last_sync';

interface CartCountState {
  count: number;
  lastSync: number | null;
}

/**
 * Hook for managing cart count in localStorage for instant navbar display
 * Provides immediate cart count display without waiting for server data
 */
export function useCartCount() {
  const [count, setCount] = useState<number>(0);
  const [lastSync, setLastSync] = useState<number | null>(null);

  // Load initial count from localStorage
  useEffect(() => {
    if (typeof window === 'undefined') return;

    try {
      const storedCount = localStorage.getItem(CART_COUNT_KEY);
      const storedLastSync = localStorage.getItem(CART_LAST_SYNC_KEY);

      if (storedCount) {
        setCount(Math.max(0, parseInt(storedCount, 10)));
      }

      if (storedLastSync) {
        setLastSync(parseInt(storedLastSync, 10));
      }
    } catch (error) {
      console.warn('Failed to load cart count from localStorage:', error);
    }
  }, []);

  // Save count to localStorage whenever it changes
  useEffect(() => {
    if (typeof window === 'undefined') return;

    try {
      if (count >= 0) {
        localStorage.setItem(CART_COUNT_KEY, count.toString());
      }
    } catch (error) {
      console.warn('Failed to save cart count to localStorage:', error);
    }
  }, [count]);

  // Update cart count
  const updateCount = useCallback((newCount: number) => {
    const sanitizedCount = Math.max(0, Math.floor(newCount));
    setCount(sanitizedCount);
  }, []);

  // Increment cart count
  const incrementCount = useCallback((amount: number = 1) => {
    setCount(prev => Math.max(0, prev + amount));
  }, []);

  // Decrement cart count
  const decrementCount = useCallback((amount: number = 1) => {
    setCount(prev => Math.max(0, prev - amount));
  }, []);

  // Sync count with server data and update last sync timestamp
  const syncWithServer = useCallback((serverCount: number) => {
    const sanitizedCount = Math.max(0, Math.floor(serverCount));
    setCount(sanitizedCount);
    setLastSync(Date.now());

    try {
      localStorage.setItem(CART_LAST_SYNC_KEY, Date.now().toString());
    } catch (error) {
      console.warn('Failed to save last sync timestamp:', error);
    }
  }, []);

  // Reset cart count (on logout, clear cart, etc.)
  const resetCount = useCallback(() => {
    setCount(0);
    setLastSync(null);

    try {
      localStorage.removeItem(CART_COUNT_KEY);
      localStorage.removeItem(CART_LAST_SYNC_KEY);
    } catch (error) {
      console.warn('Failed to reset cart count in localStorage:', error);
    }
  }, []);

  // Check if count needs sync (if last sync was more than 5 minutes ago)
  const needsSync = useCallback(() => {
    if (!lastSync) return true;
    const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);
    return lastSync < fiveMinutesAgo;
  }, [lastSync]);

  return {
    count,
    lastSync,
    updateCount,
    incrementCount,
    decrementCount,
    syncWithServer,
    resetCount,
    needsSync,
  };
}