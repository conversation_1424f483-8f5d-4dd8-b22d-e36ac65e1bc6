import { useState } from 'react';

interface ProductImage {
  id: number | string;
  product_id: number;
  image_url: string;
  image_type: string;
  // display_order: number;
}

export function useImageNavigation(images: ProductImage[]) {
  const [[imageIndex, direction], setImageIndex] = useState([0, 0]);
  const [isZoomOpen, setIsZoomOpen] = useState(false);

  const currentImage = images[imageIndex]?.image_url;
  const currentImageType = images[imageIndex]?.image_type;

  const paginate = (newDirection: number) => {
    let newIndex = imageIndex + newDirection;
    if (newIndex < 0) {
      newIndex = images.length - 1;
    } else if (newIndex >= images.length) {
      newIndex = 0;
    }
    setImageIndex([newIndex, newDirection]);
  };

  const setImageByIndex = (index: number) => {
    const newDirection = index > imageIndex ? 1 : -1;
    setImageIndex([index, newDirection]);
  };

  return {
    imageIndex,
    direction,
    currentImage,
    currentImageType,
    isZoomOpen,
    setIsZoomOpen,
    paginate,
    setImageByIndex,
  };
}