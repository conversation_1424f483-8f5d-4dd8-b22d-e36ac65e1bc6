'use client';

import { useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { getBatchProductPricing } from '@/lib/actions/client/product-client.actions';
import { useCurrency } from '@/components/providers/CurrencyProvider';
import { DEFAULT_CURRENCY } from '@/lib/constants';
import type { BatchPricingResponse } from '@/lib/actions/product-pricing.actions';

interface UseListingPricingUpdateProps {
  productIds: number[];
  locale: string;
  initialPricing?: BatchPricingResponse | null;
}

export function useListingPricingUpdate({
  productIds,
  locale,
  initialPricing,
}: UseListingPricingUpdateProps) {
  const { currency: userCurrency } = useCurrency();
  const [pricing, setPricing] = useState<BatchPricingResponse | null>(initialPricing || null);

  // Only fetch when currency changes from default
  const { data: updatedPricing, isLoading: isUpdatingPricing } = useQuery({
    queryKey: ['batchPricing', productIds.sort().join(','), userCurrency, locale],
    queryFn: () => getBatchProductPricing(productIds, userCurrency, locale),
    enabled: userCurrency !== DEFAULT_CURRENCY && productIds.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    retry: 2,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  });

  // Update pricing when data is available
  useEffect(() => {
    if (updatedPricing) {
      setPricing(updatedPricing);
    }
  }, [updatedPricing]);

  return {
    pricing,
    isUpdatingPricing,
  };
}