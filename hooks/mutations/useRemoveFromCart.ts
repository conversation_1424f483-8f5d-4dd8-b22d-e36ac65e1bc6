'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { removeFromCart } from '@/lib/actions/cart.actions';
import { useToast } from '@/components/providers/ToastProvider';
import { useCartCount } from '@/hooks/use-cart-count';

export function useRemoveFromCart() {
  const queryClient = useQueryClient();
  const { showToast } = useToast();
  const { decrementCount } = useCartCount();

  return useMutation({
    mutationFn: (data: Parameters<typeof removeFromCart>[0]) =>
      removeFromCart(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['cart'] });
      decrementCount(); // Update localStorage cart count
      showToast('success', 'Item removed from cart');
    },
    onError: (error) => {
      console.error('Remove from cart error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to remove item from cart';
      showToast('error', errorMessage);
    },
    retry: (failureCount, error) => {
      // Don't retry on client errors (4xx)
      if (error instanceof Error && error.message.includes('Authentication required')) {
        return false;
      }
      if (error instanceof Error && error.message.includes('Cart not found')) {
        return false;
      }
      // Retry on server errors (5xx) or network issues
      return failureCount < 3;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

