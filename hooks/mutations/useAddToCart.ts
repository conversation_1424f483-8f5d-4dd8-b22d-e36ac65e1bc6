'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { addToCart } from '@/lib/actions/cart.actions';
import { useToast } from '@/components/providers/ToastProvider';
import { useCartCount } from '@/hooks/use-cart-count';

export function useAddToCart() {
  const queryClient = useQueryClient();
  const { showToast } = useToast();
  const { incrementCount } = useCartCount();

  return useMutation({
    mutationFn: (item: Parameters<typeof addToCart>[0]) => addToCart(item),
    onMutate: async (newItem) => {
      // Cancel any outgoing refetches (so they don't overwrite our optimistic update)
      await queryClient.cancelQueries({ queryKey: ['cart'] });

      // Snapshot the previous value
      const previousCart = queryClient.getQueryData(['cart']);

      // Optimistically update to the new value
      queryClient.setQueryData(['cart'], (old: { items: unknown[]; itemCount: number; totals: unknown } | undefined) => {
        if (!old) return old;

        const optimisticItem = {
          productId: newItem.productId,
          variantId: newItem.variantId,
          quantity: newItem.quantity,
          imageUrl: newItem.imageUrl,
          productName: 'Loading...', // Will be updated when real data comes in
          productSlug: '',
          price: 0, // Will be calculated server-side
          currency: 'CNY',
          marketplace: ''
        };

        return {
          ...old,
          items: [...(old.items || []), optimisticItem],
          itemCount: (old.itemCount || 0) + 1,
          totals: old.totals // Keep existing totals for now
        };
      });

      // Return a context object with the snapshotted value
      return { previousCart };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['cart'] });
      incrementCount(); // Update localStorage cart count
      showToast('success', 'Item added to cart successfully');
    },
    retry: (failureCount, error) => {
      // Don't retry on client errors (4xx)
      if (error instanceof Error && error.message.includes('Authentication required')) {
        return false;
      }
      if (error instanceof Error && error.message.includes('Product not found')) {
        return false;
      }
      // Retry on server errors (5xx) or network issues
      return failureCount < 3;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

