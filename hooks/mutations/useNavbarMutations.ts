'use client';

// hooks/mutations/useNavbarMutations.ts
// TanStack Query mutations for navbar state management

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { checkAdminAccess } from '@/lib/actions/auth.actions';
import { useCartCount } from '@/hooks/use-cart-count';

interface UserProfile {
  displayName: string | null;
  email: string | null;
  photoURL: string | null;
  hasAdminAccess: boolean;
  uid: string;
}

interface CartSummary {
  itemCount: number;
  total: number;
  currency: string;
}

interface UserPreferences {
  theme?: 'light' | 'dark' | 'system';
  currency?: string;
}

// Declare global window interface for Google Analytics
declare global {
  interface Window {
    gtag: (command: string, targetId: string, config?: Record<string, unknown>) => void;
  }
}

// Mutation for refreshing user profile data
export function useRefreshUserProfile() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (userId: string) => {
      const hasAdminAccess = await checkAdminAccess();
      // Return updated profile data - in reality, this would fetch fresh user data
      return { hasAdminAccess };
    },
    onSuccess: (data, userId) => {
      // Update the cached user profile
      queryClient.setQueryData(['user-profile', userId], (oldData: UserProfile | null) => {
        if (!oldData) return oldData;
        return {
          ...oldData,
          hasAdminAccess: data.hasAdminAccess,
        };
      });
    },
    onError: (error) => {
      console.error('Failed to refresh user profile:', error);
    },
  });
}

// Mutation for quick cart operations (add/remove/update) with optimistic updates
export function useQuickCartOperation() {
  const queryClient = useQueryClient();
  const { incrementCount, decrementCount } = useCartCount();

  return useMutation({
    mutationFn: async ({
      action,
      productId,
      variantId,
      quantity = 1,
    }: {
      action: 'add' | 'remove' | 'update';
      productId: number;
      variantId?: number;
      quantity?: number;
    }) => {
      // This would call the appropriate cart action
      // For now, just simulate the operation
      return { success: true, action };
    },
    onMutate: async ({ action, productId, variantId, quantity }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['cart-summary'] });

      // Snapshot previous value
      const previousCart = queryClient.getQueryData(['cart-summary']);

      // Optimistically update cart summary
      queryClient.setQueryData(['cart-summary'], (old: CartSummary | undefined) => {
        if (!old) return old;

        switch (action) {
          case 'add':
            incrementCount(quantity ?? 1);
            return {
              ...old,
              itemCount: old.itemCount + (quantity ?? 1),
            };
          case 'remove':
            decrementCount(quantity ?? 1);
            return {
              ...old,
              itemCount: Math.max(0, old.itemCount - (quantity ?? 1)),
            };
          case 'update':
            // For update, we'd need more complex logic
            return old;
          default:
            return old;
        }
      });

      // Return context with snapshotted value
      return { previousCart };
    },
    onError: (error, variables, context) => {
      // Revert optimistic update on error
      if (context?.previousCart) {
        queryClient.setQueryData(['cart-summary'], context.previousCart);
      }
      console.error('Cart operation failed:', error);
    },
    onSuccess: () => {
      // Refetch cart summary after successful mutation
      queryClient.invalidateQueries({ queryKey: ['cart-summary'] });
    },
  });
}

// Mutation for search analytics tracking
export function useTrackSearchAnalytics() {
  return useMutation({
    mutationFn: async ({
      query,
      action,
      resultsCount,
    }: {
      query: string;
      action: 'search' | 'suggestion_click' | 'clear';
      resultsCount?: number;
    }) => {
      // Track search analytics - could send to analytics API
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'search_analytics', {
          search_term: query,
          action,
          results_count: resultsCount,
        });
      }
      return { success: true };
    },
  });
}

// Mutation for navigation analytics
export function useTrackNavigationAnalytics() {
  return useMutation({
    mutationFn: async ({
      from,
      to,
      context,
    }: {
      from: string;
      to: string;
      context: string;
    }) => {
      // Track navigation analytics
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'navigation_analytics', {
          from_path: from,
          to_path: to,
          context,
        });
      }

      // Prefetch the destination if it's a known route
      if (to.startsWith('/products') || to.startsWith('/cart')) {
        // Prefetch logic could be added here
      }

      return { success: true };
    },
  });
}

// Mutation for theme preference updates
export function useUpdateThemePreference() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (theme: 'light' | 'dark' | 'system') => {
      // Save theme preference to localStorage and potentially to server
      localStorage.setItem('theme-preference', theme);

      // Could also send to server for persistence
      return { theme };
    },
    onSuccess: (data) => {
      // Update any cached theme data
      queryClient.setQueryData(['user-preferences'], (old: UserPreferences | undefined) => ({
        ...old,
        theme: data.theme,
      }));
    },
  });
}

// Mutation for currency preference updates
export function useUpdateCurrencyPreference() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (currency: string) => {
      // Update currency preference
      localStorage.setItem('currency-preference', currency);

      // Invalidate cart and pricing queries to refetch with new currency
      queryClient.invalidateQueries({ queryKey: ['cart'] });
      queryClient.invalidateQueries({ queryKey: ['cart-summary'] });
      queryClient.invalidateQueries({ queryKey: ['pricing'] });

      return { currency };
    },
    onSuccess: (data) => {
      // Update cached preferences
      queryClient.setQueryData(['user-preferences'], (old: UserPreferences | undefined) => ({
        ...old,
        currency: data.currency,
      }));
    },
  });
}