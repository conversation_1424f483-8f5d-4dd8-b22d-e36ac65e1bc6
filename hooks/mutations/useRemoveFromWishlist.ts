'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { removeFromWishlist } from '@/lib/actions/wishlist.actions';
import { useToast } from '@/components/providers/ToastProvider';

export function useRemoveFromWishlist() {
  const queryClient = useQueryClient();
  const { showToast } = useToast();

  return useMutation({
    mutationFn: (data: Parameters<typeof removeFromWishlist>[0]) =>
      removeFromWishlist(data),
    onMutate: async (removedItem) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['wishlistStatus', removedItem.productId, removedItem.variantId] });

      // Snapshot the previous value
      const previousStatus = queryClient.getQueryData(['wishlistStatus', removedItem.productId, removedItem.variantId]);

      // Optimistically update to false
      queryClient.setQueryData(['wishlistStatus', removedItem.productId, removedItem.variantId], false);

      // Return a context object with the snapshotted value
      return { previousStatus };
    },
    onSuccess: () => {
      // Invalidate and refetch wishlist data
      queryClient.invalidateQueries({ queryKey: ['wishlist'] });
      queryClient.invalidateQueries({ queryKey: ['wishlistStatus'] });
      showToast('success', 'Removed from wishlist');
    },
    onError: (error, removedItem, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousStatus !== undefined) {
        queryClient.setQueryData(['wishlistStatus', removedItem.productId, removedItem.variantId], context.previousStatus);
      }
      console.error('Remove from wishlist error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to remove from wishlist';
      showToast('error', errorMessage);
    },
    retry: (failureCount, error) => {
      // Don't retry on client errors (4xx)
      if (error instanceof Error && error.message.includes('Authentication required')) {
        return false;
      }
      // Retry on server errors (5xx) or network issues
      return failureCount < 3;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

