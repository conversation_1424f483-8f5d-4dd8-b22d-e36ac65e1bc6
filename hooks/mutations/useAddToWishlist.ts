'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { addToWishlist } from '@/lib/actions/wishlist.actions';
import { useToast } from '@/components/providers/ToastProvider';

export function useAddToWishlist() {
  const queryClient = useQueryClient();
  const { showToast } = useToast();

  return useMutation({
    mutationFn: (data: Parameters<typeof addToWishlist>[0]) =>
      addToWishlist(data),
    onMutate: async (newItem) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['wishlistStatus', newItem.productId, newItem.variantId] });

      // Snapshot the previous value
      const previousStatus = queryClient.getQueryData(['wishlistStatus', newItem.productId, newItem.variantId]);

      // Optimistically update to true
      queryClient.setQueryData(['wishlistStatus', newItem.productId, newItem.variantId], true);

      // Return a context object with the snapshotted value
      return { previousStatus };
    },
    onSuccess: () => {
      // Invalidate and refetch wishlist data
      queryClient.invalidateQueries({ queryKey: ['wishlist'] });
      queryClient.invalidateQueries({ queryKey: ['wishlistStatus'] });
      showToast('success', 'Added to wishlist');
    },
    onError: (error, newItem, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousStatus !== undefined) {
        queryClient.setQueryData(['wishlistStatus', newItem.productId, newItem.variantId], context.previousStatus);
      }
      console.error('Add to wishlist error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to add to wishlist';
      showToast('error', errorMessage);
    },
    retry: (failureCount, error) => {
      // Don't retry on client errors (4xx)
      if (error instanceof Error && error.message.includes('Authentication required')) {
        return false;
      }
      // Retry on server errors (5xx) or network issues
      return failureCount < 3;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

