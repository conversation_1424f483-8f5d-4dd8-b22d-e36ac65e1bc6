'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { updateCartQuantity } from '@/lib/actions/cart.actions';
import { useToast } from '@/components/providers/ToastProvider';
import { useCartCount } from '@/hooks/use-cart-count';

export function useUpdateCartQuantity() {
  const queryClient = useQueryClient();
  const { showToast } = useToast();
  const { updateCount } = useCartCount();

  return useMutation({
    mutationFn: (data: Parameters<typeof updateCartQuantity>[0]) =>
      updateCartQuantity(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['cart'] });
      // Note: Cart count will be synced when cart page loads, no need to update here
      // as quantity changes don't necessarily change total item count
      showToast('success', 'Cart quantity updated');
    },
    onError: (error) => {
      console.error('Update cart quantity error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to update cart quantity';
      showToast('error', errorMessage);
    },
    retry: (failureCount, error) => {
      // Don't retry on client errors (4xx)
      if (error instanceof Error && error.message.includes('Authentication required')) {
        return false;
      }
      if (error instanceof Error && error.message.includes('Item not found')) {
        return false;
      }
      // Retry on server errors (5xx) or network issues
      return failureCount < 3;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

