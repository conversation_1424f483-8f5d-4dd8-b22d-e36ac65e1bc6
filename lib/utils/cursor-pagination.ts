// lib/utils/cursor-pagination.ts
// Utility functions for cursor-based pagination

/**
 * Cursor structure for pagination
 */
export interface CursorData {
  timestamp: Date;
  id: string | number;
}

/**
 * Encode cursor data to base64 string
 */
export function encodeCursor(data: CursorData): string {
  const cursorString = JSON.stringify({
    timestamp: data.timestamp.toISOString(),
    id: data.id.toString(),
  });
  return Buffer.from(cursorString).toString('base64');
}

/**
 * Decode base64 cursor string to cursor data
 */
export function decodeCursor(cursor: string): CursorData | null {
  try {
    const cursorString = Buffer.from(cursor, 'base64').toString('utf-8');
    const parsed = JSON.parse(cursorString);
    
    return {
      timestamp: new Date(parsed.timestamp),
      id: parsed.id,
    };
  } catch (error) {
    console.error('Invalid cursor:', error);
    return null;
  }
}

/**
 * Build where clause for cursor-based pagination (forward pagination)
 */
export function buildCursorWhereClause(
  cursor: string | undefined,
  timestampField: string = 'created',
  idField: string = 'id'
): Record<string, unknown> {
  if (!cursor) {
    return {};
  }

  const cursorData = decodeCursor(cursor);
  if (!cursorData) {
    return {};
  }

  // For forward pagination, we want records created before the cursor
  // Using compound cursor with timestamp + id for stable ordering
  return {
    OR: [
      {
        [timestampField]: {
          lt: cursorData.timestamp,
        },
      },
      {
        [timestampField]: cursorData.timestamp,
        [idField]: {
          lt: cursorData.id,
        },
      },
    ],
  };
}

/**
 * Build where clause for cursor-based pagination (backward pagination)
 */
export function buildReverseCursorWhereClause(
  cursor: string | undefined,
  timestampField: string = 'created',
  idField: string = 'id'
): Record<string, unknown> {
  if (!cursor) {
    return {};
  }

  const cursorData = decodeCursor(cursor);
  if (!cursorData) {
    return {};
  }

  // For backward pagination, we want records created after the cursor
  return {
    OR: [
      {
        [timestampField]: {
          gt: cursorData.timestamp,
        },
      },
      {
        [timestampField]: cursorData.timestamp,
        [idField]: {
          gt: cursorData.id,
        },
      },
    ],
  };
}

/**
 * Get cursor from the last item in a result set
 */
export function getNextCursor<T extends Record<string, unknown>>(
  items: T[],
  timestampField: string = 'created',
  idField: string = 'id'
): string | undefined {
  if (items.length === 0) {
    return undefined;
  }

  const lastItem = items[items.length - 1];
  const timestamp = lastItem[timestampField];
  const id = lastItem[idField];

  if (!timestamp || id === undefined || id === null) {
    return undefined;
  }

  return encodeCursor({
    timestamp: timestamp instanceof Date ? timestamp : new Date(timestamp as string | number),
    id: id as string | number,
  });
}

/**
 * Get cursor from the first item in a result set
 */
export function getPrevCursor<T extends Record<string, unknown>>(
  items: T[],
  timestampField: string = 'created',
  idField: string = 'id'
): string | undefined {
  if (items.length === 0) {
    return undefined;
  }

  const firstItem = items[0];
  const timestamp = firstItem[timestampField];
  const id = firstItem[idField];

  if (!timestamp || id === undefined || id === null) {
    return undefined;
  }

  return encodeCursor({
    timestamp: timestamp instanceof Date ? timestamp : new Date(timestamp as string | number),
    id: id as string | number,
  });
}

/**
 * Build order by clause for cursor pagination
 * Always orders by timestamp DESC, then by id DESC for stable sorting
 */
export function buildCursorOrderBy(
  timestampField: string = 'created',
  idField: string = 'id'
): Record<string, unknown>[] {
  return [
    { [timestampField]: 'desc' },
    { [idField]: 'desc' },
  ];
}

/**
 * Build order by clause for custom sorting with cursor fallback
 * Adds timestamp and id as secondary sort for stable pagination
 */
export function buildCustomOrderByWithCursor(
  primaryOrderBy: Record<string, unknown>,
  timestampField: string = 'created',
  idField: string = 'id'
): Record<string, unknown>[] {
  return [
    primaryOrderBy,
    { [timestampField]: 'desc' },
    { [idField]: 'desc' },
  ];
}

/**
 * Pagination helper for standard cursor pagination
 */
export interface CursorPaginationOptions {
  cursor?: string;
  limit: number;
  timestampField?: string;
  idField?: string;
}

export interface CursorPaginationResult<T> {
  data: T[];
  hasMore: boolean;
  nextCursor?: string;
  prevCursor?: string;
}

/**
 * Apply cursor pagination to a Prisma query result
 */
export function applyCursorPagination<T extends Record<string, unknown>>(
  items: T[],
  options: CursorPaginationOptions
): CursorPaginationResult<T> {
  const { limit, timestampField = 'created', idField = 'id' } = options;
  
  // Check if there are more items by fetching limit + 1
  const hasMore = items.length > limit;
  const data = hasMore ? items.slice(0, limit) : items;

  return {
    data,
    hasMore,
    nextCursor: hasMore ? getNextCursor(data, timestampField, idField) : undefined,
    prevCursor: data.length > 0 ? getPrevCursor(data, timestampField, idField) : undefined,
  };
}
