// lib/utils/performance-monitoring.ts
// Performance monitoring utilities for tracking Web Vitals and custom metrics

/**
 * Web Vitals metrics
 */
export interface WebVitals {
  // Largest Contentful Paint - when the largest content element is painted
  LCP?: number;
  // First Input Delay - delay between user input and response
  FID?: number;
  // Cumulative Layout Shift - visual stability metric
  CLS?: number;
  // First Contentful Paint - when first content is painted
  FCP?: number;
  // Time to First Byte - server response time
  TTFB?: number;
}

/**
 * Custom metrics for tracking application-specific performance
 */
export interface CustomMetrics {
  // Time to Interactive - when page is fully interactive
  TTI?: number;
  // Cart initialization time
  cartInitTime?: number;
  // Currency detection time
  currencyDetectTime?: number;
  // Auth state check time
  authCheckTime?: number;
  // API response times
  apiResponseTimes?: Record<string, number>;
}

/**
 * Report Web Vitals to analytics service
 * This function is called by Next.js automatically
 */
export function reportWebVitals(metric: Record<string, unknown>) {
  // Only report in production
  if (process.env.NODE_ENV !== 'production') {
    return;
  }

  // Send to analytics service (e.g., Google Analytics, Vercel Analytics, etc.)
  const body = JSON.stringify(metric);

  // Use `navigator.sendBeacon()` if available, otherwise use `fetch()`
  if (navigator.sendBeacon) {
    navigator.sendBeacon('/api/analytics/web-vitals', body);
  } else {
    fetch('/api/analytics/web-vitals', {
      method: 'POST',
      body,
      keepalive: true,
    }).catch(error => {
      console.error('Failed to report Web Vitals:', error);
    });
  }
}

/**
 * Track custom metric
 */
export function trackMetric(name: string, value: number, metadata?: Record<string, unknown>) {
  if (process.env.NODE_ENV !== 'production') {
    console.debug(`[Metric] ${name}: ${value}ms`, metadata);
    return;
  }

  const body = JSON.stringify({
    name,
    value,
    timestamp: Date.now(),
    metadata,
  });

  if (navigator.sendBeacon) {
    navigator.sendBeacon('/api/analytics/custom-metrics', body);
  } else {
    fetch('/api/analytics/custom-metrics', {
      method: 'POST',
      body,
      keepalive: true,
    }).catch(error => {
      console.error('Failed to report custom metric:', error);
    });
  }
}

/**
 * Measure performance of an async operation
 */
export async function measureAsync<T>(
  name: string,
  fn: () => Promise<T>,
  metadata?: Record<string, unknown>
): Promise<T> {
  const startTime = performance.now();

  try {
    const result = await fn();
    const duration = performance.now() - startTime;
    trackMetric(name, duration, metadata);
    return result;
  } catch (error) {
    const duration = performance.now() - startTime;
    trackMetric(name, duration, { ...metadata, error: true });
    throw error;
  }
}

/**
 * Measure performance of a sync operation
 */
export function measureSync<T>(
  name: string,
  fn: () => T,
  metadata?: Record<string, unknown>
): T {
  const startTime = performance.now();

  try {
    const result = fn();
    const duration = performance.now() - startTime;
    trackMetric(name, duration, metadata);
    return result;
  } catch (error) {
    const duration = performance.now() - startTime;
    trackMetric(name, duration, { ...metadata, error: true });
    throw error;
  }
}

/**
 * Get current performance metrics
 */
export function getPerformanceMetrics(): WebVitals & CustomMetrics {
  if (typeof window === 'undefined') {
    return {};
  }

  const metrics: WebVitals & CustomMetrics = {};

  // Get Navigation Timing metrics
  const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
  if (navigation) {
    metrics.TTFB = navigation.responseStart - navigation.fetchStart;
    metrics.FCP = navigation.responseEnd - navigation.fetchStart;
  }

  // Get Paint Timing metrics
  const paintEntries = performance.getEntriesByType('paint');
  for (const entry of paintEntries) {
    if (entry.name === 'first-contentful-paint') {
      metrics.FCP = entry.startTime;
    }
  }

  // Get Largest Contentful Paint
  const lcpEntries = performance.getEntriesByType('largest-contentful-paint');
  if (lcpEntries.length > 0) {
    metrics.LCP = lcpEntries[lcpEntries.length - 1].startTime;
  }

  // Get Cumulative Layout Shift
  const clsEntries = performance.getEntriesByType('layout-shift');
  let cls = 0;
  for (const entry of clsEntries) {
    const layoutShiftEntry = entry as PerformanceEntry & {
      hadRecentInput?: boolean;
      value?: number;
    };
    if (!layoutShiftEntry.hadRecentInput) {
      cls += layoutShiftEntry.value || 0;
    }
  }
  metrics.CLS = cls;

  return metrics;
}

/**
 * Log performance metrics to console (development only)
 */
export function logPerformanceMetrics() {
  if (process.env.NODE_ENV !== 'development') {
    return;
  }

  const metrics = getPerformanceMetrics();
  console.group('📊 Performance Metrics');
  console.table(metrics);
  console.groupEnd();
}

