// lib/utils/static-generation.ts
// Utilities for static generation of pages

import { prisma } from '@/lib/prisma';

/**
 * Get product slugs for static generation
 * Used in generateStaticParams to pre-generate product detail pages
 * @param locale - Language code
 * @param limit - Optional limit for number of products (for faster builds)
 */
export async function getAllProductSlugsForStaticGeneration(locale: string, limit?: number): Promise<string[]> {
  try {
    const translations = await prisma.product_translations.findMany({
      where: {
        language_code: locale,
        product: {
          can_show: true, // Only include visible products
        },
      },
      select: {
        slug: true,
      },
      orderBy: [
        { product: { updated: 'desc' } }, // Most recently updated first
        { product_id: 'asc' },
      ],
      ...(limit && { take: limit }),
    });

    return translations.map(t => t.slug);
  } catch (error) {
    console.error('Error fetching product slugs for static generation:', error);
    return [];
  }
}