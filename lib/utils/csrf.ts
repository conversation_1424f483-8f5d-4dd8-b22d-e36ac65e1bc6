import Tokens from 'csrf';

const tokens = new Tokens();

/**
 * Generate a CSRF token
 */
export function generateCSRFToken(): string {
  return tokens.create(process.env.CSRF_SECRET || 'default-secret-change-in-production');
}

/**
 * Verify a CSRF token
 */
export function verifyCSRFToken(token: string): boolean {
  try {
    return tokens.verify(process.env.CSRF_SECRET || 'default-secret-change-in-production', token);
  } catch {
    return false;
  }
}

/**
 * Middleware to check CSRF token in request headers
 */
export function validateCSRFToken(request: Request): boolean {
  const csrfToken = request.headers.get('x-csrf-token');
  if (!csrfToken) {
    return false;
  }
  return verifyCSRFToken(csrfToken);
}