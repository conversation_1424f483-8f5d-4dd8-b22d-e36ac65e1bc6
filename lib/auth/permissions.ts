// lib/auth/permissions.ts
// RBAC permission checking utilities

import { prisma } from '@/lib/prisma';
import { PermissionAction } from '@/app/generated/prisma';

/**
 * Check if a user has a specific permission
 * @param firebaseUid - The user's Firebase UID
 * @param requiredPermission - The permission to check
 * @returns true if user has the permission, false otherwise
 */
export async function checkPermission(
  firebaseUid: string,
  requiredPermission: PermissionAction
): Promise<boolean> {
  try {
    // Fetch user with their roles and permissions
    const customer = await prisma.customers.findUnique({
      where: { firebase_uid: firebaseUid },
      include: {
        roles: {
          include: {
            role: {
              include: {
                permissions: {
                  include: {
                    permission: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!customer || !customer.roles.length) {
      return false;
    }

    // Check if any of the user's roles have the required permission
    const hasPermission = customer.roles.some(customerRole =>
      customerRole.role.permissions.some(rolePermission =>
        rolePermission.permission.action === requiredPermission
      )
    );

    return hasPermission;
  } catch (error) {
    console.error('Error checking permission:', error);
    return false;
  }
}

/**
 * Get all permissions for a user
 * @param firebaseUid - The user's Firebase UID
 * @returns Array of permission actions
 */
export async function getUserPermissions(
  firebaseUid: string
): Promise<PermissionAction[]> {
  try {
    const customer = await prisma.customers.findUnique({
      where: { firebase_uid: firebaseUid },
      include: {
        roles: {
          include: {
            role: {
              include: {
                permissions: {
                  include: {
                    permission: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!customer || !customer.roles.length) {
      return [];
    }

    // Collect all unique permissions from all roles
    const permissions = new Set<PermissionAction>();
    customer.roles.forEach(customerRole => {
      customerRole.role.permissions.forEach(rolePermission => {
        permissions.add(rolePermission.permission.action);
      });
    });

    return Array.from(permissions);
  } catch (error) {
    console.error('Error fetching user permissions:', error);
    return [];
  }
}

/**
 * Check if user has admin access
 * @param firebaseUid - The user's Firebase UID
 * @returns true if user can access admin dashboard
 */
export async function isAdmin(firebaseUid: string): Promise<boolean> {
  return checkPermission(firebaseUid, PermissionAction.ACCESS_ADMIN_DASHBOARD);
}
