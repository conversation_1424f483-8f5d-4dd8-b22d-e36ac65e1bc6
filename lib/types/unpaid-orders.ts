// lib/types/unpaid-orders.ts
// Type definitions for unpaid orders functionality

import { OrderStatus } from '@/app/generated/prisma';

/**
 * Configuration for unpaid orders status
 */
export interface UnpaidOrdersConfig {
  /** Statuses that are considered unpaid */
  unpaidStatuses: OrderStatus[];
  /** Default polling interval in milliseconds */
  defaultPollingInterval: number;
  /** Cache stale time in milliseconds */
  defaultStaleTime: number;
  /** Maximum retry attempts */
  maxRetryAttempts: number;
  /** Base retry delay in milliseconds */
  baseRetryDelay: number;
}

/**
 * Hook options for unpaid orders
 */
export interface UseUnpaidOrdersOptions {
  enabled?: boolean;
  refetchInterval?: number | false;
  staleTime?: number;
}

/**
 * Return type for unpaid orders hook
 */
export interface UseUnpaidOrdersReturn {
  count: number;
  isLoading: boolean;
  error: Error | null;
  refetch: () => Promise<unknown>;
  invalidate: () => void;
}

/**
 * Context type for unpaid orders provider
 */
export type UnpaidOrdersContextType = UseUnpaidOrdersReturn;

/**
 * Event detail for order status changes
 */
export interface OrderStatusChangeEventDetail {
  orderId: string;
  newStatus: OrderStatus;
  previousStatus?: OrderStatus;
}

/**
 * Custom event for order status changes
 */
export interface OrderStatusChangeEvent extends CustomEvent {
  detail: OrderStatusChangeEventDetail;
}

/**
 * Constants for unpaid orders functionality
 */
export const UNPAID_ORDERS_CONFIG: UnpaidOrdersConfig = {
  unpaidStatuses: [OrderStatus.unpaid],
  defaultPollingInterval: 30000, // 30 seconds
  defaultStaleTime: 10000, // 10 seconds
  maxRetryAttempts: 3,
  baseRetryDelay: 1000, // 1 second
} as const;

/**
 * Event name for order status changes
 */
export const ORDER_STATUS_CHANGE_EVENT = 'order-status-changed' as const;

/**
 * Type guard to check if a status is unpaid
 */
export function isUnpaidStatus(status: OrderStatus): boolean {
  return UNPAID_ORDERS_CONFIG.unpaidStatuses.includes(status);
}

/**
 * Calculate retry delay with exponential backoff
 */
export function calculateRetryDelay(attemptIndex: number): number {
  return Math.min(
    UNPAID_ORDERS_CONFIG.baseRetryDelay * Math.pow(2, attemptIndex),
    30000 // Max 30 seconds
  );
}