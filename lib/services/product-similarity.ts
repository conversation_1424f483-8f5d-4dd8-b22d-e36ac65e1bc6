// lib/services/product-similarity.ts
// Product similarity and recommendation service

import { prisma } from '@/lib/prisma';

export class ProductSimilarityService {
  /**
   * Get similar products for a given product based on category and marketplace
   */
  async getSimilarProducts(productId: number, limit: number = 6) {
    try {
      // Get the original product to find similar ones
      const originalProduct = await prisma.products.findUnique({
        where: { id: productId },
        include: {
          categories: true,
        },
      });

      if (!originalProduct) return [];

      // Find products in same categories and marketplace
      const similarProducts = await prisma.products.findMany({
        where: {
          id: { not: productId }, // Exclude the original product
          can_show: true,
          OR: [
            // Same marketplace
            { marketplace: originalProduct.marketplace },
            // Same categories
            {
              categories: {
                some: {
                  category_id: {
                    in: originalProduct.categories.map(c => c.category_id),
                  },
                },
              },
            },
          ],
        },
        include: {
          translations: { take: 1 },
          product_images: {
            where: { image_type: 'preview' },
            take: 1,
          },
          categories: {
            include: {
              category: {
                include: {
                  translations: { take: 1 },
                },
              },
            },
            take: 2, // Limit categories shown
          },
        },
        take: limit,
        orderBy: {
          // Order by user activity (views) - this uses the existing relation
          user_activity: {
            _count: 'desc',
          },
        },
      });

      return similarProducts;
    } catch (error) {
      console.error('Error fetching similar products:', error);
      return [];
    }
  }

  /**
   * Get popular products based on user activity
   */
  async getPopularProducts(limit: number = 6) {
    try {
      const popularProducts = await prisma.products.findMany({
        where: { can_show: true },
        include: {
          translations: { take: 1 },
          product_images: {
            where: { image_type: 'preview' },
            take: 1,
          },
          categories: {
            include: {
              category: {
                include: {
                  translations: { take: 1 },
                },
              },
            },
            take: 2,
          },
        },
        orderBy: {
          user_activity: {
            _count: 'desc',
          },
        },
        take: limit,
      });

      return popularProducts;
    } catch (error) {
      console.error('Error fetching popular products:', error);
      return [];
    }
  }

  /**
   * Get personalized recommendations for a user based on their viewing history
   */
  async getPersonalizedRecommendations(customerId: number, limit: number = 6) {
    try {
      // Get user's recently viewed products
      const recentActivity = await prisma.user_activity.findMany({
        where: {
          customer_id: customerId,
          activity_type: 'view',
        },
        orderBy: { created: 'desc' },
        take: 10,
        select: { product_id: true },
      });

      if (recentActivity.length === 0) {
        return this.getPopularProducts(limit);
      }

      const viewedProductIds = recentActivity.map(a => a.product_id);

      // Get categories and marketplaces from recently viewed products
      const viewedProducts = await prisma.products.findMany({
        where: {
          id: { in: viewedProductIds },
        },
        select: {
          marketplace: true,
          categories: {
            select: { category_id: true },
          },
        },
      });

      // Collect unique marketplaces and categories
      const marketplaces = new Set(viewedProducts.map(p => p.marketplace));
      const categoryIds = new Set(
        viewedProducts.flatMap(p => p.categories.map(c => c.category_id))
      );

      // Find recommendations based on user's interests
      const recommendations = await prisma.products.findMany({
        where: {
          id: { notIn: viewedProductIds }, // Don't recommend already viewed products
          can_show: true,
          OR: [
            { marketplace: { in: Array.from(marketplaces) } },
            {
              categories: {
                some: {
                  category_id: { in: Array.from(categoryIds) },
                },
              },
            },
          ],
        },
        include: {
          translations: { take: 1 },
          product_images: {
            where: { image_type: 'preview' },
            take: 1,
          },
          categories: {
            include: {
              category: {
                include: {
                  translations: { take: 1 },
                },
              },
            },
            take: 2,
          },
        },
        orderBy: {
          user_activity: {
            _count: 'desc',
          },
        },
        take: limit,
      });

      return recommendations;
    } catch (error) {
      console.error('Error getting personalized recommendations:', error);
      return this.getPopularProducts(limit);
    }
  }
}

export const productSimilarityService = new ProductSimilarityService();