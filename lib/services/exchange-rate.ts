// lib/services/exchange-rate.ts
// Exchange rate service with Next.js Cache API

import { cache } from 'react';

interface ExchangeRateData {
  rates: Record<string, number>;
  base: string;
  timestamp: number;
  expiry: number; // Unix timestamp when cache expires
}

class ExchangeRateService {
  private readonly CACHE_TTL = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
  private readonly API_KEY = process.env.EXCHANGE_RATE_API_KEY;
  private readonly BASE_URL = 'https://v6.exchangerate-api.com/v6';

  // Cached function for fetching exchange rates
  private fetchExchangeRatesCached = cache(async (baseCurrency: string): Promise<ExchangeRateData> => {
    if (!this.API_KEY) {
      throw new Error('Exchange rate API key not configured');
    }

    const response = await fetch(`${this.BASE_URL}/${this.API_KEY}/latest/${baseCurrency}`);

    if (!response.ok) {
      throw new Error(`Exchange rate API error: ${response.status}`);
    }

    const data = await response.json();

    if (data.result !== 'success') {
      throw new Error(`Exchange rate API error: ${data['error-type']}`);
    }

    return {
      rates: data.conversion_rates,
      base: data.base_code,
      timestamp: Date.now(),
      expiry: data.time_next_update_unix * 1000, // Convert to milliseconds
    };
  });

  async getExchangeRate(fromCurrency: string, toCurrency: string): Promise<number> {
    // Same currency conversion
    if (fromCurrency === toCurrency) {
      return 1;
    }

    try {
      // Use Next.js cache for automatic deduplication and caching
      const data = await this.fetchExchangeRatesCached(fromCurrency);

      // Check if cached data is still valid (24 hours)
      if (Date.now() < data.expiry) {
        return data.rates[toCurrency] / data.rates[fromCurrency];
      }

      // If expired, Next.js cache will automatically refetch on next call
      // For now, return the expired data as fallback
      return data.rates[toCurrency] / data.rates[fromCurrency];

    } catch (error) {
      console.warn(`Exchange rate API failed (${fromCurrency} to ${toCurrency}):`, error instanceof Error ? error.message : 'Unknown error');

      // Fallback exchange rates for build time / API failures
      return this.getFallbackExchangeRate(fromCurrency, toCurrency);
    }
  }

  async getExchangeRates(baseCurrency: string = 'CNY'): Promise<Record<string, number>> {
    // Use Next.js cache for automatic deduplication and caching
    const data = await this.fetchExchangeRatesCached(baseCurrency);

    // Check if cached data is still valid (24 hours)
    if (Date.now() < data.expiry) {
      return data.rates;
    }

    // If expired, Next.js cache will automatically refetch on next call
    // For now, return the expired data as fallback
    return data.rates;
  }


  // Convert amount from one currency to another
  async convertCurrency(amount: number, fromCurrency: string, toCurrency: string): Promise<number> {
    if (fromCurrency === toCurrency) {
      return amount;
    }

    const rate = await this.getExchangeRate(fromCurrency, toCurrency);
    return amount * rate;
  }

  /**
   * Get fallback exchange rate when API fails (for build time / offline scenarios)
   */
  private getFallbackExchangeRate(fromCurrency: string, toCurrency: string): number {
    // Static fallback rates (approximate rates as of late 2024)
    // Base currency is CNY (Chinese Yuan)
    const fallbackRates: Record<string, number> = {
      'CNY': 1.0,
      'USD': 0.14,      // 1 CNY ≈ 0.14 USD
      'EUR': 0.13,      // 1 CNY ≈ 0.13 EUR
      'GBP': 0.11,      // 1 CNY ≈ 0.11 GBP
      'JPY': 20.5,      // 1 CNY ≈ 20.5 JPY
      'CAD': 0.19,      // 1 CNY ≈ 0.19 CAD
      'AUD': 0.21,      // 1 CNY ≈ 0.21 AUD
      'CHF': 0.12,      // 1 CNY ≈ 0.12 CHF
      'XAF': 85.0,      // 1 CNY ≈ 85 XAF (Central African CFA Franc)
      'KES': 18.0,      // 1 CNY ≈ 18 KES (Kenyan Shilling)
      'TZS': 365.0,     // 1 CNY ≈ 365 TZS (Tanzanian Shilling)
      'UGX': 525.0,     // 1 CNY ≈ 525 UGX (Ugandan Shilling)
      'ZAR': 2.6,       // 1 CNY ≈ 2.6 ZAR (South African Rand)
      'NGN': 225.0,     // 1 CNY ≈ 225 NGN (Nigerian Naira)
      'GHS': 2.2,       // 1 CNY ≈ 2.2 GHS (Ghanaian Cedi)
    };

    // Get rates relative to CNY
    const fromRate = fallbackRates[fromCurrency] || 1.0;
    const toRate = fallbackRates[toCurrency] || 1.0;

    // Calculate cross rate: (1 CNY = X fromCurrency) and (1 CNY = Y toCurrency)
    // So: 1 fromCurrency = (1/fromRate) CNY = (toRate/fromRate) toCurrency
    return toRate / fromRate;
  }
}

export const exchangeRateService = new ExchangeRateService();