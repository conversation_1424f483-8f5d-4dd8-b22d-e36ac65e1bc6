// lib/services/geolocation.ts
// Geolocation service for detecting user country

interface GeolocationData {
  country_code: string;
  country_name: string;
  city?: string;
  region?: string;
}

class GeolocationService {
  private readonly API_URL = 'https://ipapi.co/json/';

  async getUserCountry(): Promise<string | null> {
    try {
      // Prevent multiple simultaneous geolocation requests
      if (this.getUserCountryPromise) {
        return await this.getUserCountryPromise;
      }

      this.getUserCountryPromise = fetch(this.API_URL, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
      }).then(async (response) => {
        if (!response.ok) {
          throw new Error(`Geolocation API error: ${response.status}`);
        }

        const data: GeolocationData = await response.json();

        if (data.country_code) {
          return data.country_code.toUpperCase();
        }

        return null;
      }).catch((error) => {
        console.error('Error fetching geolocation:', error);
        return null;
      }).finally(() => {
        // Clear the promise after completion
        this.getUserCountryPromise = null;
      });

      return await this.getUserCountryPromise;
    } catch (error) {
      console.error('Error fetching geolocation:', error);
      return null;
    }
  }

  private getUserCountryPromise: Promise<string | null> | null = null;
}

export const geolocationService = new GeolocationService();