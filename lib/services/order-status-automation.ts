// lib/services/order-status-automation.ts
// Automatic order status updates based on warehouse receipts

import { prisma } from '@/lib/prisma';
import { OrderStatus, WarehouseReceiptStatus } from '@/app/generated/prisma';

export class OrderStatusAutomationService {
  /**
   * Check and update order status based on warehouse receipt status
   * This should be called whenever a warehouse receipt status changes
   */
  async updateOrderStatusFromReceipts(orderId: string) {
    try {
      // Get the order with all its items and receipts
      const order = await prisma.orders.findUnique({
        where: { id: orderId },
        include: {
          order_items: {
            include: {
              warehouse_receipts: true,
            },
          },
        },
      });

      if (!order) return;

      // Check if all order items have at least one matched receipt
      const allItemsReceived = order.order_items.every(item =>
        item.warehouse_receipts.some(receipt =>
          receipt.status === WarehouseReceiptStatus.matched
        )
      );

      // If all items are received and order is still pending/processing, update to shipped
      if (allItemsReceived && (order.status === OrderStatus.unpaid || order.status === OrderStatus.paid || order.status === OrderStatus.processing)) {
        await prisma.orders.update({
          where: { id: orderId },
          data: { status: OrderStatus.shipped },
        });

        console.log(`Order ${orderId} status updated to shipped - all items received`);
      }
    } catch (error) {
      console.error('Error updating order status from receipts:', error);
    }
  }

  /**
   * Update order status to delivered when tracking number is provided
   * This should be called when tracking information is added to receipts
   */
  async updateOrderStatusOnTrackingAdded(orderId: string) {
    try {
      const order = await prisma.orders.findUnique({
        where: { id: orderId },
        include: {
          order_items: {
            include: {
              warehouse_receipts: true,
            },
          },
        },
      });

      if (!order) return;

      // Check if any receipt has tracking information and is shipped
      const hasTracking = order.order_items.some(item =>
        item.warehouse_receipts.some(receipt =>
          receipt.status === WarehouseReceiptStatus.shipped &&
          receipt.tracking_number &&
          receipt.carrier
        )
      );

      // If tracking is provided and order is shipped, update to delivered
      if (hasTracking && order.status === OrderStatus.shipped) {
        await prisma.orders.update({
          where: { id: orderId },
          data: { status: OrderStatus.delivered },
        });

        console.log(`Order ${orderId} status updated to delivered - tracking information added`);
      }
    } catch (error) {
      console.error('Error updating order status on tracking added:', error);
    }
  }

  /**
   * Batch update order statuses for all orders
   * This can be run as a scheduled job
   */
  async batchUpdateOrderStatuses() {
    try {
      // Find all orders that might need status updates
      const ordersToCheck = await prisma.orders.findMany({
        where: {
          status: {
            in: [OrderStatus.unpaid, OrderStatus.paid, OrderStatus.processing, OrderStatus.shipped],
          },
        },
        include: {
          order_items: {
            include: {
              warehouse_receipts: true,
            },
          },
        },
      });

      for (const order of ordersToCheck) {
        await this.updateOrderStatusFromReceipts(order.id);
        await this.updateOrderStatusOnTrackingAdded(order.id);
      }

      console.log(`Batch updated ${ordersToCheck.length} orders`);
    } catch (error) {
      console.error('Error in batch order status update:', error);
    }
  }

  /**
   * Get order status summary for reporting
   */
  async getOrderStatusSummary() {
    const statusCounts = await prisma.orders.groupBy({
      by: ['status'],
      _count: {
        id: true,
      },
    });

    return statusCounts.reduce((acc, curr) => {
      acc[curr.status] = curr._count.id;
      return acc;
    }, {} as Record<string, number>);
  }
}

export const orderStatusAutomationService = new OrderStatusAutomationService();