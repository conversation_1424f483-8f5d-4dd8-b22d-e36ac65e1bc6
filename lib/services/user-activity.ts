// lib/services/user-activity.ts
// User activity tracking service

import { prisma } from '@/lib/prisma';
import { UserActivityType, user_activity } from '@/app/generated/prisma';

export class UserActivityService {
  /**
   * Track a user activity event
   */
  async trackActivity(data: {
    customerId: number;
    productId: number;
    activityType: UserActivityType;
    sessionId: string;
    marketplace: string;
  }) {
    try {
      await prisma.user_activity.create({
        data: {
          customer_id: data.customerId,
          product_id: data.productId,
          activity_type: data.activityType,
          session_id: data.sessionId,
          marketplace: data.marketplace,
        },
      });
    } catch (error) {
      console.error('Error tracking user activity:', error);
      // Don't throw - activity tracking shouldn't break the main flow
    }
  }

  /**
   * Track product view
   */
  async trackProductView(customerId: number, productId: number, sessionId: string, marketplace: string) {
    await this.trackActivity({
      customerId,
      productId,
      activityType: UserActivityType.view,
      sessionId,
      marketplace,
    });
  }

  /**
   * Track cart addition
   */
  async trackCartAdd(customerId: number, productId: number, sessionId: string, marketplace: string) {
    await this.trackActivity({
      customerId,
      productId,
      activityType: UserActivityType.cart_add,
      sessionId,
      marketplace,
    });
  }

  /**
   * Track purchase
   */
  async trackPurchase(customerId: number, productId: number, sessionId: string, marketplace: string) {
    await this.trackActivity({
      customerId,
      productId,
      activityType: UserActivityType.purchase,
      sessionId,
      marketplace,
    });
  }

  /**
   * Get popular products based on activity
   */
  async getPopularProducts(limit: number = 10, days: number = 30) {
    const dateThreshold = new Date();
    dateThreshold.setDate(dateThreshold.getDate() - days);

    const popularProducts = await prisma.user_activity.groupBy({
      by: ['product_id'],
      where: {
        created: {
          gte: dateThreshold,
        },
      },
      _count: {
        id: true,
      },
      orderBy: {
        _count: {
          id: 'desc',
        },
      },
      take: limit,
    });

    const productIds = popularProducts.map(p => p.product_id);

    if (productIds.length === 0) {
      return [];
    }

    const products = await prisma.products.findMany({
      where: {
        id: {
          in: productIds,
        },
        can_show: true,
      },
      include: {
        translations: {
          take: 1,
        },
        product_images: {
          where: {
            image_type: 'preview',
          },
          take: 1,
        },
      },
    });

    // Sort by popularity
    return products.sort((a, b) => {
      const aPopularity = popularProducts.find(p => p.product_id === a.id)?._count.id || 0;
      const bPopularity = popularProducts.find(p => p.product_id === b.id)?._count.id || 0;
      return bPopularity - aPopularity;
    });
  }

  /**
   * Get user behavior insights
   */
  async getUserInsights(customerId: number) {
    const activities = await prisma.user_activity.findMany({
      where: {
        customer_id: customerId,
      },
      orderBy: {
        created: 'desc',
      },
      take: 100, // Last 100 activities
    });

    const insights = {
      totalViews: activities.filter(a => a.activity_type === UserActivityType.view).length,
      totalCartAdds: activities.filter(a => a.activity_type === UserActivityType.cart_add).length,
      totalPurchases: activities.filter(a => a.activity_type === UserActivityType.purchase).length,
      favoriteMarketplace: this.getFavoriteMarketplace(activities),
      recentlyViewed: activities
        .filter(a => a.activity_type === UserActivityType.view)
        .slice(0, 5)
        .map(a => a.product_id),
    };

    return insights;
  }

  private getFavoriteMarketplace(activities: user_activity[]) {
    const marketplaceCount: Record<string, number> = {};

    activities.forEach(activity => {
      if (activity.marketplace) {
        marketplaceCount[activity.marketplace] = (marketplaceCount[activity.marketplace] || 0) + 1;
      }
    });

    const favorite = Object.entries(marketplaceCount).sort(([,a], [,b]) => b - a)[0];
    return favorite ? favorite[0] : null;
  }
}

export const userActivityService = new UserActivityService();