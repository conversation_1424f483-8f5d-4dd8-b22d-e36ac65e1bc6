// lib/firebase/server.ts
// Firebase Admin SDK initialization for server-side operations

import { cert, getApps, initializeApp } from 'firebase-admin/app';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';

// Initialize Firebase Admin SDK (singleton pattern)
export function initializeFirebaseAdmin() {
  if (getApps().length === 0) {
    initializeApp({
      credential: cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        // Handle private key with escaped newlines
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      }),
    });
  }
}

// Get auth instance
export function getAdminAuth() {
  initializeFirebaseAdmin();
  return getAuth();
}

// Get Firestore instance
export function getAdminFirestore() {
  initializeFirebaseAdmin();
  return getFirestore();
}
