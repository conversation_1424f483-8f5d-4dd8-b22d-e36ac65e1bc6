'use server';

// lib/actions/user.actions.ts
// Server actions for user profile management

import { Prisma } from '@/app/generated/prisma';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from './auth.actions';
import { revalidatePath } from 'next/cache';
import type { Address, UserProfile } from '@/lib/types';

/**
 * Get full user profile
 */
export async function getUserProfile(): Promise<UserProfile | null> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return null;
    }

    const customer = await prisma.customers.findUnique({
      where: { id: user.customerId },
      select: {
        id: true,
        firebase_uid: true,
        email: true,
        full_name: true,
        phone: true,
        addresses: true,
        preferred_currency: true,
        created: true,
        updated: true,
      },
    });

    if (!customer) {
      return null;
    }

    return {
      id: customer.id,
      firebaseUid: customer.firebase_uid,
      email: customer.email,
      fullName: customer.full_name,
      phone: customer.phone,
      addresses: customer.addresses as Prisma.JsonValue,
      preferredCurrency: customer.preferred_currency,
      created: customer.created,
      updated: customer.updated,
    };
  } catch (error) {
    console.error('Error getting user profile:', error);
    return null;
  }
}

/**
 * Update user profile
 */
export async function updateProfile(data: {
  fullName?: string;
  phone?: string;
  preferredCurrency?: string;
}) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return {
        success: false,
        error: 'Not authenticated',
      };
    }

    await prisma.customers.update({
      where: { id: user.customerId },
      data: {
        full_name: data.fullName,
        phone: data.phone,
        preferred_currency: data.preferredCurrency,
      },
    });

    revalidatePath('/account/profile');

    return {
      success: true,
    };
  } catch (error) {
    console.error('Error updating profile:', error);
    return {
      success: false,
      error: 'Failed to update profile',
    };
  }
}

/**
 * Get user addresses
 */
export async function getUserAddresses() {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return null;
    }

    const customer = await prisma.customers.findUnique({
      where: { id: user.customerId },
      select: { addresses: true },
    });

    return customer?.addresses as unknown as Address[] | null;
  } catch (error) {
    console.error('Error fetching addresses:', error);
    return null;
  }
}

/**
 * Add a new address
 */
export async function addAddress(address: Omit<Address, 'id'>) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return {
        success: false,
        error: 'Not authenticated',
      };
    }

    const customer = await prisma.customers.findUnique({
      where: { id: user.customerId },
      select: { addresses: true },
    });

    const currentAddresses = (customer?.addresses as unknown as Address[]) || [];
    const newAddress: Address = {
      ...address,
      id: crypto.randomUUID(),
    };

    // If this is the first address or marked as default, set it as default
    if (currentAddresses.length === 0 || address.isDefault) {
      // Remove default from other addresses
      currentAddresses.forEach(addr => addr.isDefault = false);
      newAddress.isDefault = true;
    }

    await prisma.customers.update({
      where: { id: user.customerId },
      data: {
        addresses: [...currentAddresses, newAddress] as unknown as Prisma.InputJsonValue,
      },
    });

    revalidatePath('/account/profile');
    revalidatePath('/checkout/shipping');

    return {
      success: true,
      address: newAddress,
    };
  } catch (error) {
    console.error('Error adding address:', error);
    return {
      success: false,
      error: 'Failed to add address',
    };
  }
}

/**
 * Update an existing address
 */
export async function updateAddress(addressId: string, updates: Partial<Address>) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return {
        success: false,
        error: 'Not authenticated',
      };
    }

    const customer = await prisma.customers.findUnique({
      where: { id: user.customerId },
      select: { addresses: true },
    });

    const addresses = (customer?.addresses as unknown as Address[]) || [];
    const addressIndex = addresses.findIndex(addr => addr.id === addressId);

    if (addressIndex === -1) {
      return {
        success: false,
        error: 'Address not found',
      };
    }

    // If setting as default, remove default from others
    if (updates.isDefault) {
      addresses.forEach(addr => addr.isDefault = false);
    }

    addresses[addressIndex] = { ...addresses[addressIndex], ...updates };

    await prisma.customers.update({
      where: { id: user.customerId },
      data: {
        addresses: addresses as unknown as Prisma.InputJsonValue,
      },
    });

    revalidatePath('/account/profile');
    revalidatePath('/checkout/shipping');

    return {
      success: true,
    };
  } catch (error) {
    console.error('Error updating address:', error);
    return {
      success: false,
      error: 'Failed to update address',
    };
  }
}

/**
 * Delete an address
 */
export async function deleteAddress(addressId: string) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return {
        success: false,
        error: 'Not authenticated',
      };
    }

    const customer = await prisma.customers.findUnique({
      where: { id: user.customerId },
      select: { addresses: true },
    });

    const addresses = (customer?.addresses as unknown as Address[]) || [];
    const filteredAddresses = addresses.filter(addr => addr.id !== addressId);

    await prisma.customers.update({
      where: { id: user.customerId },
      data: {
        addresses: filteredAddresses as unknown as Prisma.InputJsonValue,
      },
    });

    revalidatePath('/account/profile');
    revalidatePath('/checkout/shipping');

    return {
      success: true,
    };
  } catch (error) {
    console.error('Error deleting address:', error);
    return {
      success: false,
      error: 'Failed to delete address',
    };
  }
}
