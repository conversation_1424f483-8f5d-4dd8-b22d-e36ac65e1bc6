'use server';

// lib/actions/product-static.actions.ts
// Static product actions for ISR - no personalized pricing, only static data

import { prisma } from '@/lib/prisma';
import { DEFAULT_CURRENCY } from '@/lib/constants';
import type { ProductWithDetails } from '@/lib/types';

/**
 * Get product by slug for static generation (ISR)
 * Returns product with default currency pricing only - no personalization
 */
export async function getProductBySlugStatic(
  slug: string,
  locale: string = 'en'
): Promise<ProductWithDetails | null> {
  try {
    // First find the product by slug in translations
    const translation = await prisma.product_translations.findFirst({
      where: {
        slug,
        language_code: locale,
      },
      select: {
        product_id: true,
      },
    });

    if (!translation) {
      return null;
    }

    // Fetch full product details with limits - no pricing calculations
    const product = await prisma.products.findUnique({
      where: {
        id: translation.product_id,
        can_show: true,
      },
      include: {
        translations: {
          where: { language_code: locale },
        },
        variants: {
          include: {
            translations: {
              where: { language_code: locale },
            },
          },
        },
        offers: {
          orderBy: { min_quantity: 'asc' },
        },
        product_images: {
          orderBy: { id: 'asc' },
        },
        product_attributes: {
          include: {
            translations: {
              where: { language_code: locale },
            },
          },
        },
        categories: {
          include: {
            category: {
              include: {
                translations: {
                  where: { language_code: locale },
                },
              },
            },
          },
        },
      },
    });

    if (!product) {
      return null;
    }

    // Add default currency display prices to offers (no personalization)
    const processedProduct = {
      ...product,
      offers: product.offers.map(offer => ({
        ...offer,
        display_price: Number(offer.price_low),
        display_currency: DEFAULT_CURRENCY,
        exchange_rate: 1,
      })),
    };

    return JSON.parse(JSON.stringify(processedProduct));
  } catch (error) {
    console.error('Error fetching product by slug (static):', error);
    return null;
  }
}

/**
 * Get related products for static generation (ISR)
 * Returns products with default currency pricing only - no personalization
 */
export async function getRelatedProductsStatic(
  productId: number,
  locale: string = 'en',
  limit: number = 6
) {
  try {
    // Get product categories (using explicit join table)
    const product = await prisma.products.findUnique({
      where: { id: productId },
      select: {
        categories: {
          take: 1,
          select: { category_id: true },
        },
      },
    });

    if (!product || !product.categories.length) {
      return [];
    }

    const categoryId = product.categories[0].category_id;

    // Find related products in same category (using explicit join table)
    const relatedProducts = await prisma.products.findMany({
      where: {
        can_show: true,
        id: { not: productId },
        categories: {
          some: {
            category_id: categoryId,
          },
        },
      },
      take: limit,
      select: {
        id: true,
        original_name: true,
        marketplace: true,
        translations: {
          where: { language_code: locale },
          take: 1,
          select: {
            name: true,
            slug: true,
            language_code: true,
          },
        },
        product_images: {
          where: { image_type: 'preview' },
          take: 1,
          orderBy: { id: 'asc' },
          select: {
            image_url: true,
            image_type: true,
          },
        },
        offers: {
          orderBy: { price_low: 'asc' },
          take: 1,
          select: {
            id: true,
            price_low: true,
            price_high: true,
            currency: true,
            min_quantity: true,
          },
        },
        categories: {
          take: 3,
          select: {
            category: {
              select: {
                id: true,
                translations: {
                  where: { language_code: locale },
                  take: 1,
                  select: {
                    name: true,
                    slug: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    // Add default currency display prices (no personalization)
    const processedProducts = relatedProducts.map(product => ({
      ...product,
      offers: product.offers.map(offer => ({
        ...offer,
        display_price: Number(offer.price_low),
        display_currency: DEFAULT_CURRENCY,
        exchange_rate: 1,
      })),
    }));

    return JSON.parse(JSON.stringify(processedProducts));
  } catch (error) {
    console.error('Error fetching related products (static):', error);
    return [];
  }
}
