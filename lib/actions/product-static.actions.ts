'use server';

// lib/actions/product-static.actions.ts
// Static product actions for ISR - no personalized pricing, only static data

import { prisma } from '@/lib/prisma';
import { DEFAULT_CURRENCY, PRODUCTS_PER_PAGE } from '@/lib/constants';
import type { ProductWithDetails, ProductListItem, PaginatedResponse, ProductFilters, CategoryWithTranslations } from '@/lib/types';
import { Prisma, $Enums } from '@/app/generated/prisma';
import { unstable_cache } from 'next/cache';
import {
  buildCursorWhereClause,
  buildCursorOrderBy,
  buildCustomOrderByWithCursor,
  applyCursorPagination
} from '@/lib/utils/cursor-pagination';

/**
 * Get product by slug for static generation (ISR)
 * Returns product with default currency pricing only - no personalization
 */
export async function getProductBySlugStatic(
  slug: string,
  locale: string = 'en'
): Promise<ProductWithDetails | null> {
  try {
    // First find the product by slug in translations
    const translation = await prisma.product_translations.findFirst({
      where: {
        slug,
        language_code: locale,
      },
      select: {
        product_id: true,
      },
    });

    if (!translation) {
      return null;
    }

    // Fetch full product details with limits - no pricing calculations
    const product = await prisma.products.findUnique({
      where: {
        id: translation.product_id,
        can_show: true,
      },
      include: {
        translations: {
          where: { language_code: locale },
        },
        variants: {
          include: {
            translations: {
              where: { language_code: locale },
            },
          },
        },
        offers: {
          orderBy: { min_quantity: 'asc' },
        },
        product_images: {
          orderBy: { id: 'asc' },
        },
        product_attributes: {
          include: {
            translations: {
              where: { language_code: locale },
            },
          },
        },
        categories: {
          include: {
            category: {
              include: {
                translations: {
                  where: { language_code: locale },
                },
              },
            },
          },
        },
      },
    });

    if (!product) {
      return null;
    }

    // Add default currency display prices to offers (no personalization)
    const processedProduct = {
      ...product,
      offers: product.offers.map(offer => ({
        ...offer,
        display_price: Number(offer.price_low),
        display_currency: DEFAULT_CURRENCY,
        exchange_rate: 1,
      })),
    };

    return JSON.parse(JSON.stringify(processedProduct));
  } catch (error) {
    console.error('Error fetching product by slug (static):', error);
    return null;
  }
}

/**
 * Get related products for static generation (ISR)
 * Returns products with default currency pricing only - no personalization
 */
export async function getRelatedProductsStatic(
  productId: number,
  locale: string = 'en',
  limit: number = 6
) {
  try {
    // Get product categories (using explicit join table)
    const product = await prisma.products.findUnique({
      where: { id: productId },
      select: {
        categories: {
          take: 1,
          select: { category_id: true },
        },
      },
    });

    if (!product || !product.categories.length) {
      return [];
    }

    const categoryId = product.categories[0].category_id;

    // Find related products in same category (using explicit join table)
    const relatedProducts = await prisma.products.findMany({
      where: {
        can_show: true,
        id: { not: productId },
        categories: {
          some: {
            category_id: categoryId,
          },
        },
      },
      take: limit,
      select: {
        id: true,
        original_name: true,
        marketplace: true,
        translations: {
          where: { language_code: locale },
          take: 1,
          select: {
            name: true,
            slug: true,
            language_code: true,
          },
        },
        product_images: {
          where: { image_type: 'preview' },
          take: 1,
          orderBy: { id: 'asc' },
          select: {
            image_url: true,
            image_type: true,
          },
        },
        offers: {
          orderBy: { price_low: 'asc' },
          take: 1,
          select: {
            id: true,
            price_low: true,
            price_high: true,
            currency: true,
            min_quantity: true,
          },
        },
        categories: {
          take: 3,
          select: {
            category: {
              select: {
                id: true,
                translations: {
                  where: { language_code: locale },
                  take: 1,
                  select: {
                    name: true,
                    slug: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    // Add default currency display prices (no personalization)
    const processedProducts = relatedProducts.map(product => ({
      ...product,
      offers: product.offers.map(offer => ({
        ...offer,
        display_price: Number(offer.price_low),
        display_currency: DEFAULT_CURRENCY,
        exchange_rate: 1,
      })),
    }));

    return JSON.parse(JSON.stringify(processedProducts));
  } catch (error) {
    console.error('Error fetching related products (static):', error);
    return [];
  }
}

/**
 * Get products for static generation (ISR) - Product Listing Page
 * Returns products with default currency pricing only - no personalization
 * This function is used at build time to generate static product listing pages
 */
async function _getProductsStatic(
  filters: ProductFilters = {},
  locale: string = 'en'
): Promise<PaginatedResponse<ProductListItem>> {
  try {
    const {
      search,
      categoryId,
      marketplace,
      minPrice,
      maxPrice,
      sortBy = 'newest',
      cursor,
      limit = PRODUCTS_PER_PAGE,
    } = filters;

    // Build where clause - same as dynamic version but for static generation
    const where: Prisma.productsWhereInput = {
      can_show: true, // Only show visible products
      ...buildCursorWhereClause(cursor, 'created', 'id'),
    };

    // Add search filter
    if (search) {
      where.OR = [
        {
          original_name: {
            contains: search,
            mode: 'insensitive',
          },
        },
        {
          translations: {
            some: {
              language_code: locale,
              name: {
                contains: search,
                mode: 'insensitive',
              },
            },
          },
        },
      ];
    }

    // Add category filter
    if (categoryId) {
      where.categories = {
        some: {
          category_id: categoryId,
        },
      };
    }

    // Add marketplace filter
    if (marketplace) {
      where.marketplace = marketplace as $Enums.Marketplace;
    }

    // Add price filters (using min_price_cny for performance)
    if (minPrice !== undefined || maxPrice !== undefined) {
      where.min_price_cny = {};
      if (minPrice !== undefined) {
        where.min_price_cny.gte = minPrice;
      }
      if (maxPrice !== undefined) {
        where.min_price_cny.lte = maxPrice;
      }
    }

    // Build order by clause
    let orderBy: Prisma.productsOrderByWithRelationInput[];
    switch (sortBy) {
      case 'price_asc':
        orderBy = buildCustomOrderByWithCursor({ min_price_cny: 'asc' });
        break;
      case 'price_desc':
        orderBy = buildCustomOrderByWithCursor({ min_price_cny: 'desc' });
        break;
      case 'popular':
        // For now, use created date as popularity proxy
        orderBy = buildCursorOrderBy('created', 'id');
        break;
      case 'newest':
      default:
        orderBy = buildCursorOrderBy('created', 'id');
        break;
    }

    // Execute query with cursor pagination - same select as dynamic version
    const products = await prisma.products.findMany({
      where,
      orderBy,
      take: limit + 1, // Fetch one extra to determine hasMore
      select: {
        id: true,
        original_name: true,
        marketplace: true,
        translations: {
          where: { language_code: locale },
          take: 1,
          select: {
            name: true,
            slug: true,
            language_code: true,
          },
        },
        product_images: {
          where: { image_type: 'preview' },
          take: 1, // Only get first preview image for listing
          orderBy: { id: 'asc' },
          select: {
            image_url: true,
            image_type: true,
          },
        },
        offers: {
          orderBy: { price_low: 'asc' },
          take: 1, // Only get lowest price offer
          select: {
            price_low: true,
            price_high: true,
            currency: true,
            min_quantity: true,
          },
        },
        categories: {
          take: 3, // Limit categories shown
          select: {
            category: {
              select: {
                id: true,
                translations: {
                  where: { language_code: locale },
                  take: 1,
                  select: {
                    name: true,
                    slug: true,
                  },
                },
              },
            },
          },
        },
        // Include created and id for cursor pagination
        created: true,
      },
    });

    // Apply cursor pagination logic
    const paginationResult = applyCursorPagination(products, {
      cursor,
      limit,
      timestampField: 'created',
      idField: 'id',
    });

    // Process products with static pricing (no personalization)
    const finalProducts = paginationResult.data.map(product => ({
      ...product,
      offers: product.offers.map(offer => ({
        ...offer,
        // Add static display pricing in default currency
        display_price: Number(offer.price_low),
        display_currency: DEFAULT_CURRENCY,
        exchange_rate: 1,
      })),
    }));

    return JSON.parse(JSON.stringify({
      data: finalProducts as ProductListItem[],
      pagination: {
        limit,
        hasMore: paginationResult.hasMore,
        nextCursor: paginationResult.nextCursor,
        prevCursor: paginationResult.prevCursor,
      },
    }));
  } catch (error) {
    console.error('Error fetching products (static):', error);
    return {
      data: [],
      pagination: {
        limit: PRODUCTS_PER_PAGE,
        hasMore: false,
        nextCursor: undefined,
        prevCursor: undefined,
      },
    };
  }
}

// Cached wrapper for getProductsStatic with tag-based revalidation
export const getProductsStatic = unstable_cache(
  _getProductsStatic,
  ['products-static'],
  {
    tags: ['products', 'product-listings'],
    revalidate: 3600, // 1 hour
  }
);

/**
 * Get categories for static generation (ISR)
 * Returns categories with product counts - used at build time
 */
async function _getCategoriesStatic(locale: string = 'en'): Promise<CategoryWithTranslations[]> {
  try {
    // Using select for optimal performance - only fetch needed fields
    const categories = await prisma.categories.findMany({
      where: {
        parent_id: null, // Only top-level categories
        products: {
          some: {
            product: {
              can_show: true,
            },
          },
        },
      },
      take: 20, // Limit to 20 main categories
      select: {
        id: true,
        parent_id: true,
        created: true,
        updated: true,
        translations: {
          where: { language_code: locale },
          select: {
            id: true,
            category_id: true,
            language_code: true,
            name: true,
            slug: true,
          },
        },
        _count: {
          select: {
            products: {
              where: {
                product: {
                  can_show: true,
                },
              },
            },
          },
        },
      },
      orderBy: {
        id: 'asc',
      },
    });

    return JSON.parse(JSON.stringify(categories));
  } catch (error) {
    console.error('Error fetching categories (static):', error);
    return [];
  }
}

// Cached wrapper for getCategoriesStatic with tag-based revalidation
export const getCategoriesStatic = unstable_cache(
  _getCategoriesStatic,
  ['categories-static'],
  {
    tags: ['categories', 'product-listings'],
    revalidate: 3600, // 1 hour
  }
);

/**
 * Generate static params for popular product listing combinations
 * This helps pre-generate the most common filter combinations
 */
export async function generatePopularListingParams(locale: string = 'en') {
  try {
    const params = [];

    // Base listing page
    params.push({ locale });

    // Get top 5 categories for pre-generation
    const topCategories = await prisma.categories.findMany({
      where: {
        parent_id: null,
        products: {
          some: {
            product: {
              can_show: true,
            },
          },
        },
      },
      take: 5,
      select: {
        id: true,
        _count: {
          select: {
            products: {
              where: {
                product: {
                  can_show: true,
                },
              },
            },
          },
        },
      },
      orderBy: {
        products: {
          _count: 'desc',
        },
      },
    });

    // Add popular category combinations
    for (const category of topCategories) {
      params.push({
        locale,
        searchParams: { category: category.id.toString() }
      });
    }

    // Add popular sort combinations
    const popularSorts = ['newest', 'price_asc', 'price_desc'];
    for (const sortBy of popularSorts) {
      params.push({
        locale,
        searchParams: { sortBy }
      });
    }

    return params;
  } catch (error) {
    console.error('Error generating popular listing params:', error);
    return [{ locale }];
  }
}
