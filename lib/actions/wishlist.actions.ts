// lib/actions/wishlist.actions.ts
// Server actions for wishlist functionality

'use server';

import { getCurrentUser } from '@/lib/actions/auth.actions';
import { prisma } from '@/lib/prisma';
import { revalidatePath } from 'next/cache';
import { pricingService } from '@/lib/services/pricing';

export interface WishlistItem {
  id: string;
  productId: number;
  variantId?: number;
  priceWhenAdded: number;
  currencyWhenAdded: string;
  currentPrice: number;
  currentCurrency: string;
  notes?: string;
  createdAt: Date;
  product: {
    id: number;
    originalName?: string;
    marketplace: string;
    translations: Array<{
      name: string;
      slug: string;
    }>;
    productImages: Array<{
      imageUrl: string;
      imageType: string;
    }>;
    offers: Array<{
      priceHigh?: number;
      priceLow: number;
      currency: string;
      minQuantity?: number;
    }>;
  };
  variant?: {
    id: string;
    originalVariantName: string;
    originalVariantType: string;
    priceLow: number;
    currency: string;
  };
}

/**
 * Add item to wishlist (overloaded for API compatibility)
 */
export async function addToWishlist(
  userId: string,
  productId: number
): Promise<{ success: boolean; error?: string }>;
export async function addToWishlist(data: {
  productId: number;
  variantId?: number;
  notes?: string;
}): Promise<{ success: boolean; error?: string }>;
export async function addToWishlist(
  userIdOrData: string | { productId: number; variantId?: number; notes?: string },
  productId?: number
): Promise<{ success: boolean; error?: string }> {
  try {
    let user;
    let data: { productId: number; variantId?: number; notes?: string };

    // Handle overloaded function signatures
    if (typeof userIdOrData === 'string') {
      // API call format: addToWishlist(userId, productId)
      const { getAdminFirestore } = await import('@/lib/firebase/server');
      const adminDb = getAdminFirestore();
      const userDoc = await adminDb.doc(`users/${userIdOrData}`).get();

      if (!userDoc.exists) {
        return { success: false, error: 'User not found' };
      }

      const userData = userDoc.data();
      user = {
        uid: userIdOrData,
        customerId: userData?.customerId,
      };

      data = { productId: productId! };
    } else {
      // Original format: addToWishlist(data)
      user = await getCurrentUser();
      if (!user) {
        return { success: false, error: 'Authentication required' };
      }
      data = userIdOrData;
    }

    const { productId: finalProductId, variantId, notes } = data;

    // Get product data for price tracking
    const product = await prisma.products.findUnique({
      where: {
        id: finalProductId,
        can_show: true,
      },
      select: {
        id: true,
        marketplace: true,
        offers: {
          orderBy: { min_quantity: 'asc' },
          take: 1,
          select: { price_low: true },
        },
        variants: variantId ? {
          where: { id: variantId },
          take: 1,
          select: { price_low: true, currency: true },
        } : false,
      },
    });

    if (!product) {
      return { success: false, error: 'Product not found' };
    }

    // Get user's currency preference
    let userCurrency = 'USD';
    try {
      const { getAdminFirestore } = await import('@/lib/firebase/server');
      const adminDb = getAdminFirestore();
      const userDoc = await adminDb.doc(`users/${user.uid}`).get();
      if (userDoc.exists) {
        userCurrency = userDoc.data()?.preferredCurrency || 'USD';
      }
    } catch (error) {
      console.error('Error getting user currency:', error);
    }

    // Calculate current price for tracking
    let costPriceCNY: number;
    if (variantId && product.variants && product.variants.length > 0) {
      costPriceCNY = Number(product.variants[0].price_low);
    } else {
      costPriceCNY = Number(product.offers[0]?.price_low || 0);
    }

    const pricingResult = await pricingService.calculatePrice(costPriceCNY, {
      productId: finalProductId,
      marketplace: product.marketplace,
      userCurrency,
    });

    // Check if item already exists
    const existingItem = await prisma.wishlist_items.findFirst({
      where: {
        customer_id: user.customerId,
        product_id: finalProductId,
        variant_id: variantId ? variantId : null,
      },
    });

    if (existingItem) {
      return { success: false, error: 'Item already in wishlist' };
    }

    // Add to wishlist
    await prisma.wishlist_items.create({
      data: {
        customer_id: user.customerId,
        product_id: finalProductId,
        variant_id: variantId ? variantId : null,
        price_when_added: pricingResult.displayPrice,
        currency_when_added: pricingResult.currency,
        notes,
      },
    });

    revalidatePath('/account/wishlist');
    return { success: true };
  } catch (error) {
    console.error('Error adding to wishlist:', error);
    return { success: false, error: 'Failed to add item to wishlist' };
  }
}

/**
 * Remove item from wishlist (overloaded for API compatibility)
 */
export async function removeFromWishlist(
  userId: string,
  productId: number
): Promise<{ success: boolean; error?: string }>;
export async function removeFromWishlist(data: {
  productId: number;
  variantId?: number;
}): Promise<{ success: boolean; error?: string }>;
export async function removeFromWishlist(
  userIdOrData: string | { productId: number; variantId?: number },
  productId?: number
): Promise<{ success: boolean; error?: string }> {
  try {
    let user;
    let data: { productId: number; variantId?: number };

    // Handle overloaded function signatures
    if (typeof userIdOrData === 'string') {
      // API call format: removeFromWishlist(userId, productId)
      const { getAdminFirestore } = await import('@/lib/firebase/server');
      const adminDb = getAdminFirestore();
      const userDoc = await adminDb.doc(`users/${userIdOrData}`).get();

      if (!userDoc.exists) {
        return { success: false, error: 'User not found' };
      }

      const userData = userDoc.data();
      user = {
        uid: userIdOrData,
        customerId: userData?.customerId,
      };

      data = { productId: productId! };
    } else {
      // Original format: removeFromWishlist(data)
      user = await getCurrentUser();
      if (!user) {
        return { success: false, error: 'Authentication required' };
      }
      data = userIdOrData;
    }

    const { productId: finalProductId, variantId } = data;

    await prisma.wishlist_items.deleteMany({
      where: {
        customer_id: user.customerId,
        product_id: finalProductId,
        variant_id: variantId ? variantId : null,
      },
    });

    revalidatePath('/account/wishlist');
    return { success: true };
  } catch (error) {
    console.error('Error removing from wishlist:', error);
    return { success: false, error: 'Failed to remove item from wishlist' };
  }
}

/**
 * Check if item is in wishlist
 */
export async function isInWishlist(data: {
  productId: number;
  variantId?: number;
}): Promise<boolean> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return false;
    }

    const { productId, variantId } = data;

    const item = await prisma.wishlist_items.findFirst({
      where: {
        customer_id: user.customerId,
        product_id: productId,
        variant_id: variantId ? variantId : null,
      },
    });

    return !!item;
  } catch (error) {
    console.error('Error checking wishlist:', error);
    return false;
  }
}

/**
 * Check wishlist status for API endpoint (simplified version)
 */
export async function checkWishlistStatus(userId: string, productId: number): Promise<boolean> {
  try {
    // Get user's customer ID
    const { getAdminFirestore } = await import('@/lib/firebase/server');
    const adminDb = getAdminFirestore();
    const userDoc = await adminDb.doc(`users/${userId}`).get();

    if (!userDoc.exists) {
      return false;
    }

    const userData = userDoc.data();
    const customerId = userData?.customerId;

    if (!customerId) {
      return false;
    }

    const item = await prisma.wishlist_items.findFirst({
      where: {
        customer_id: customerId,
        product_id: productId,
      },
    });

    return !!item;
  } catch (error) {
    console.error('Error checking wishlist status:', error);
    return false;
  }
}

/**
 * Get user's wishlist items
 */
export async function getWishlistItems(userCurrency?: string): Promise<WishlistItem[] | null> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return null;
    }

    const currency = userCurrency || 'USD';

    const items = await prisma.wishlist_items.findMany({
      where: {
        customer_id: user.customerId,
      },
      orderBy: {
        created_at: 'desc',
      },
      include: {
        product: {
          include: {
            translations: {
              where: { language_code: 'en' },
              take: 1,
            },
            product_images: {
              where: { image_type: 'preview' },
              take: 1,
            },
            offers: {
              orderBy: { min_quantity: 'asc' },
              take: 1,
            },
          },
        },
        variant: true,
      },
    });

    // Calculate current prices for each item
    const itemsWithPrices = await Promise.all(
      items.map(async (item) => {
        // Get current cost price
        let costPriceCNY: number;
        if (item.variant && item.variant.price_low) {
          costPriceCNY = Number(item.variant.price_low);
        } else {
          costPriceCNY = Number(item.product.offers[0]?.price_low || 0);
        }

        // Calculate current price using pricing service
        const pricingResult = await pricingService.calculatePrice(costPriceCNY, {
          productId: item.product_id,
          marketplace: item.product.marketplace,
          userCurrency: currency,
        });

        return {
          id: item.id.toString(),
          productId: item.product_id,
          variantId: item.variant_id ? Number(item.variant_id) : undefined,
          priceWhenAdded: Number(item.price_when_added),
          currencyWhenAdded: item.currency_when_added,
          currentPrice: pricingResult.displayPrice,
          currentCurrency: pricingResult.currency,
          notes: item.notes || undefined,
          createdAt: item.created_at,
          product: {
            id: item.product.id,
            originalName: item.product.original_name || undefined,
            marketplace: item.product.marketplace,
            translations: item.product.translations.map(t => ({
              name: t.name,
              slug: t.slug,
            })),
            productImages: item.product.product_images.map(img => ({
              imageUrl: img.image_url,
              imageType: img.image_type,
            })),
            offers: item.product.offers.map(offer => ({
              priceHigh: offer.price_high ? Number(offer.price_high) : undefined,
              priceLow: Number(offer.price_low),
              currency: offer.currency,
              minQuantity: offer.min_quantity ? Number(offer.min_quantity) : undefined,
            })),
          },
          variant: item.variant ? {
            id: item.variant.id.toString(),
            originalVariantName: item.variant.original_variant_name,
            originalVariantType: item.variant.original_variant_type,
            priceLow: Number(item.variant.price_low),
            currency: item.variant.currency,
          } : undefined,
        };
      })
    );

    return itemsWithPrices;
  } catch (error) {
    console.error('Error getting wishlist items:', error);
    return null;
  }
}

/**
 * Get user's previously bought items (not in wishlist)
 */
export async function getPreviouslyBoughtItems(userCurrency?: string): Promise<WishlistItem[] | null> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return null;
    }

    const currency = userCurrency || 'USD';

    // Get unique products from order items
    const orderItems = await prisma.order_items.findMany({
      where: {
        order: {
          customer_id: user.customerId,
          status: {
            in: ['delivered', 'shipped'], // Only completed orders
          },
        },
      },
      orderBy: {
        order: {
          created: 'desc',
        },
      },
      include: {
        product: {
          include: {
            translations: {
              where: { language_code: 'en' },
              take: 1,
            },
            product_images: {
              where: { image_type: 'preview' },
              take: 1,
            },
            offers: {
              orderBy: { min_quantity: 'asc' },
              take: 1,
            },
          },
        },
        variant: true,
        order: {
          select: {
            created: true,
            currency: true,
          },
        },
      },
      take: 20, // Limit to recent items
    });

    // Remove duplicates and items already in wishlist
    const wishlistItems = await prisma.wishlist_items.findMany({
      where: {
        customer_id: user.customerId,
      },
      select: {
        product_id: true,
        variant_id: true,
      },
    });

    const wishlistKeys = new Set(
      wishlistItems.map(item => `${item.product_id}-${item.variant_id || 'null'}`)
    );

    const uniqueItems = new Map<string, typeof orderItems[0]>();

    for (const item of orderItems) {
      const key = `${item.product_id}-${item.variant_id || 'null'}`;

      // Skip if already in wishlist or already processed
      if (wishlistKeys.has(key) || uniqueItems.has(key)) {
        continue;
      }

      uniqueItems.set(key, item);
    }

    // Calculate current prices for each item
    const itemsWithPrices = await Promise.all(
      Array.from(uniqueItems.values()).map(async (item) => {
        // Get current cost price
        let costPriceCNY: number;
        if (item.variant && item.variant.price_low) {
          costPriceCNY = Number(item.variant.price_low);
        } else {
          costPriceCNY = Number(item.product.offers[0]?.price_low || 0);
        }

        // Calculate current price using pricing service
        const pricingResult = await pricingService.calculatePrice(costPriceCNY, {
          productId: item.product_id,
          marketplace: item.product.marketplace,
          userCurrency: currency,
        });

        return {
          id: `prev-${item.id}`,
          productId: item.product_id,
          variantId: item.variant_id ? Number(item.variant_id) : undefined,
          priceWhenAdded: Number(item.price_per_unit),
          currencyWhenAdded: item.order.currency,
          currentPrice: pricingResult.displayPrice,
          currentCurrency: pricingResult.currency,
          notes: `Last ordered: ${item.order.created.toLocaleDateString()}`,
          createdAt: item.order.created,
          product: {
            id: item.product.id,
            originalName: item.product.original_name || undefined,
            marketplace: item.product.marketplace,
            translations: item.product.translations.map(t => ({
              name: t.name,
              slug: t.slug,
            })),
            productImages: item.product.product_images.map(img => ({
              imageUrl: img.image_url,
              imageType: img.image_type,
            })),
            offers: item.product.offers.map(offer => ({
              priceHigh: offer.price_high ? Number(offer.price_high) : undefined,
              priceLow: Number(offer.price_low),
              currency: offer.currency,
              minQuantity: offer.min_quantity ? Number(offer.min_quantity) : undefined,
            })),
          },
          variant: item.variant ? {
            id: item.variant.id.toString(),
            originalVariantName: item.variant.original_variant_name,
            originalVariantType: item.variant.original_variant_type,
            priceLow: Number(item.variant.price_low),
            currency: item.variant.currency,
          } : undefined,
        };
      })
    );

    return itemsWithPrices;
  } catch (error) {
    console.error('Error getting previously bought items:', error);
    return null;
  }
}
