// lib/actions/admin/pricing.actions.ts
// Admin pricing management server actions

'use server';

import { revalidatePath } from 'next/cache';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/actions/auth.actions';
import { checkPermission } from '@/lib/auth/permissions';
import { PermissionAction } from '@/app/generated/prisma';
import {
  buildCursorWhereClause,
  buildCursorOrderBy,
  getNextCursor,
  getPrevCursor,
} from '@/lib/utils/cursor-pagination';
import type { pricing_rules } from '@/app/generated/prisma';
import type {
  PricingRule,
  ErrorResponse,
  SuccessResponseWithId,
  SuccessResponse,
} from '@/lib/types/admin';

/**
 * Get all pricing rules
 */
export async function getAllPricingRules(): Promise<PricingRule[] | ErrorResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { error: 'Not authenticated' };
    }

    // Check permission - requires READ permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PRICING_RULE_READ
    );

    if (!hasPermission) {
      return { error: 'Insufficient permissions' };
    }

    const rules = await prisma.pricing_rules.findMany({
      orderBy: { priority: 'desc' }, // Higher priority number = higher priority
    });

    return rules;
  } catch (error) {
    console.error('Error fetching pricing rules:', error);
    return { error: 'Failed to fetch pricing rules' };
  }
}

/**
 * Create pricing rule
 */
export async function createPricingRule(data: {
  rule_name: string;
  condition_type: string;
  condition_value?: string;
  markup_type: string;
  markup_value: number;
  priority: number;
  is_active: boolean;
}): Promise<SuccessResponseWithId> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check permission - requires CREATE permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PRICING_RULE_CREATE
    );

    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }

    const rule = await prisma.pricing_rules.create({
      data: {
        rule_name: data.rule_name,
        condition_type: data.condition_type,
        condition_value: data.condition_value,
        markup_type: data.markup_type,
        markup_value: data.markup_value,
        priority: data.priority,
        is_active: data.is_active,
      },
    });

    revalidatePath('/admin/pricing');

    return { success: true, ruleId: rule.id.toString() };
  } catch (error) {
    console.error('Error creating pricing rule:', error);
    return { success: false, error: 'Failed to create pricing rule' };
  }
}

/**
 * Update pricing rule
 */
export async function updatePricingRule(
  ruleId: string,
  data: {
    rule_name?: string;
    condition_type?: string;
    condition_value?: string;
    markup_type?: string;
    markup_value?: number;
    priority?: number;
    is_active?: boolean;
  }
): Promise<SuccessResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check permission - requires UPDATE permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PRICING_RULE_UPDATE
    );

    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }

    await prisma.pricing_rules.update({
      where: { id: parseInt(ruleId) },
      data,
    });

    revalidatePath('/admin/pricing');

    return { success: true };
  } catch (error) {
    console.error('Error updating pricing rule:', error);
    return { success: false, error: 'Failed to update pricing rule' };
  }
}

/**
 * Delete pricing rule
 */
export async function deletePricingRule(
  ruleId: string
): Promise<SuccessResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check permission - requires DELETE permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PRICING_RULE_DELETE
    );

    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }

    await prisma.pricing_rules.delete({
      where: { id: parseInt(ruleId) },
    });

    revalidatePath('/admin/pricing');

    return { success: true };
  } catch (error) {
    console.error('Error deleting pricing rule:', error);
    return { success: false, error: 'Failed to delete pricing rule' };
  }
}

/**
 * Get pricing rule preview with affected products and price comparisons
 */
export async function getPricingRulePreview(
  ruleId: string,
  cursor?: string,
  limit: number = 50
): Promise<{
  rule: PricingRule;
  pagination: {
    hasMore: boolean;
    nextCursor?: string;
    prevCursor?: string;
  };
  affectedProducts: Array<{
    id: number;
    product_url: string;
    marketplace: string;
    original_name: string | null;
    min_price_cny: number | null;
    categoryIds: number[];
    originalPrice: number;
    adjustedPrice: number;
    priceDifference: number;
    percentageChange: number;
    created: Date;
  }>;
} | ErrorResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { error: 'Not authenticated' };
    }

    // Check permission - requires READ permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PRICING_RULE_READ
    );

    if (!hasPermission) {
      return { error: 'Insufficient permissions' };
    }

    // Get the pricing rule
    const rule = await prisma.pricing_rules.findUnique({
      where: { id: parseInt(ruleId) },
    });

    if (!rule) {
      return { error: 'Pricing rule not found' };
    }

    // Build where clause for affected products based on rule conditions
    const where: Record<string, unknown> = {
      can_show: true, // Only show products that are visible
    };

    switch (rule.condition_type) {
      case 'GLOBAL':
        // All products
        break;
      case 'MARKETPLACE':
        where.marketplace = rule.condition_value;
        break;
      case 'CATEGORY':
        where.categories = {
          some: {
            category_id: parseInt(rule.condition_value!),
          },
        };
        break;
      case 'PRODUCT_ID':
        where.id = parseInt(rule.condition_value!);
        break;
      default:
        return { error: 'Invalid condition type' };
    }

    // Get affected products with their offers and categories (with pagination)
    const cursorWhereClause = buildCursorWhereClause(cursor, 'created', 'id');
    const combinedWhere = {
      ...where,
      ...cursorWhereClause,
    };

    const products = await prisma.products.findMany({
      where: combinedWhere,
      select: {
        id: true,
        product_url: true,
        marketplace: true,
        original_name: true,
        min_price_cny: true,
        created: true,
        offers: {
          select: {
            price_low: true,
          },
          orderBy: {
            price_low: 'asc',
          },
          take: 1,
        },
        categories: {
          select: {
            category_id: true,
          },
        },
      },
      orderBy: buildCursorOrderBy('created', 'id'),
      take: limit + 1, // Fetch one extra to check if there are more
    });

    // Check if there are more products
    const hasMore = products.length > limit;
    const paginatedProducts = hasMore ? products.slice(0, limit) : products;

    // Generate cursors
    const nextCursor = hasMore ? getNextCursor(paginatedProducts, 'created', 'id') : undefined;
    const prevCursor = cursor ? getPrevCursor(paginatedProducts, 'created', 'id') : undefined;

    // Calculate prices for each paginated product
    const affectedProducts = await Promise.all(
      paginatedProducts.map(async (product) => {
        // Get the base price (lowest offer price or min_price_cny)
        const basePriceNumber = Number(product.offers[0]?.price_low || product.min_price_cny || 0);
        const categoryIds = product.categories.map(c => c.category_id);

        // Calculate price without this rule (existing rules)
        const existingRules = await prisma.pricing_rules.findMany({
          where: {
            is_active: true,
            id: { not: rule.id }, // Exclude this rule
            OR: [
              { condition_type: 'GLOBAL' },
              {
                condition_type: 'MARKETPLACE',
                condition_value: product.marketplace,
              },
              {
                condition_type: 'CATEGORY',
                condition_value: { in: categoryIds.map(id => id.toString()) },
              },
              {
                condition_type: 'PRODUCT_ID',
                condition_value: product.id.toString(),
              },
            ],
          },
          orderBy: { priority: 'desc' },
        });

        // Apply existing rules
        let originalPrice = basePriceNumber;
        for (const existingRule of existingRules) {
          const markupAmount = calculateMarkup(originalPrice, existingRule);
          originalPrice += markupAmount;
        }

        // Apply this rule as well
        const ruleMarkup = calculateMarkup(originalPrice, rule);
        const adjustedPrice = originalPrice + ruleMarkup;

        const priceDifference = adjustedPrice - originalPrice;
        const percentageChange = originalPrice > 0 ? (priceDifference / originalPrice) * 100 : 0;

        return {
          id: product.id,
          product_url: product.product_url,
          marketplace: product.marketplace,
          original_name: product.original_name,
          min_price_cny: product.min_price_cny ? Number(product.min_price_cny) : null,
          categoryIds,
          originalPrice: Number(originalPrice.toFixed(2)),
          adjustedPrice: Number(adjustedPrice.toFixed(2)),
          priceDifference: Number(priceDifference.toFixed(2)),
          percentageChange: Number(percentageChange.toFixed(2)),
          created: product.created,
        };
      })
    );

    return {
      rule,
      pagination: {
        hasMore,
        nextCursor,
        prevCursor,
      },
      affectedProducts,
    };
  } catch (error) {
    console.error('Error getting pricing rule preview:', error);
    return { error: 'Failed to get pricing rule preview' };
  }
}

// Helper function to calculate markup (duplicate from pricing service)
function calculateMarkup(currentPrice: number, rule: pricing_rules): number {
  const { markup_type, markup_value } = rule;
  const value = Number(markup_value);

  switch (markup_type) {
    case 'PERCENTAGE':
      return currentPrice * (value / 100);
    case 'FIXED_AMOUNT_ADD':
      return value;
    case 'FIXED_AMOUNT_SET':
      return value - currentPrice;
    default:
      return 0;
  }
}