// lib/actions/admin/customer.actions.ts
// Admin customer management server actions

'use server';

import { revalidatePath } from 'next/cache';
import { Prisma } from '@/app/generated/prisma';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/actions/auth.actions';
import { checkPermission } from '@/lib/auth/permissions';
import { PermissionAction } from '@/app/generated/prisma';
import type {
  CustomerListItem,
  CustomerDetailTransformed,
  RoleTransformed,
  ErrorResponse,
  SuccessResponse,
  AdminPaginatedResponse,
} from '@/lib/types/admin';
import {
  buildCursorWhereClause,
  buildCursorOrderBy,
  applyCursorPagination
} from '@/lib/utils/cursor-pagination';

const CUSTOMERS_PER_PAGE = 20;

/**
 * Get all customers (admin)
 */
export async function getAllCustomers(filters: {
  search?: string;
  cursor?: string;
  limit?: number;
} = {}): Promise<AdminPaginatedResponse<CustomerListItem> | ErrorResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.CUSTOMER_READ
    );

    if (!hasPermission) {
      return { error: 'Insufficient permissions' };
    }

    const { search, cursor, limit = CUSTOMERS_PER_PAGE } = filters;

    // Build where clause
    const where: Prisma.customersWhereInput = {
      ...buildCursorWhereClause(cursor, 'created', 'id'),
    };

    if (search) {
      where.OR = [
        { email: { contains: search, mode: 'insensitive' } },
        { full_name: { contains: search, mode: 'insensitive' } },
      ];
    }

    // Fetch customers with cursor pagination
    // Using select for optimal performance - only fetch needed fields
    const customers = await prisma.customers.findMany({
        where,
        select: {
          id: true,
          email: true,
          full_name: true,
          created: true,
          firebase_uid: true,
          roles: {
            select: {
              role: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
          _count: {
            select: {
              orders: true,
            },
          },
        },
        orderBy: buildCursorOrderBy('created', 'id'),
        take: limit + 1, // Fetch one extra to determine hasMore
      });

    // Apply cursor pagination logic
    const paginationResult = applyCursorPagination(customers, {
      cursor,
      limit,
      timestampField: 'created',
      idField: 'id',
    });

    // Transform the customers data to match the expected format
    const transformedCustomers = paginationResult.data.map((customer) => ({
      ...customer,
      roles: customer.roles.map((cr) => cr.role),
    }));

    return {
      data: transformedCustomers,
      hasMore: paginationResult.hasMore,
      nextCursor: paginationResult.nextCursor,
      prevCursor: paginationResult.prevCursor,
    };
  } catch (error) {
    console.error('Error fetching customers:', error);
    return { error: 'Failed to fetch customers' };
  }
}

/**
 * Get customer by ID (admin)
 */
export async function getCustomerByIdAdmin(
  customerId: number
): Promise<CustomerDetailTransformed | ErrorResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.CUSTOMER_READ
    );

    if (!hasPermission) {
      return { error: 'Insufficient permissions' };
    }

    const customer = await prisma.customers.findUnique({
      where: { id: customerId },
      include: {
        roles: {
          include: {
            role: {
              include: {
                permissions: {
                  include: {
                    permission: true,
                  },
                },
              },
            },
          },
        },
        orders: {
          take: 10,
          orderBy: { created: 'desc' },
          select: {
            id: true,
            created: true,
            status: true,
            total_amount: true,
            currency: true,
          },
        },
      },
    });

    if (!customer) {
      return { error: 'Customer not found' };
    }

    // Transform the roles data to match the expected format
    const transformedCustomer = {
      ...customer,
      roles: customer.roles.map((cr) => ({
        id: cr.role.id,
        name: cr.role.name,
        description: cr.role.description,
        permissions: cr.role.permissions.map((rp) => rp.permission),
      })),
    };

    return transformedCustomer;
  } catch (error) {
    console.error('Error fetching customer:', error);
    return { error: 'Failed to fetch customer' };
  }
}

/**
 * Get all available roles
 */
export async function getAllRoles(): Promise<RoleTransformed[] | ErrorResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { error: 'Not authenticated' };
    }

    // Check permission - requires ROLE_READ permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.ROLE_READ
    );

    if (!hasPermission) {
      return { error: 'Insufficient permissions' };
    }

    const roles = await prisma.role.findMany({
      include: {
        permissions: {
          include: {
            permission: true,
          },
        },
      },
      orderBy: { name: 'asc' },
    });

    // Transform the roles data to match the expected format
    const transformedRoles = roles.map((role) => ({
      id: role.id,
      name: role.name,
      description: role.description,
      permissions: role.permissions.map((rp) => rp.permission),
    }));

    return transformedRoles;
  } catch (error) {
    console.error('Error fetching roles:', error);
    return { error: 'Failed to fetch roles' };
  }
}

/**
 * Assign role to customer
 */
export async function assignRoleToCustomer(
  customerId: number,
  roleId: number
): Promise<SuccessResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.CUSTOMER_ASSIGN_ROLE
    );

    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }

    // Check if role is already assigned
    const existing = await prisma.customer_roles.findUnique({
      where: {
        customer_id_role_id: {
          customer_id: customerId,
          role_id: roleId,
        },
      },
    });

    if (existing) {
      return { success: false, error: 'Role already assigned' };
    }

    // Assign role using explicit join table
    await prisma.customer_roles.create({
      data: {
        customer_id: customerId,
        role_id: roleId,
      },
    });

    revalidatePath(`/admin/customers/${customerId}`);
    revalidatePath('/admin/customers');

    return { success: true };
  } catch (error) {
    console.error('Error assigning role:', error);
    return { success: false, error: 'Failed to assign role' };
  }
}

/**
 * Remove role from customer
 */
export async function removeRoleFromCustomer(
  customerId: number,
  roleId: number
): Promise<SuccessResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check permission - requires CUSTOMER_REMOVE_ROLE permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.CUSTOMER_REMOVE_ROLE
    );

    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }

    // Remove role using explicit join table
    await prisma.customer_roles.delete({
      where: {
        customer_id_role_id: {
          customer_id: customerId,
          role_id: roleId,
        },
      },
    });

    revalidatePath(`/admin/customers/${customerId}`);
    revalidatePath('/admin/customers');

    return { success: true };
  } catch (error) {
    console.error('Error removing role:', error);
    return { success: false, error: 'Failed to remove role' };
  }
}