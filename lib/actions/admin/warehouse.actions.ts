// lib/actions/admin/warehouse.actions.ts
// Admin warehouse management server actions

'use server';

import { revalidatePath } from 'next/cache';
import { Prisma, WarehouseReceiptStatus } from '@/app/generated/prisma';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/actions/auth.actions';
import { checkPermission } from '@/lib/auth/permissions';
import { PermissionAction } from '@/app/generated/prisma';
import { orderStatusAutomationService } from '@/lib/services/order-status-automation';
import type {
  WarehouseReceiptListItem,
  WarehouseReceiptWithOrderItem,
  OrderConsolidationStatus,
  ErrorResponse,
  SuccessResponse,
  SuccessResponseWithId,
  AdminPaginatedResponse,
} from '@/lib/types/admin';
import {
  buildCursorWhereClause,
  buildCursorOrderBy,
  applyCursorPagination
} from '@/lib/utils/cursor-pagination';

const RECEIPTS_PER_PAGE = 20;

/**
 * Get all warehouse receipts with filters
 */
export async function getAllWarehouseReceipts(filters: {
  status?: WarehouseReceiptStatus;
  marketplaceOrderId?: string;
  cursor?: string;
  limit?: number;
} = {}): Promise<AdminPaginatedResponse<WarehouseReceiptListItem> | ErrorResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.WAREHOUSE_RECEIPT_READ
    );

    if (!hasPermission) {
      return { error: 'Insufficient permissions' };
    }

    const { status, marketplaceOrderId, cursor, limit = RECEIPTS_PER_PAGE } = filters;

    // Build where clause
    const where: Prisma.warehouse_receiptsWhereInput = {
      ...buildCursorWhereClause(cursor, 'received_at', 'id'),
    };

    if (status) {
      where.status = status;
    }

    if (marketplaceOrderId) {
      where.marketplace_order_id = {
        contains: marketplaceOrderId,
        mode: 'insensitive',
      };
    }

    // Fetch receipts with cursor pagination
    const receipts = await prisma.warehouse_receipts.findMany({
        where,
        select: {
          id: true,
          marketplace_order_id: true,
          package_weight: true,
          package_weight_unit: true,
          status: true,
          received_at: true,
          order_item: {
            select: {
              id: true,
              order: {
                select: {
                  id: true,
                  customer: {
                    select: {
                      full_name: true,
                      email: true,
                    },
                  },
                },
              },
              product: {
                select: {
                  original_name: true,
                  translations: {
                    take: 1,
                    select: {
                      name: true,
                    },
                  },
                },
              },
            },
          },
        },
        orderBy: buildCursorOrderBy('received_at', 'id'),
        take: limit + 1, // Fetch one extra to determine hasMore
      });

    // Apply cursor pagination logic
    const paginationResult = applyCursorPagination(receipts, {
      cursor,
      limit,
      timestampField: 'received_at',
      idField: 'id',
    });

    return {
      data: paginationResult.data,
      hasMore: paginationResult.hasMore,
      nextCursor: paginationResult.nextCursor,
      prevCursor: paginationResult.prevCursor,
    };
  } catch (error) {
    console.error('Error fetching warehouse receipts:', error);
    return { error: 'Failed to fetch warehouse receipts' };
  }
}

/**
 * Get warehouse receipt by ID
 */
export async function getWarehouseReceiptById(
  receiptId: string,
  locale: string = 'en'
): Promise<WarehouseReceiptWithOrderItem | ErrorResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.WAREHOUSE_RECEIPT_READ
    );

    if (!hasPermission) {
      return { error: 'Insufficient permissions' };
    }

    const receipt = await prisma.warehouse_receipts.findUnique({
      where: { id: receiptId },
      select: {
        id: true,
        marketplace_order_id: true,
        package_weight: true,
        package_weight_unit: true,
        status: true,
        received_at: true,
        tracking_number: true,
        carrier: true,
        shipping_label_url: true,
        order_item: {
          select: {
            id: true,
            quantity: true,
            price_per_unit: true,
            order: {
              select: {
                id: true,
                status: true,
                customer: {
                  select: {
                    id: true,
                    email: true,
                    full_name: true,
                  },
                },
              },
            },
            product: {
              select: {
                id: true,
                original_name: true,
                translations: {
                  where: {
                    language_code: locale,
                  },
                  take: 1,
                  select: {
                    name: true,
                    slug: true,
                  },
                },
              },
            },
            variant: {
              select: {
                id: true,
                original_variant_name: true,
                translations: {
                  where: {
                    language_code: locale,
                  },
                  take: 1,
                  select: {
                    variant_name: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!receipt) {
      return { error: 'Warehouse receipt not found' };
    }

    return receipt;
  } catch (error) {
    console.error('Error fetching warehouse receipt:', error);
    return { error: 'Failed to fetch warehouse receipt' };
  }
}

/**
 * Create warehouse receipt when package arrives
 */
export async function createWarehouseReceipt(data: {
  marketplace_order_id: string;
  package_weight: number;
  package_weight_unit: string;
  order_item_id?: number;
}): Promise<SuccessResponseWithId> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.WAREHOUSE_RECEIPT_CREATE
    );

    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }

    // If order_item_id not provided, try to find matching order item
    let orderItemId = data.order_item_id;

    if (!orderItemId) {
      const orderItem = await prisma.order_items.findFirst({
        where: {
          marketplace_order_id: data.marketplace_order_id,
        },
      });

      if (orderItem) {
        orderItemId = Number(orderItem.id);
      } else {
        return {
          success: false,
          error: 'No matching order item found for this marketplace order ID',
        };
      }
    }

    // Create warehouse receipt
    const receipt = await prisma.warehouse_receipts.create({
      data: {
        marketplace_order_id: data.marketplace_order_id,
        package_weight: data.package_weight,
        package_weight_unit: data.package_weight_unit,
        order_item_id: orderItemId,
        status: 'matched', // Automatically match if we found the order item
      },
    });

    // Trigger order status automation
    const orderItem = await prisma.order_items.findUnique({
      where: { id: orderItemId },
      select: { order_id: true },
    });

    if (orderItem) {
      await orderStatusAutomationService.updateOrderStatusFromReceipts(orderItem.order_id);
    }

    revalidatePath('/admin/warehouse');

    return {
      success: true,
      receiptId: receipt.id,
      message: 'Warehouse receipt created successfully',
    };
  } catch (error) {
    console.error('Error creating warehouse receipt:', error);
    return { success: false, error: 'Failed to create warehouse receipt' };
  }
}

/**
 * Update tracking info and mark as shipped
 */
export async function updateWarehouseTracking(
  receiptId: string,
  data: {
    tracking_number: string;
    carrier: string;
    shipping_label_url?: string;
  }
): Promise<SuccessResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.WAREHOUSE_UPDATE_TRACKING
    );

    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }

    // Update receipt with tracking info and mark as shipped
    const updatedReceipt = await prisma.warehouse_receipts.update({
      where: { id: receiptId },
      data: {
        tracking_number: data.tracking_number,
        carrier: data.carrier,
        shipping_label_url: data.shipping_label_url,
        status: 'shipped',
      },
      select: {
        order_item: {
          select: {
            order_id: true,
          },
        },
      },
    });

    // Trigger order status automation for delivery
    if (updatedReceipt.order_item) {
      await orderStatusAutomationService.updateOrderStatusOnTrackingAdded(
        updatedReceipt.order_item.order_id
      );
    }

    revalidatePath('/admin/warehouse');

    return {
      success: true,
      message: 'Tracking info updated successfully',
    };
  } catch (error) {
    console.error('Error updating tracking info:', error);
    return { success: false, error: 'Failed to update tracking info' };
  }
}

/**
 * Get consolidation status for an order
 */
export async function getOrderConsolidationStatus(
  orderId: string,
  locale: string = 'en'
): Promise<OrderConsolidationStatus | ErrorResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.WAREHOUSE_RECEIPT_READ
    );

    if (!hasPermission) {
      return { error: 'Insufficient permissions' };
    }

    // Get all order items with their warehouse receipts
    const orderItems = await prisma.order_items.findMany({
      where: { order_id: orderId },
      select: {
        id: true,
        quantity: true,
        product: {
          select: {
            translations: {
              where: { language_code: locale },
              take: 1,
              select: { name: true },
            },
            original_name: true,
          },
        },
        variant: {
          select: {
            translations: {
              where: { language_code: locale },
              take: 1,
              select: { variant_name: true },
            },
            original_variant_name: true,
          },
        },
        warehouse_receipts: {
          select: {
            id: true,
            status: true,
            tracking_number: true,
          },
        },
      },
    });

    const totalItems = orderItems.length;
    let pendingItems = 0;
    let matchedItems = 0;
    let shippedItems = 0;

    const items = orderItems.map((item) => {
      const receipt = item.warehouse_receipts[0];
      const receiptStatus = receipt
        ? receipt.status
        : 'none';

      if (!receipt) pendingItems++;
      else if (receipt.status === 'pending') pendingItems++;
      else if (receipt.status === 'matched') matchedItems++;
      else if (receipt.status === 'shipped') shippedItems++;

      const productName =
        item.product.translations[0]?.name || item.product.original_name || 'Unknown Product';
      const variantName =
        item.variant?.translations[0]?.variant_name ||
        item.variant?.original_variant_name;

      return {
        orderItemId: Number(item.id),
        productName,
        variantName,
        quantity: Number(item.quantity),
        receiptStatus: receiptStatus as 'none' | 'pending' | 'matched' | 'shipped',
        receiptId: receipt?.id,
        trackingNumber: receipt?.tracking_number || undefined,
      };
    });

    const canConsolidate = matchedItems === totalItems && totalItems > 0;

    return {
      orderId,
      totalItems,
      pendingItems,
      matchedItems,
      shippedItems,
      canConsolidate,
      items,
    };
  } catch (error) {
    console.error('Error fetching consolidation status:', error);
    return { error: 'Failed to fetch consolidation status' };
  }
}

