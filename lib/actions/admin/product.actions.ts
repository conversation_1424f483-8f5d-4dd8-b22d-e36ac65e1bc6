// lib/actions/admin/product.actions.ts
// Admin product management server actions

'use server';

import { revalidatePath } from 'next/cache';
import { Prisma } from '@/app/generated/prisma';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/actions/auth.actions';
import { checkPermission } from '@/lib/auth/permissions';
import { PermissionAction } from '@/app/generated/prisma';
import { PRODUCTS_PER_PAGE } from '@/lib/constants';
import type {
  ProductAdminListItem,
  ProductAdminDetail,
  ErrorResponse,
  SuccessResponse,
  AdminPaginatedResponse,
} from '@/lib/types/admin';
import {
  buildCursorWhereClause,
  buildCursorOrderBy,
  applyCursorPagination
} from '@/lib/utils/cursor-pagination';

/**
 * Get all products for admin (with filters)
 */
export async function getAllProductsAdmin(filters: {
  search?: string;
  category?: string;
  cursor?: string;
  limit?: number;
} = {}): Promise<AdminPaginatedResponse<ProductAdminListItem> | ErrorResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PRODUCT_READ
    );

    if (!hasPermission) {
      return { error: 'Insufficient permissions' };
    }

    const { search, category, cursor, limit = PRODUCTS_PER_PAGE } = filters;

    // Build where clause
    const where: Prisma.productsWhereInput = {
      ...buildCursorWhereClause(cursor, 'created', 'id'),
    };

    if (search) {
      where.OR = [
        {
          translations: {
            some: {
              name: { contains: search, mode: 'insensitive' },
            },
          },
        },
      ];
    }

    if (category) {
      where.categories = {
        some: {
          category_id: parseInt(category),
        },
      };
    }

    // Fetch products with cursor pagination
    // Using select for optimal performance - only fetch needed fields
    const products = await prisma.products.findMany({
        where,
        select: {
          id: true,
          original_name: true,
          marketplace: true,
          can_show: true,
          featured: true,
          created: true,
          translations: {
            where: { language_code: 'en' },
            take: 1,
            select: {
              name: true,
              slug: true,
              language_code: true,
            },
          },
          product_images: {
            take: 1,
            orderBy: { id: 'asc' },
            select: {
              image_url: true,
              image_type: true,
            },
          },
          categories: {
            take: 3,
            select: {
              category: {
                select: {
                  id: true,
                  translations: {
                    where: { language_code: 'en' },
                    take: 1,
                    select: {
                      name: true,
                    },
                  },
                },
              },
            },
          },
        },
        orderBy: buildCursorOrderBy('created', 'id'),
        take: limit + 1, // Fetch one extra to determine hasMore
      });

    // Apply cursor pagination logic
    const paginationResult = applyCursorPagination(products, {
      cursor,
      limit,
      timestampField: 'created',
      idField: 'id',
    });

    return {
      data: paginationResult.data,
      hasMore: paginationResult.hasMore,
      nextCursor: paginationResult.nextCursor,
      prevCursor: paginationResult.prevCursor,
    };
  } catch (error) {
    console.error('Error fetching products:', error);
    return { error: 'Failed to fetch products' };
  }
}

/**
 * Get product by ID for admin (full details)
 */
export async function getProductByIdAdmin(
  productId: number
): Promise<ProductAdminDetail | ErrorResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PRODUCT_READ
    );

    if (!hasPermission) {
      return { error: 'Insufficient permissions' };
    }

    const product = await prisma.products.findUnique({
      where: { id: productId },
      include: {
        translations: true,
        product_images: {
          orderBy: { id: 'asc' },
        },
        variants: {
          include: {
            translations: true,
          },
        },
        categories: {
          include: {
            category: {
              include: {
                translations: true,
              },
            },
          },
        },
        offers: true,
        product_attributes: {
          include: {
            translations: true,
          },
        },
      },
    });

    if (!product) {
      return { error: 'Product not found' };
    }

    return product;
  } catch (error) {
    console.error('Error fetching product:', error);
    return { error: 'Failed to fetch product' };
  }
}

/**
 * Delete product (admin)
 */
export async function deleteProduct(
  productId: number
): Promise<SuccessResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PRODUCT_DELETE
    );

    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }

    // Delete product (cascade will handle related records)
    await prisma.products.delete({
      where: { id: productId },
    });

    revalidatePath('/admin/products');

    return { success: true };
  } catch (error) {
    console.error('Error deleting product:', error);
    return { success: false, error: 'Failed to delete product' };
  }
}

/**
 * Update product visibility (admin)
 */
export async function updateProductVisibility(
  productId: number,
  canShow: boolean
): Promise<SuccessResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PRODUCT_UPDATE
    );

    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }

    await prisma.products.update({
      where: { id: productId },
      data: { can_show: canShow },
    });

    revalidatePath('/admin/products');
    revalidatePath(`/admin/products/${productId}`);

    // Revalidate product detail pages for all locales
    const product = await prisma.products.findUnique({
      where: { id: productId },
      include: {
        translations: true,
      },
    });

    if (product) {
      const { LOCALES } = await import('@/lib/constants');
      for (const locale of LOCALES) {
        const translation = product.translations.find(t => t.language_code === locale);
        if (translation?.slug) {
          revalidatePath(`/${locale}/products/${translation.slug}`);
        }
      }
    }

    return { success: true };
  } catch (error) {
    console.error('Error updating product visibility:', error);
    return { success: false, error: 'Failed to update product visibility' };
  }
}

/**
 * Update product featured status (admin)
 */
export async function updateProductFeatured(
  productId: number,
  featured: boolean
): Promise<SuccessResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PRODUCT_UPDATE
    );

    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }

    await prisma.products.update({
      where: { id: productId },
      data: { featured },
    });

    revalidatePath('/admin/products');
    revalidatePath(`/admin/products/${productId}`);

    // Trigger ISR revalidation via webhook
    try {
      const revalidationSecret = process.env.REVALIDATION_SECRET;
      if (revalidationSecret) {
        await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/revalidate/products`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${revalidationSecret}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            type: 'product-update',
            productIds: [productId],
          }),
        });
      }
    } catch (error) {
      console.error('Error triggering ISR revalidation:', error);
      // Don't fail the main operation if revalidation fails
    }

    return { success: true };
  } catch (error) {
    console.error('Error updating product featured status:', error);
    return { success: false, error: 'Failed to update product featured status' };
  }
}

/**
 * Delete product image (admin)
 */
export async function deleteProductImage(
  imageId: number
): Promise<SuccessResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PRODUCT_MANAGE_IMAGES
    );

    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }

    await prisma.product_images.delete({
      where: { id: imageId },
    });

    revalidatePath('/admin/products');

    return { success: true };
  } catch (error) {
    console.error('Error deleting product image:', error);
    return { success: false, error: 'Failed to delete product image' };
  }
}

/**
 * Update product image type (admin)
 */
export async function updateProductImageType(
  imageId: number,
  imageType: 'preview' | 'description' | 'video'
): Promise<SuccessResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PRODUCT_MANAGE_IMAGES
    );

    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }

    await prisma.product_images.update({
      where: { id: imageId },
      data: { image_type: imageType },
    });

    revalidatePath('/admin/products');

    // Revalidate product detail pages for all locales
    const image = await prisma.product_images.findUnique({
      where: { id: imageId },
      include: {
        product: {
          include: {
            translations: true,
          },
        },
      },
    });

    if (image?.product) {
      const { LOCALES } = await import('@/lib/constants');
      for (const locale of LOCALES) {
        const translation = image.product.translations.find(t => t.language_code === locale);
        if (translation?.slug) {
          revalidatePath(`/${locale}/products/${translation.slug}`);
        }
      }
    }

    return { success: true };
  } catch (error) {
    console.error('Error updating product image type:', error);
    return { success: false, error: 'Failed to update product image type' };
  }
}

/**
 * Delete product variant (admin)
 */
export async function deleteProductVariant(
  variantId: number
): Promise<SuccessResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PRODUCT_MANAGE_VARIANTS
    );

    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }

    await prisma.variants.delete({
      where: { id: variantId },
    });

    revalidatePath('/admin/products');

    return { success: true };
  } catch (error) {
    console.error('Error deleting product variant:', error);
    return { success: false, error: 'Failed to delete product variant' };
  }
}

/**
 * Update product variant (admin)
 */
export async function updateProductVariant(
  variantId: number,
  data: {
    original_variant_name: string;
    original_variant_type: string;
    available_quantity?: number | null;
    min_quantity?: number | null;
    price_low: number;
    price_high?: number | null;
    currency: string;
  }
): Promise<SuccessResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PRODUCT_MANAGE_VARIANTS
    );

    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }

    await prisma.variants.update({
      where: { id: variantId },
      data: {
        original_variant_name: data.original_variant_name,
        original_variant_type: data.original_variant_type,
        available_quantity: data.available_quantity,
        min_quantity: data.min_quantity,
        price_low: data.price_low,
        price_high: data.price_high,
        currency: data.currency,
      },
    });

    revalidatePath('/admin/products');

    // Revalidate product detail pages for all locales
    const variant = await prisma.variants.findUnique({
      where: { id: variantId },
      include: {
        product: {
          include: {
            translations: true,
          },
        },
      },
    });

    if (variant?.product) {
      const { LOCALES } = await import('@/lib/constants');
      for (const locale of LOCALES) {
        const translation = variant.product.translations.find(t => t.language_code === locale);
        if (translation?.slug) {
          revalidatePath(`/${locale}/products/${translation.slug}`);
        }
      }
    }

    return { success: true };
  } catch (error) {
    console.error('Error updating product variant:', error);
    return { success: false, error: 'Failed to update product variant' };
  }
}

/**
 * Delete product offer (admin)
 */
export async function deleteProductOffer(
  offerId: number
): Promise<SuccessResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PRODUCT_UPDATE
    );

    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }

    await prisma.offers.delete({
      where: { id: offerId },
    });

    revalidatePath('/admin/products');

    return { success: true };
  } catch (error) {
    console.error('Error deleting product offer:', error);
    return { success: false, error: 'Failed to delete product offer' };
  }
}

/**
 * Update product offer (admin)
 */
export async function updateProductOffer(
  offerId: number,
  data: {
    min_quantity: number;
    price_low: number;
    price_high?: number | null;
    currency: string;
    quantity_info?: string | null;
  }
): Promise<SuccessResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PRODUCT_UPDATE
    );

    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }

    await prisma.offers.update({
      where: { id: offerId },
      data: {
        min_quantity: data.min_quantity,
        price_low: data.price_low,
        price_high: data.price_high,
        currency: data.currency,
        quantity_info: data.quantity_info,
      },
    });

    revalidatePath('/admin/products');

    // Revalidate product detail pages for all locales
    const offer = await prisma.offers.findUnique({
      where: { id: offerId },
      include: {
        product: {
          include: {
            translations: true,
          },
        },
      },
    });

    if (offer?.product) {
      const { LOCALES } = await import('@/lib/constants');
      for (const locale of LOCALES) {
        const translation = offer.product.translations.find(t => t.language_code === locale);
        if (translation?.slug) {
          revalidatePath(`/${locale}/products/${translation.slug}`);
        }
      }
    }

    return { success: true };
  } catch (error) {
    console.error('Error updating product offer:', error);
    return { success: false, error: 'Failed to update product offer' };
  }
}

/**
 * Create product (admin)
 */
export async function createProduct(data: {
  original_name: string;
  product_url: string;
  marketplace: 'taobao' | 'pinduoduo' | 'alibaba';
  can_show: boolean;
  weight?: number;
  weight_unit: string;
  translations: Array<{
    language_code: string;
    name: string;
    slug: string;
  }>;
  images: Array<{
    image_url: string;
    image_type: 'preview' | 'description' | 'video';
  }>;
  variants: Array<{
    original_variant_name: string;
    original_variant_type: string;
    available_quantity?: number;
    min_quantity?: number;
    price_low: number;
    price_high?: number;
    currency: string;
  }>;
  offers: Array<{
    min_quantity: number;
    price_low: number;
    price_high?: number;
    currency: string;
    quantity_info?: string;
  }>;
  attributes: Array<{
    original_attr_key: string;
    original_attr_value: string;
  }>;
}): Promise<SuccessResponse & { productId?: number }> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PRODUCT_CREATE
    );

    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }

    // Calculate min_price_cny from offers
    const minPriceCny = data.offers.length > 0
      ? Math.min(...data.offers.map(o => o.price_low))
      : null;

    const result = await prisma.$transaction(async (tx) => {
      // Create product
      const product = await tx.products.create({
        data: {
          original_name: data.original_name,
          product_url: data.product_url,
          marketplace: data.marketplace,
          can_show: data.can_show,
          weight: data.weight,
          weight_unit: data.weight_unit,
          min_price_cny: minPriceCny,
        },
      });

      // Create translations
      if (data.translations.length > 0) {
        await tx.product_translations.createMany({
          data: data.translations.map(t => ({
            product_id: product.id,
            language_code: t.language_code,
            name: t.name,
            slug: t.slug,
          })),
        });
      }

      // Create images
      if (data.images.length > 0) {
        await tx.product_images.createMany({
          data: data.images.map(img => ({
            product_id: product.id,
            image_url: img.image_url,
            image_type: img.image_type,
          })),
        });
      }

      // Create variants
      if (data.variants.length > 0) {
        await tx.variants.createMany({
          data: data.variants.map(v => ({
            product_id: product.id,
            original_variant_name: v.original_variant_name,
            original_variant_type: v.original_variant_type,
            available_quantity: v.available_quantity,
            min_quantity: v.min_quantity,
            price_low: v.price_low,
            price_high: v.price_high,
            currency: v.currency,
          })),
        });
      }

      // Create offers
      if (data.offers.length > 0) {
        await tx.offers.createMany({
          data: data.offers.map(o => ({
            product_id: product.id,
            min_quantity: o.min_quantity,
            price_low: o.price_low,
            price_high: o.price_high,
            currency: o.currency,
            quantity_info: o.quantity_info,
          })),
        });
      }

      // Create attributes
      if (data.attributes.length > 0) {
        await tx.product_attributes.createMany({
          data: data.attributes.map(attr => ({
            product_id: product.id,
            original_attr_key: attr.original_attr_key,
            original_attr_value: attr.original_attr_value,
          })),
        });
      }

      return product;
    });

    revalidatePath('/admin/products');

    // Revalidate product detail pages for all locales
    const product = await prisma.products.findUnique({
      where: { id: result.id },
      include: {
        translations: true,
      },
    });

    if (product) {
      const { LOCALES } = await import('@/lib/constants');
      for (const locale of LOCALES) {
        const translation = product.translations.find(t => t.language_code === locale);
        if (translation?.slug) {
          revalidatePath(`/${locale}/products/${translation.slug}`);
        }
      }
    }

    return { success: true, productId: result.id };
  } catch (error) {
    console.error('Error creating product:', error);
    return { success: false, error: 'Failed to create product' };
  }
}

/**
 * Update product (admin)
 */
export async function updateProduct(
  productId: number,
  data: {
    original_name: string;
    product_url: string;
    marketplace: 'taobao' | 'pinduoduo' | 'alibaba';
    can_show: boolean;
    weight?: number;
    weight_unit: string;
    translations: Array<{
      language_code: string;
      name: string;
      slug: string;
    }>;
    images: Array<{
      image_url: string;
      image_type: 'preview' | 'description' | 'video';
    }>;
    variants: Array<{
      original_variant_name: string;
      original_variant_type: string;
      available_quantity?: number;
      min_quantity?: number;
      price_low: number;
      price_high?: number;
      currency: string;
    }>;
    offers: Array<{
      min_quantity: number;
      price_low: number;
      price_high?: number;
      currency: string;
      quantity_info?: string;
    }>;
    attributes: Array<{
      original_attr_key: string;
      original_attr_value: string;
    }>;
  }
): Promise<SuccessResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PRODUCT_UPDATE
    );

    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }

    // Calculate min_price_cny from offers
    const minPriceCny = data.offers.length > 0
      ? Math.min(...data.offers.map(o => o.price_low))
      : null;

    await prisma.$transaction(async (tx) => {
      // Update product
      await tx.products.update({
        where: { id: productId },
        data: {
          original_name: data.original_name,
          product_url: data.product_url,
          marketplace: data.marketplace,
          can_show: data.can_show,
          weight: data.weight,
          weight_unit: data.weight_unit,
          min_price_cny: minPriceCny,
        },
      });

      // Update translations - delete existing and recreate
      await tx.product_translations.deleteMany({
        where: { product_id: productId },
      });

      if (data.translations.length > 0) {
        await tx.product_translations.createMany({
          data: data.translations.map(t => ({
            product_id: productId,
            language_code: t.language_code,
            name: t.name,
            slug: t.slug,
          })),
        });
      }

      // Update images - delete existing and recreate
      await tx.product_images.deleteMany({
        where: { product_id: productId },
      });

      if (data.images.length > 0) {
        await tx.product_images.createMany({
          data: data.images.map(img => ({
            product_id: productId,
            image_url: img.image_url,
            image_type: img.image_type,
          })),
        });
      }

      // Update variants - delete existing and recreate
      await tx.variants.deleteMany({
        where: { product_id: productId },
      });

      if (data.variants.length > 0) {
        await tx.variants.createMany({
          data: data.variants.map(v => ({
            product_id: productId,
            original_variant_name: v.original_variant_name,
            original_variant_type: v.original_variant_type,
            available_quantity: v.available_quantity,
            min_quantity: v.min_quantity,
            price_low: v.price_low,
            price_high: v.price_high,
            currency: v.currency,
          })),
        });
      }

      // Update offers - delete existing and recreate
      await tx.offers.deleteMany({
        where: { product_id: productId },
      });

      if (data.offers.length > 0) {
        await tx.offers.createMany({
          data: data.offers.map(o => ({
            product_id: productId,
            min_quantity: o.min_quantity,
            price_low: o.price_low,
            price_high: o.price_high,
            currency: o.currency,
            quantity_info: o.quantity_info,
          })),
        });
      }

      // Update attributes - delete existing and recreate
      await tx.product_attributes.deleteMany({
        where: { product_id: productId },
      });

      if (data.attributes.length > 0) {
        await tx.product_attributes.createMany({
          data: data.attributes.map(attr => ({
            product_id: productId,
            original_attr_key: attr.original_attr_key,
            original_attr_value: attr.original_attr_value,
          })),
        });
      }
    });

    revalidatePath('/admin/products');
    revalidatePath(`/admin/products/${productId}`);

    // Revalidate product detail pages for all locales
    const product = await prisma.products.findUnique({
      where: { id: productId },
      include: {
        translations: true,
      },
    });

    if (product) {
      const { LOCALES } = await import('@/lib/constants');
      for (const locale of LOCALES) {
        const translation = product.translations.find(t => t.language_code === locale);
        if (translation?.slug) {
          revalidatePath(`/${locale}/products/${translation.slug}`);
        }
      }
    }

    return { success: true };
  } catch (error) {
    console.error('Error updating product:', error);
    return { success: false, error: 'Failed to update product' };
  }
}