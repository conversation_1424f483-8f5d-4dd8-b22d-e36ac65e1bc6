// lib/actions/admin/payment.actions.ts
// Admin payment management server actions

'use server';

import { revalidatePath } from 'next/cache';
import { Prisma, PaymentStatus } from '@/app/generated/prisma';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/actions/auth.actions';
import { checkPermission } from '@/lib/auth/permissions';
import { PermissionAction } from '@/app/generated/prisma';
import type {
  PaymentListItem,
  PaymentWithOrder,
  ErrorResponse,
  SuccessResponse,
  SuccessResponseWithId,
  AdminPaginatedResponse,
} from '@/lib/types/admin';
import {
  buildCursorWhereClause,
  buildCursorOrderBy,
  applyCursorPagination
} from '@/lib/utils/cursor-pagination';

const PAYMENTS_PER_PAGE = 20;

/**
 * Get all payments with filters
 */
export async function getAllPayments(filters: {
  status?: PaymentStatus;
  orderId?: string;
  cursor?: string;
  limit?: number;
} = {}): Promise<AdminPaginatedResponse<PaymentListItem> | ErrorResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PAYMENT_READ
    );

    if (!hasPermission) {
      return { error: 'Insufficient permissions' };
    }

    const { status, orderId, cursor, limit = PAYMENTS_PER_PAGE } = filters;

    // Build where clause
    const where: Prisma.paymentsWhereInput = {
      ...buildCursorWhereClause(cursor, 'created', 'id'),
    };

    if (status) {
      where.status = status;
    }

    if (orderId) {
      where.order_id = {
        contains: orderId,
        mode: 'insensitive',
      };
    }

    // Fetch payments with cursor pagination
    const payments = await prisma.payments.findMany({
        where,
        select: {
          id: true,
          amount: true,
          currency: true,
          payment_method: true,
          transaction_id: true,
          status: true,
          created: true,
          order: {
            select: {
              id: true,
              customer: {
                select: {
                  full_name: true,
                  email: true,
                },
              },
            },
          },
        },
        orderBy: buildCursorOrderBy('created', 'id'),
        take: limit + 1, // Fetch one extra to determine hasMore
      });

    // Apply cursor pagination logic
    const paginationResult = applyCursorPagination(payments, {
      cursor,
      limit,
      timestampField: 'created',
      idField: 'id',
    });

    return {
      data: paginationResult.data,
      hasMore: paginationResult.hasMore,
      nextCursor: paginationResult.nextCursor,
      prevCursor: paginationResult.prevCursor,
    };
  } catch (error) {
    console.error('Error fetching payments:', error);
    return { error: 'Failed to fetch payments' };
  }
}

/**
 * Get payment by ID
 */
export async function getPaymentById(
  paymentId: string
): Promise<PaymentWithOrder | ErrorResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PAYMENT_READ
    );

    if (!hasPermission) {
      return { error: 'Insufficient permissions' };
    }

    const payment = await prisma.payments.findUnique({
      where: { id: paymentId },
      select: {
        id: true,
        amount: true,
        currency: true,
        payment_method: true,
        transaction_id: true,
        status: true,
        created: true,
        order: {
          select: {
            id: true,
            status: true,
            total_amount: true,
            currency: true,
            customer: {
              select: {
                id: true,
                email: true,
                full_name: true,
              },
            },
          },
        },
      },
    });

    if (!payment) {
      return { error: 'Payment not found' };
    }

    return payment;
  } catch (error) {
    console.error('Error fetching payment:', error);
    return { error: 'Failed to fetch payment' };
  }
}

/**
 * Get payments for an order
 */
export async function getPaymentsByOrderId(
  orderId: string
): Promise<PaymentListItem[] | ErrorResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PAYMENT_READ
    );

    if (!hasPermission) {
      return { error: 'Insufficient permissions' };
    }

    const payments = await prisma.payments.findMany({
      where: { order_id: orderId },
      select: {
        id: true,
        amount: true,
        currency: true,
        payment_method: true,
        transaction_id: true,
        status: true,
        created: true,
        order: {
          select: {
            id: true,
            customer: {
              select: {
                full_name: true,
                email: true,
              },
            },
          },
        },
      },
      orderBy: { created: 'desc' },
    });

    return payments;
  } catch (error) {
    console.error('Error fetching payments for order:', error);
    return { error: 'Failed to fetch payments' };
  }
}

/**
 * Record payment receipt
 */
export async function recordPayment(data: {
  order_id: string;
  amount: number;
  currency: string;
  payment_method: string;
  transaction_id: string;
}): Promise<SuccessResponseWithId> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PAYMENT_CREATE
    );

    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }

    // Check if order exists
    const order = await prisma.orders.findUnique({
      where: { id: data.order_id },
    });

    if (!order) {
      return { success: false, error: 'Order not found' };
    }

    // Check if payment with this transaction ID already exists
    const existingPayment = await prisma.payments.findUnique({
      where: { transaction_id: data.transaction_id },
    });

    if (existingPayment) {
      return {
        success: false,
        error: 'Payment with this transaction ID already exists',
      };
    }

    // Create payment record
    const payment = await prisma.payments.create({
      data: {
        order_id: data.order_id,
        amount: data.amount,
        currency: data.currency,
        payment_method: data.payment_method,
        transaction_id: data.transaction_id,
        status: 'pending',
      },
    });

    revalidatePath('/admin/payments');
    revalidatePath(`/admin/orders/${data.order_id}`);

    return {
      success: true,
      paymentId: payment.id,
      message: 'Payment recorded successfully',
    };
  } catch (error) {
    console.error('Error recording payment:', error);
    return { success: false, error: 'Failed to record payment' };
  }
}

/**
 * Update payment status
 */
export async function updatePaymentStatus(
  paymentId: string,
  status: PaymentStatus
): Promise<SuccessResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PAYMENT_UPDATE_STATUS
    );

    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }

    // Get payment to find order
    const payment = await prisma.payments.findUnique({
      where: { id: paymentId },
      select: { order_id: true },
    });

    if (!payment) {
      return { success: false, error: 'Payment not found' };
    }

    // Update payment status in a transaction
    await prisma.$transaction(async (tx) => {
      // Update payment status
      await tx.payments.update({
        where: { id: paymentId },
        data: { status },
      });

      // If payment succeeded, update order status to processing
      if (status === 'succeeded') {
        await tx.orders.update({
          where: { id: payment.order_id },
          data: { status: 'processing' },
        });
      }

      // If payment failed, keep order as pending
      // If payment refunded, update order status to refunded
      if (status === 'refunded') {
        await tx.orders.update({
          where: { id: payment.order_id },
          data: { status: 'refunded' },
        });
      }
    });

    revalidatePath('/admin/payments');
    revalidatePath(`/admin/orders/${payment.order_id}`);

    return {
      success: true,
      message: 'Payment status updated successfully',
    };
  } catch (error) {
    console.error('Error updating payment status:', error);
    return { success: false, error: 'Failed to update payment status' };
  }
}

/**
 * Process refund
 */
export async function processRefund(
  paymentId: string,
  refundAmount?: number
): Promise<SuccessResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PAYMENT_REFUND
    );

    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }

    // Get payment
    const payment = await prisma.payments.findUnique({
      where: { id: paymentId },
    });

    if (!payment) {
      return { success: false, error: 'Payment not found' };
    }

    if (payment.status !== 'succeeded') {
      return {
        success: false,
        error: 'Can only refund succeeded payments',
      };
    }

    // Update payment status to refunded
    await updatePaymentStatus(paymentId, 'refunded');

    return {
      success: true,
      message: 'Refund processed successfully',
    };
  } catch (error) {
    console.error('Error processing refund:', error);
    return { success: false, error: 'Failed to process refund' };
  }
}

