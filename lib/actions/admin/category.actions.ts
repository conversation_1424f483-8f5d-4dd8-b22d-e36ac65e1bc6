// lib/actions/admin/category.actions.ts
// Admin category management server actions

'use server';

import { revalidatePath } from 'next/cache';
import { Prisma } from '@/app/generated/prisma';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/actions/auth.actions';
import { checkPermission } from '@/lib/auth/permissions';
import { PermissionAction } from '@/app/generated/prisma';
import type {
  ErrorResponse,
  SuccessResponse,
  SuccessResponseWithId,
} from '@/lib/types/admin';

/**
 * Category with translations and children for admin
 */
export type CategoryWithTranslations = Prisma.categoriesGetPayload<{
  include: {
    translations: true;
    children: {
      include: {
        translations: true;
      };
    };
    parent: {
      include: {
        translations: true;
      };
    };
    _count: {
      select: {
        products: true;
        children: true;
      };
    };
  };
}>;

/**
 * Get all categories with hierarchical structure
 */
export async function getAllCategories(): Promise<CategoryWithTranslations[] | ErrorResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PRODUCT_MANAGE_CATEGORIES
    );

    if (!hasPermission) {
      return { error: 'Insufficient permissions' };
    }

    const categories = await prisma.categories.findMany({
      include: {
        translations: true,
        children: {
          include: {
            translations: true,
          },
        },
        parent: {
          include: {
            translations: true,
          },
        },
        _count: {
          select: {
            products: true,
            children: true,
          },
        },
      },
      orderBy: [
        { parent_id: 'asc' },
        { id: 'asc' },
      ],
    });

    return categories;
  } catch (error) {
    console.error('Error fetching categories:', error);
    return { error: 'Failed to fetch categories' };
  }
}

/**
 * Get category by ID with full details
 */
export async function getCategoryById(
  categoryId: number
): Promise<CategoryWithTranslations | ErrorResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PRODUCT_MANAGE_CATEGORIES
    );

    if (!hasPermission) {
      return { error: 'Insufficient permissions' };
    }

    const category = await prisma.categories.findUnique({
      where: { id: categoryId },
      include: {
        translations: true,
        children: {
          include: {
            translations: true,
          },
        },
        parent: {
          include: {
            translations: true,
          },
        },
        _count: {
          select: {
            products: true,
            children: true,
          },
        },
      },
    });

    if (!category) {
      return { error: 'Category not found' };
    }

    return category;
  } catch (error) {
    console.error('Error fetching category:', error);
    return { error: 'Failed to fetch category' };
  }
}

/**
 * Create category with translations
 */
export async function createCategory(data: {
  parent_id?: number;
  translations: Array<{
    language_code: string;
    name: string;
    slug: string;
    description?: string;
  }>;
}): Promise<SuccessResponseWithId> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PRODUCT_MANAGE_CATEGORIES
    );

    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }

    // Validate parent category exists if provided
    if (data.parent_id) {
      const parentExists = await prisma.categories.findUnique({
        where: { id: data.parent_id },
      });

      if (!parentExists) {
        return { success: false, error: 'Parent category not found' };
      }
    }

    const result = await prisma.$transaction(async (tx) => {
      // Create category
      const category = await tx.categories.create({
        data: {
          parent_id: data.parent_id,
        },
      });

      // Create translations
      if (data.translations.length > 0) {
        await tx.category_translations.createMany({
          data: data.translations.map(t => ({
            category_id: category.id,
            language_code: t.language_code,
            name: t.name,
            slug: t.slug,
            description: t.description,
          })),
        });
      }

      return category;
    });

    revalidatePath('/admin/categories');

    return { success: true, categoryId: result.id };
  } catch (error) {
    console.error('Error creating category:', error);
    return { success: false, error: 'Failed to create category' };
  }
}

/**
 * Update category with translations
 */
export async function updateCategory(
  categoryId: number,
  data: {
    parent_id?: number;
    translations: Array<{
      language_code: string;
      name: string;
      slug: string;
      description?: string;
    }>;
  }
): Promise<SuccessResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PRODUCT_MANAGE_CATEGORIES
    );

    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }

    // Validate parent category exists if provided and is not self
    if (data.parent_id) {
      if (data.parent_id === categoryId) {
        return { success: false, error: 'Category cannot be its own parent' };
      }

      const parentExists = await prisma.categories.findUnique({
        where: { id: data.parent_id },
      });

      if (!parentExists) {
        return { success: false, error: 'Parent category not found' };
      }
    }

    await prisma.$transaction(async (tx) => {
      // Update category
      await tx.categories.update({
        where: { id: categoryId },
        data: {
          parent_id: data.parent_id,
        },
      });

      // Update translations - delete existing and recreate
      await tx.category_translations.deleteMany({
        where: { category_id: categoryId },
      });

      if (data.translations.length > 0) {
        await tx.category_translations.createMany({
          data: data.translations.map(t => ({
            category_id: categoryId,
            language_code: t.language_code,
            name: t.name,
            slug: t.slug,
            description: t.description,
          })),
        });
      }
    });

    revalidatePath('/admin/categories');
    revalidatePath(`/admin/categories/${categoryId}`);

    return { success: true };
  } catch (error) {
    console.error('Error updating category:', error);
    return { success: false, error: 'Failed to update category' };
  }
}

/**
 * Delete category (admin)
 */
export async function deleteCategory(
  categoryId: number
): Promise<SuccessResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PRODUCT_MANAGE_CATEGORIES
    );

    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }

    // Check if category has children
    const childrenCount = await prisma.categories.count({
      where: { parent_id: categoryId },
    });

    if (childrenCount > 0) {
      return { success: false, error: 'Cannot delete category with subcategories' };
    }

    // Check if category has products
    const productsCount = await prisma.product_categories.count({
      where: { category_id: categoryId },
    });

    if (productsCount > 0) {
      return { success: false, error: 'Cannot delete category with products' };
    }

    // Delete category (cascade will handle translations)
    await prisma.categories.delete({
      where: { id: categoryId },
    });

    revalidatePath('/admin/categories');

    return { success: true };
  } catch (error) {
    console.error('Error deleting category:', error);
    return { success: false, error: 'Failed to delete category' };
  }
}
