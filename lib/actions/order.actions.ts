'use server';

// lib/actions/order.actions.ts
// Server actions for order management

import { Prisma } from '@/app/generated/prisma';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from './auth.actions';
import { revalidatePath } from 'next/cache';
import type { CartItem, Address } from '@/lib/types';
import { ORDERS_PER_PAGE } from '@/lib/constants';
import {
  buildCursorWhereClause,
  buildCursorOrderBy,
  applyCursorPagination
} from '@/lib/utils/cursor-pagination';
import { validateCSRFToken } from '@/lib/utils/csrf';

/**
 * Create a new order from cart items with server-side price calculation
 */
export async function createOrder(data: {
  items: CartItem[];
  shippingAddress: Address;
  userCurrency: string;
  shippingCost: number;
  shippingMethod: 'boat' | 'plane';
}) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return {
        success: false,
        error: 'Not authenticated',
      };
    }

    const { items, shippingAddress, userCurrency, shippingCost, shippingMethod } = data;

    // Enhanced server-side input validation
    if (!Array.isArray(items) || !items.length) {
      return {
        success: false,
        error: 'Cart is empty or invalid',
      };
    }

    if (items.length > 100) {
      return {
        success: false,
        error: 'Too many items in cart (maximum 100)',
      };
    }

    // Validate each item has required fields with enhanced checks
    for (const item of items) {
      if (!item || typeof item !== 'object') {
        return {
          success: false,
          error: 'Invalid cart item structure',
        };
      }

      if (!Number.isInteger(item.productId) || item.productId <= 0) {
        return {
          success: false,
          error: 'Invalid product ID in cart item',
        };
      }

      if (!Number.isInteger(item.quantity) || item.quantity <= 0 || item.quantity > 999) {
        return {
          success: false,
          error: 'Invalid quantity in cart item (must be 1-999)',
        };
      }

      if (typeof item.price !== 'number' || item.price < 0 || !isFinite(item.price)) {
        return {
          success: false,
          error: 'Invalid price in cart item',
        };
      }

      if (typeof item.currency !== 'string' || item.currency.length !== 3) {
        return {
          success: false,
          error: 'Invalid currency in cart item',
        };
      }
    }

    // Validate shipping address with enhanced checks
    if (!shippingAddress || typeof shippingAddress !== 'object') {
      return {
        success: false,
        error: 'Invalid shipping address structure',
      };
    }

    if (!shippingAddress.fullName || typeof shippingAddress.fullName !== 'string' ||
        shippingAddress.fullName.trim().length === 0 || shippingAddress.fullName.length > 100) {
      return {
        success: false,
        error: 'Invalid full name',
      };
    }

    if (!shippingAddress.phone || typeof shippingAddress.phone !== 'string' ||
        shippingAddress.phone.trim().length === 0 || shippingAddress.phone.length > 20) {
      return {
        success: false,
        error: 'Invalid phone number',
      };
    }

    // Validate currency
    if (!userCurrency || typeof userCurrency !== 'string' || userCurrency.length !== 3) {
      return {
        success: false,
        error: 'Invalid currency code',
      };
    }

    // Validate shipping cost
    if (typeof shippingCost !== 'number' || shippingCost < 0 || !isFinite(shippingCost) || shippingCost > 10000) {
      return {
        success: false,
        error: 'Invalid shipping cost',
      };
    }

    // Validate shipping method
    if (!['boat', 'plane'].includes(shippingMethod)) {
      return {
        success: false,
        error: 'Invalid shipping method',
      };
    }

    // Import services for server-side price calculation
    const { pricingService } = await import('@/lib/services/pricing');
    const { exchangeRateService } = await import('@/lib/services/exchange-rate');

    // Get current exchange rate for the user's currency
    const exchangeRate = await exchangeRateService.getExchangeRate('CNY', userCurrency);

    // Fetch product data to get base prices
    const itemProductIds = items.map(item => item.productId);
    const itemProducts = await prisma.products.findMany({
      where: {
        id: { in: itemProductIds },
        can_show: true,
      },
      select: {
        id: true,
        marketplace: true,
        product_url: true,
        offers: {
          orderBy: { price_low: 'asc' },
          take: 1,
          select: {
            price_low: true,
            price_high: true,
            currency: true,
            min_quantity: true,
          },
        },
      },
    });

    // Create map of product data with null checks
    const productDataMap = new Map(
      itemProducts
        .filter(product => product.offers && product.offers.length > 0 && product.offers[0]?.price_low)
        .map(product => [product.id, {
          costPriceCNY: Number(product.offers[0].price_low),
          marketplace: product.marketplace,
          productUrl: product.product_url,
        }])
    );

    // Calculate final prices for each cart item server-side
    const orderItems = await Promise.all(
      items.map(async (item) => {
        const productData = productDataMap.get(item.productId);
        if (!productData) {
          throw new Error(`Product ${item.productId} not found or not available`);
        }

        const pricingResult = await pricingService.calculatePrice(productData.costPriceCNY, {
          productId: item.productId,
          marketplace: productData.marketplace,
          userCurrency: userCurrency,
        });

        return {
          ...item,
          price: pricingResult.displayPrice,
          currency: pricingResult.currency,
          costPriceCNY: productData.costPriceCNY,
          productUrl: productData.productUrl,
          marketplace: productData.marketplace,
        };
      })
    );

    // Calculate total from server-calculated prices
    const subtotal = orderItems.reduce((sum, item) => sum + item.price * item.quantity, 0);
    const totalAmount = subtotal + shippingCost;

    // Validate calculated amounts
    if (totalAmount <= 0 || !isFinite(totalAmount)) {
      return {
        success: false,
        error: 'Invalid order total amount',
      };
    }

    if (orderItems.length === 0) {
      return {
        success: false,
        error: 'No valid items in order',
      };
    }

    // Create order with order items in a transaction
    const order = await prisma.$transaction(async (tx) => {
      // Create order
      const newOrder = await tx.orders.create({
        data: {
          customer_id: user.customerId,
          shipping_address: shippingAddress as unknown as Prisma.InputJsonValue,
          shipping_method: shippingMethod,
          status: 'unpaid',
          total_amount: totalAmount,
          currency: userCurrency,
          exchange_rate: exchangeRate,
          shipping_cost: shippingCost,
        },
      });

      // Prepare order items data for batch insert using server-calculated prices
      const orderItemsData = orderItems.map(item => {
        let notes = `Marketplace: ${item.marketplace}`;
        if (item.variantId && item.variantName) {
          notes += ` | Variant: ${item.variantName}`;
        }

        return {
          order_id: newOrder.id,
          product_id: item.productId,
          variant_id: item.variantId || null,
          quantity: item.quantity,
          price_per_unit: item.price,
          image_url: item.imageUrl,
          marketplace_product_url: item.productUrl,
          marketplace_notes: notes,
        };
      });

      // Batch create order items
      if (orderItemsData.length > 0) {
        await tx.order_items.createMany({
          data: orderItemsData,
        });
      }

      return newOrder;
    });

    revalidatePath('/account/orders');

    return {
      success: true,
      orderId: order.id,
    };
  } catch (error) {
    console.error('Error creating order:', error);
    return {
      success: false,
      error: 'Failed to create order',
    };
  }
}

/**
 * Get user orders with cursor-based pagination
 */
export async function getUserOrders(cursor?: string, limit: number = ORDERS_PER_PAGE) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return null;
    }

    const orders = await prisma.orders.findMany({
        where: {
          customer_id: user.customerId,
          ...buildCursorWhereClause(cursor, 'created', 'id'),
        },
        orderBy: buildCursorOrderBy('created', 'id'),
        take: limit + 1, // Fetch one extra to determine hasMore
        include: {
          order_items: {
            take: 5, // Limit items shown in list
            include: {
              product: {
                include: {
                  translations: {
                    take: 1,
                  },
                },
              },
              variant: {
                include: {
                  translations: {
                    take: 1,
                  },
                },
              },
            },
          },
        },
      });

    // Apply cursor pagination logic
    const paginationResult = applyCursorPagination(orders, {
      cursor,
      limit,
      timestampField: 'created',
      idField: 'id',
    });

    // Convert Decimal fields to numbers for client compatibility
    const serializedOrders = paginationResult.data.map(order => ({
      ...order,
      total_amount: Number(order.total_amount),
      exchange_rate: Number(order.exchange_rate),
      shipping_cost: Number(order.shipping_cost),
      order_items: order.order_items.map(item => ({
        ...item,
        quantity: Number(item.quantity),
        price_per_unit: Number(item.price_per_unit),
        product: {
          ...item.product,
          weight: item.product.weight ? Number(item.product.weight) : null,
        },
        variant: item.variant ? {
          ...item.variant,
          available_quantity: Number(item.variant.available_quantity),
          min_quantity: Number(item.variant.min_quantity),
          price_low: Number(item.variant.price_low),
          price_high: Number(item.variant.price_high),
        } : null,
      })),
    }));

    return {
      orders: serializedOrders,
      pagination: {
        limit,
        hasMore: paginationResult.hasMore,
        nextCursor: paginationResult.nextCursor,
        prevCursor: paginationResult.prevCursor,
      },
    };
  } catch (error) {
    console.error('Error fetching orders:', error);
    return null;
  }
}

/**
 * Get single order by ID
 * Only returns if order belongs to current user
 */
export async function getOrderById(orderId: string) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return null;
    }

    const order = await prisma.orders.findFirst({
      where: {
        id: orderId,
        customer_id: user.customerId, // Security: only user's own orders
      },
      include: {
        order_items: {
          include: {
            product: {
              include: {
                translations: {
                  take: 1,
                },
                product_images: {
                  where: {
                    image_type: 'preview',
                  },
                  take: 1,
                },
              },
            },
            variant: {
              include: {
                translations: {
                  take: 1,
                },
              },
            },
          },
        },
        payments: true,
      },
    });

    // Convert Decimal fields to numbers for client compatibility
    if (order) {
      return {
        ...order,
        total_amount: Number(order.total_amount),
        exchange_rate: Number(order.exchange_rate),
        shipping_cost: Number(order.shipping_cost),
        order_items: order.order_items.map(item => ({
          ...item,
          quantity: Number(item.quantity),
          price_per_unit: Number(item.price_per_unit),
          product: {
            ...item.product,
            weight: item.product.weight ? Number(item.product.weight) : null,
          },
          variant: item.variant ? {
            ...item.variant,
            available_quantity: Number(item.variant.available_quantity),
            min_quantity: Number(item.variant.min_quantity),
            price_low: Number(item.variant.price_low),
            price_high: Number(item.variant.price_high),
          } : null,
        })),
      };
    }

    return order;
  } catch (error) {
    console.error('Error fetching order:', error);
    return null;
  }
}

/**
 * Update order status
 * Only allows updating user's own orders
 */
export async function updateOrderStatus(orderId: string, status: 'unpaid' | 'paid' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded') {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return {
        success: false,
        error: 'Not authenticated',
      };
    }

    const updatedOrder = await prisma.orders.updateMany({
      where: {
        id: orderId,
        customer_id: user.customerId, // Security: only user's own orders
      },
      data: {
        status,
        updated: new Date(),
      },
    });

    if (updatedOrder.count === 0) {
      return {
        success: false,
        error: 'Order not found or unauthorized',
      };
    }

    return {
      success: true,
      message: `Order status updated to ${status}`,
    };
  } catch (error) {
    console.error('Error updating order status:', error);
    return {
      success: false,
      error: 'Failed to update order status',
    };
  }
}
