'use server';

// lib/actions/auth.actions.ts
// Server actions for authentication and user management

import { prisma } from '@/lib/prisma';
import { checkPermission } from '@/lib/auth/permissions';
import { PermissionAction } from '@/app/generated/prisma';
import { ID_TOKEN_COOKIE_NAME } from '@/lib/constants';

/**
 * Create a new customer record after Firebase signup
 * Called from the client after successful Firebase registration or auto-created during login
 */
export async function createNewCustomer(data: {
  firebaseUid: string;
  email: string;
  fullName: string;
  phone?: string;
}) {
  try {
    // Check if customer already exists
    const existingCustomer = await prisma.customers.findUnique({
      where: { firebase_uid: data.firebaseUid },
    });

    if (existingCustomer) {
      return {
        success: true,
        customerId: existingCustomer.id,
      };
    }

    // Validate required fields
    if (!data.email) {
      return {
        success: false,
        error: 'Email is required to create customer account',
      };
    }

    // Create new customer
    const customer = await prisma.customers.create({
      data: {
        firebase_uid: data.firebaseUid,
        email: data.email,
        full_name: data.fullName,
        phone: data.phone || null,
        addresses: [],
        preferred_currency: 'USD',
      },
    });

    return {
      success: true,
      customerId: customer.id,
    };
  } catch (error) {
    console.error('Error creating customer:', error);
    return {
      success: false,
      error: 'Failed to create customer account',
    };
  }
}

/**
 * Get customer by Firebase UID
 */
export async function getCustomerByFirebaseUid(firebaseUid: string) {
  try {
    const customer = await prisma.customers.findUnique({
      where: { firebase_uid: firebaseUid },
      include: {
        roles: {
          include: {
            role: {
              include: {
                permissions: {
                  include: {
                    permission: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    return customer;
  } catch (error) {
    console.error('Error fetching customer:', error);
    return null;
  }
}

/**
 * Get current user session from cookie
 * This is a helper to be used in Server Components
 */
export async function getCurrentUser() {
  // This will be called from Server Components
  // The middleware already verified the session
  // We just need to extract the user info from the cookie

  const { cookies } = await import('next/headers');
  const sessionCookie = (await cookies()).get(ID_TOKEN_COOKIE_NAME)?.value;

  if (!sessionCookie) {
    return null;
  }

  try {
    const { getAdminAuth } = await import('@/lib/firebase/server');
    const auth = getAdminAuth();
    const decodedClaims = await auth.verifyIdToken(sessionCookie);

    // Get customer data from database
    let customer = await getCustomerByFirebaseUid(decodedClaims.uid);

    // If customer doesn't exist, create one (handles missing records from OAuth or other auth methods)
    if (!customer) {
      // Only create customer if we have required data (email is required)
      if (!decodedClaims.email) {
        console.error('Cannot create customer record: email is required but not provided by Firebase');
        return null;
      }

      const createResult = await createNewCustomer({
        firebaseUid: decodedClaims.uid,
        email: decodedClaims.email,
        fullName: decodedClaims.name || decodedClaims.email.split('@')[0] || 'User',
      });

      if (!createResult.success) {
        console.error('Failed to create customer record for authenticated user:', createResult.error);
        return null;
      }

      // Fetch the newly created customer
      customer = await getCustomerByFirebaseUid(decodedClaims.uid);
      if (!customer) {
        console.error('Failed to fetch newly created customer');
        return null;
      }
    }

    return {
      uid: decodedClaims.uid,
      email: decodedClaims.email || customer.email,
      customerId: customer.id,
      fullName: customer.full_name,
      preferredCurrency: customer.preferred_currency,
      roles: customer.roles,
    };
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}

/**
 * Check if current user has admin access (client-safe server action)
 */
export async function checkAdminAccess(): Promise<boolean> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return false;
    }

    const hasAccess = await checkPermission(
      user.uid,
      PermissionAction.ACCESS_ADMIN_DASHBOARD
    );

    return hasAccess;
  } catch (error) {
    console.error('Error checking admin access:', error);
    return false;
  }
}

/**
 * Validate if current session is still active (for any authenticated user)
 */
export async function validateSession(): Promise<boolean> {
  try {
    const user = await getCurrentUser();
    return !!user; // Return true if user exists (session is valid)
  } catch (error) {
    console.error('Error validating session:', error);
    return false;
  }
}
