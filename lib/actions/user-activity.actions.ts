'use server';

// lib/actions/user-activity.actions.ts
// Server actions for user activity tracking

import { userActivityService } from '@/lib/services/user-activity';
import { getCurrentUser } from './auth.actions';

/**
 * Track product view activity
 */
export async function trackProductView(productId: number, marketplace: string) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      // Anonymous user - skip tracking for now
      return { success: true };
    }

    const sessionId = `session_${Date.now()}_${Math.random()}`;
    await userActivityService.trackProductView(
      user.customerId,
      productId,
      sessionId,
      marketplace
    );

    return { success: true };
  } catch (error) {
    console.error('Error tracking product view:', error);
    return { success: false, error: 'Failed to track activity' };
  }
}

/**
 * Track cart addition activity
 */
export async function trackCartAdd(productId: number, marketplace: string) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      // Anonymous user - skip tracking for now
      return { success: true };
    }

    const sessionId = `session_${Date.now()}_${Math.random()}`;
    await userActivityService.trackCartAdd(
      user.customerId,
      productId,
      sessionId,
      marketplace
    );

    return { success: true };
  } catch (error) {
    console.error('Error tracking cart add:', error);
    return { success: false, error: 'Failed to track activity' };
  }
}

/**
 * Track purchase activity (called during checkout)
 */
export async function trackPurchase(productId: number, marketplace: string, sessionId: string) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { success: false, error: 'User not authenticated' };
    }

    await userActivityService.trackPurchase(
      user.customerId,
      productId,
      sessionId,
      marketplace
    );

    return { success: true };
  } catch (error) {
    console.error('Error tracking purchase:', error);
    return { success: false, error: 'Failed to track activity' };
  }
}