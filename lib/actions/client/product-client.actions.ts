'use client';

// lib/actions/client/product-client.actions.ts
// Client-side actions for product-related operations

import { getProductPricing, getBatchPricing } from '@/lib/actions/product-pricing.actions';
import { getProducts } from '@/lib/actions/product.actions';
import type { ProductFiltersState } from '@/lib/types';
import type { BatchPricingResponse } from '@/lib/actions/product-pricing.actions';

type PricingResponse = Awaited<ReturnType<typeof getProductPricing>>;

declare global {
  interface Window {
    batchPricingRequests?: Record<string, Promise<BatchPricingResponse | null>>;
    pricingCache?: Map<string, Promise<PricingResponse>>;
  }
}

/**
 * Fetch products for infinite scroll
 */
export async function getMoreProducts(filters: ProductFiltersState & { page: number }, locale: string, currency: string) {
  try {
    const result = await getProducts(filters, locale, undefined, undefined, currency);
    return result;
  } catch (error: unknown) {
    console.error('Error fetching more products:', error);
    return { data: [], pagination: { hasMore: false } };
  }
}

/**
 * Fetch personalized pricing for a product
 */
export async function getPersonalizedProductPricing(productId: number, userCurrency: string, locale: string = 'en'): Promise<PricingResponse> {
  try {
    // Create cache key for this request
    const cacheKey = `pricing-${productId}-${userCurrency}-${locale}`;

    // Check if we already have this request cached
    if (typeof window !== 'undefined') {
      if (!window.pricingCache) {
        window.pricingCache = new Map();
      }

      const existingRequest = window.pricingCache.get(cacheKey);
      if (existingRequest) {
        console.log('Using cached personalized pricing request for:', cacheKey);
        return existingRequest;
      }

      // Create the promise and cache it
      const pricingPromise = getProductPricing(productId, userCurrency, locale);

      // Cache the promise
      window.pricingCache.set(cacheKey, pricingPromise);

      // Clean up cache after promise resolves
      pricingPromise.finally(() => {
        if (window.pricingCache) {
          window.pricingCache.delete(cacheKey);
        }
      });

      return pricingPromise;
    }

    return await getProductPricing(productId, userCurrency, locale);
  } catch (error: unknown) {
    console.error('Error fetching personalized pricing:', error);
    return null;
  }
}

/**
 * Fetch batch pricing for multiple products with caching
 */
export async function getBatchProductPricing(productIds: number[], userCurrency: string, locale: string = 'en') {
  try {
    // Validate input to prevent unnecessary requests
    if (!productIds || productIds.length === 0 || !userCurrency) {
      return null;
    }

    // Create cache key
    const cacheKey = `batch-pricing-${productIds.sort().join(',')}-${userCurrency}-${locale}`;

    // Check if we already have a request in flight for this exact same data
    if (typeof window !== 'undefined') {
      const windowWithCache = window as typeof window & { batchPricingRequests?: Record<string, Promise<BatchPricingResponse | null>> };
      const existingRequest = windowWithCache.batchPricingRequests?.[cacheKey];
      if (existingRequest) {
        console.log('Using cached batch pricing request for:', cacheKey);
        return existingRequest;
      }

      // Initialize cache if not exists
      if (!windowWithCache.batchPricingRequests) {
        windowWithCache.batchPricingRequests = {};
      }

      // Create the promise and cache it
      const pricingPromise = getBatchPricing({
        productIds,
        currency: userCurrency,
        locale,
      });

      // Cache the promise
      windowWithCache.batchPricingRequests[cacheKey] = pricingPromise;

      // Clean up cache after promise resolves
      pricingPromise.finally(() => {
        if (windowWithCache.batchPricingRequests) {
          delete windowWithCache.batchPricingRequests[cacheKey];
        }
      });

      return pricingPromise;
    }

    return await getBatchPricing({
      productIds,
      currency: userCurrency,
      locale,
    });
  } catch (error: unknown) {
    console.error('Error fetching batch pricing:', error);
    return null;
  }
}