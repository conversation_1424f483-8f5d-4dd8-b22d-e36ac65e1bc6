'use server';

// lib/actions/cart.actions.ts
// Server actions for Firestore-based cart management

import { getAdminFirestore } from '@/lib/firebase/server';
import { getCurrentUser } from './auth.actions';
import { pricingService } from '@/lib/services/pricing';
// import { exchangeRateService } from '@/lib/services/exchange-rate';
import { CartData, FirestoreCart, FirestoreCartItem } from '@/lib/types';
import { validateCSRFToken } from '@/lib/utils/csrf';

/**
 * Get user's cart from Firestore
 */
export async function getCart(): Promise<CartData | null> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return null;
    }

    const adminDb = getAdminFirestore();
    const cartRef = adminDb.doc(`carts/${user.uid}`);
    const cartSnap = await cartRef.get();

    if (!cartSnap.exists) {
      // Return empty cart
      return {
        items: [],
        totals: {
          subtotal: 0,
          total: 0,
          currency: 'USD',
        },
        itemCount: 0,
      };
    }

    const cartData = cartSnap.data() as FirestoreCart;

    // Schema validation and data sanitization
    if (!cartData || typeof cartData !== 'object') {
      throw new Error('Invalid cart data structure');
    }

    // Ensure items array exists and is valid
    if (!Array.isArray(cartData.items)) {
      cartData.items = [];
    } else {
      // Validate each item has required fields
      cartData.items = cartData.items.filter(item =>
        item &&
        typeof item === 'object' &&
        typeof item.productId === 'number' &&
        typeof item.quantity === 'number' &&
        item.quantity > 0 &&
        typeof item.price === 'number' &&
        typeof item.currency === 'string'
      );
    }

    // Ensure totals object exists and is valid
    if (!cartData.totals || typeof cartData.totals !== 'object') {
      cartData.totals = {
        subtotal: 0,
        total: 0,
      };
    } else {
      // Validate totals values
      const subtotal = typeof cartData.totals.subtotal === 'number' ? cartData.totals.subtotal : 0;
      const total = typeof cartData.totals.total === 'number' ? cartData.totals.total : 0;
      cartData.totals = { subtotal, total };
    }

    // Ensure currency exists and is valid
    if (typeof cartData.currency !== 'string' || cartData.currency.length === 0) {
      cartData.currency = 'USD';
    }

    // Ensure itemCount is valid
    if (typeof cartData.itemCount !== 'number' || cartData.itemCount < 0) {
      cartData.itemCount = cartData.items.reduce((sum, item) => sum + item.quantity, 0);
    }

    // Check if any items are missing variant names and populate them
    const itemsNeedingVariantNames = cartData.items.filter(item =>
      item.variantId && !item.variantName
    );

    if (itemsNeedingVariantNames.length > 0) {
      const { prisma } = await import('@/lib/prisma');
      const variantIds = itemsNeedingVariantNames.map(item => item.variantId!);

      const variants = await prisma.variants.findMany({
        where: { id: { in: variantIds } },
        select: {
          id: true,
          original_variant_name: true,
          original_variant_type: true,
          translations: {
            where: { language_code: 'en' },
            take: 1,
            select: { variant_name: true, variant_type: true },
          },
        },
      });

      // Update cart items with variant names
      const variantMap = new Map(variants.map(v => [Number(v.id), v]));

      for (const item of cartData.items) {
        if (item.variantId && !item.variantName) {
          const variant = variantMap.get(item.variantId);
          if (variant) {
            const variantName = variant.translations[0]?.variant_name || variant.original_variant_name;
            const variantType = variant.translations[0]?.variant_type || variant.original_variant_type;
            item.variantName = variantType && variantName ? `${variantType}: ${variantName}` : variantName;
          }
        }
      }

      // Update Firestore with the populated variant names
      await cartRef.set(cartData);
    }

    // Convert to client format
    const clientItems = cartData.items.map(item => ({
      productId: item.productId,
      variantId: item.variantId,
      quantity: item.quantity,
      productName: item.productName,
      productSlug: item.productSlug,
      variantName: item.variantName,
      price: item.price,
      currency: item.currency,
      imageUrl: item.imageUrl,
      marketplace: item.marketplace,
    }));

    return {
      items: clientItems,
      totals: {
        subtotal: cartData.totals.subtotal,
        total: cartData.totals.total,
        currency: cartData.currency,
      },
      itemCount: cartData.itemCount,
    };
  } catch (error) {
    console.error('Error getting cart:', error);
    return null;
  }
}

/**
 * Add item to cart with server-side price calculation
 */
export async function addToCart(data: {
  productId: number;
  variantId?: number;
  quantity: number;
  imageUrl?: string;
  isBuyNow?: boolean;
}): Promise<{ success: boolean; error?: string; cartId?: string }> {
  try {
    // CSRF protection for server actions
    // Note: In Next.js 13+ server actions, CSRF is handled by the framework
    // but we add additional validation for critical operations

    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: 'Authentication required' };
    }

    const adminDb = getAdminFirestore();
    const { productId, variantId, quantity, imageUrl: providedImageUrl, isBuyNow = false } = data;

    // Server-side input validation
    if (!Number.isInteger(productId) || productId <= 0) {
      return { success: false, error: 'Invalid product ID' };
    }

    if (variantId !== undefined && (!Number.isInteger(variantId) || variantId <= 0)) {
      return { success: false, error: 'Invalid variant ID' };
    }

    if (!Number.isInteger(quantity) || quantity <= 0 || quantity > 999) {
      return { success: false, error: 'Invalid quantity (must be 1-999)' };
    }

    if (providedImageUrl && (typeof providedImageUrl !== 'string' || providedImageUrl.length > 2000)) {
      return { success: false, error: 'Invalid image URL' };
    }

    // Get user's currency preference
    let userCurrency = 'USD';
    try {
      const userDoc = await adminDb.doc(`users/${user.uid}`).get();
      if (userDoc.exists) {
        userCurrency = userDoc.data()?.preferredCurrency || 'USD';
      }
    } catch (error) {
      console.error('Error getting user currency:', error);
    }

    // Fetch product data
    const { prisma } = await import('@/lib/prisma');
    const product = await prisma.products.findUnique({
      where: {
        id: productId,
        can_show: true,
      },
      select: {
        id: true,
        marketplace: true,
        product_url: true,
        translations: {
          where: { language_code: 'en' }, // Default to English
          take: 1,
          select: { name: true, slug: true },
        },
        product_images: {
          where: { image_type: 'preview' },
          take: 1,
          select: { image_url: true },
        },
        offers: {
          orderBy: { min_quantity: 'asc' },
          select: { min_quantity: true, price_low: true, price_high: true },
        },
        variants: variantId ? {
          where: { id: variantId },
          take: 1,
          select: {
            id: true,
            original_variant_name: true,
            original_variant_type: true,
            price_low: true,
            price_high: true,
            translations: {
              where: { language_code: 'en' }, // Default to English
              take: 1,
              select: { variant_name: true, variant_type: true },
            },
          },
        } : false,
      },
    });

    if (!product || product.offers.length === 0) {
      return { success: false, error: 'Product not found or not available' };
    }

    // Determine cost price based on variant or quantity-based offer
    let costPriceCNY: number;

    if (variantId && product.variants && product.variants.length > 0) {
      // Use variant price if variant selected
      costPriceCNY = Number(product.variants[0].price_low);
    } else {
      // Find appropriate offer based on quantity
      const applicableOffer = product.offers
        .sort((a, b) => Number(a.min_quantity || 0) - Number(b.min_quantity || 0))
        .find(offer => quantity >= Number(offer.min_quantity || 1));

      costPriceCNY = Number(applicableOffer?.price_low || product.offers[0].price_low);
    }

    // Calculate price server-side using pricing service
    const pricingResult = await pricingService.calculatePrice(costPriceCNY, {
      productId,
      marketplace: product.marketplace,
      userCurrency,
    });

    // Get variant name if variant is selected
    let variantName: string | undefined;
    if (variantId && product.variants && product.variants.length > 0) {
      const variant = product.variants[0] as {
        id: number;
        original_variant_name: string;
        original_variant_type: string;
        translations?: Array<{
          variant_name: string;
          variant_type: string;
        }>;
      };
      // Use translated variant name if available, otherwise use original
      variantName = variant.translations?.[0]?.variant_name || variant.original_variant_name;
      // Include variant type for better clarity
      const variantType = variant.translations?.[0]?.variant_type || variant.original_variant_type;
      if (variantType && variantName) {
        variantName = `${variantType}: ${variantName}`;
      }
    }

    // Prepare cart item
    const cartItem: FirestoreCartItem = {
      productId,
      quantity,
      price: pricingResult.displayPrice,
      currency: pricingResult.currency,
      productName: product.translations[0]?.name || 'Product',
      productSlug: product.translations[0]?.slug || `product-${productId}`,
      variantName,
      imageUrl: providedImageUrl || product.product_images[0]?.image_url,
      marketplace: product.marketplace,
      addedAt: new Date(),
      ...(variantId !== undefined && { variantId }),
      ...(variantName !== undefined && { variantName }),
    };

    // Get or create cart (use different collection for buy now)
    const cartCollection = isBuyNow ? 'buy_now_carts' : 'carts';
    const cartRef = adminDb.doc(`${cartCollection}/${user.uid}`);
    const cartSnap = await cartRef.get();

    let existingItems: FirestoreCartItem[] = [];
    let currentCurrency = userCurrency;

    if (cartSnap.exists) {
      const cartData = cartSnap.data() as FirestoreCart;
      existingItems = cartData.items || [];
      currentCurrency = cartData.currency || userCurrency;

      // Check if currency changed - if so, we need to recalculate all prices
      if (currentCurrency !== userCurrency) {
        // Currency changed, recalculate all items
        const recalculatedItems = await Promise.all(
          existingItems.map(async (item) => {
            const itemProduct = await prisma.products.findUnique({
              where: { id: item.productId },
              select: {
                offers: {
                  orderBy: { min_quantity: 'asc' },
                  select: { min_quantity: true, price_low: true },
                },
                variants: item.variantId ? {
                  where: { id: item.variantId },
                  take: 1,
                  select: { price_low: true },
                } : false,
                marketplace: true,
              },
            });

            if (itemProduct) {
              // Determine cost price based on variant or quantity-based offer
              let itemCostPriceCNY: number;

              if (item.variantId && itemProduct.variants && itemProduct.variants.length > 0) {
                // Use variant price if variant was selected
                itemCostPriceCNY = Number(itemProduct.variants[0].price_low);
              } else {
                // Find appropriate offer based on item quantity
                const applicableOffer = itemProduct.offers
                  .sort((a, b) => Number(a.min_quantity || 0) - Number(b.min_quantity || 0))
                  .find(offer => item.quantity >= Number(offer.min_quantity || 1));

                itemCostPriceCNY = Number(applicableOffer?.price_low || itemProduct.offers[0].price_low);
              }

              const itemPricingResult = await pricingService.calculatePrice(
                itemCostPriceCNY,
                {
                  productId: item.productId,
                  marketplace: itemProduct.marketplace,
                  userCurrency,
                }
              );

              return {
                ...item,
                price: itemPricingResult.displayPrice,
                currency: itemPricingResult.currency,
              };
            }
            return item;
          })
        );
        existingItems = recalculatedItems;
        currentCurrency = userCurrency;
      }
    }

    // Check if item already exists
    const existingIndex = existingItems.findIndex(
      item => item.productId === productId && item.variantId === variantId
    );

    if (existingIndex > -1) {
      // Update quantity
      existingItems[existingIndex].quantity += quantity;
    } else {
      // Add new item
      existingItems.push(cartItem);
    }

    // Calculate totals (shipping and taxes will be applied via pricing rules at checkout)
    const subtotal = existingItems.reduce((sum, item) => sum + item.price * item.quantity, 0);
    const total = subtotal;

    // Get existing createdAt or set new one
    const existingCreatedAt = cartSnap.exists ? (cartSnap.data() as FirestoreCart).createdAt : undefined;
    const createdAt = existingCreatedAt || new Date();

    // Save to Firestore
    const cartData: FirestoreCart = {
      userId: user.uid,
      items: existingItems,
      currency: currentCurrency,
      totals: {
        subtotal,
        total,
      },
      itemCount: existingItems.reduce((sum, item) => sum + item.quantity, 0),
      updatedAt: new Date(),
      createdAt,
    };

    await cartRef.set(cartData);

    return {
      success: true,
      cartId: isBuyNow ? `buy_now_${user.uid}` : undefined
    };
  } catch (error) {
    console.error('Error adding to cart:', error);
    return { success: false, error: 'Failed to add item to cart' };
  }
}

/**
 * Update item quantity in cart
 */
export async function updateCartQuantity(data: {
  productId: number;
  quantity: number;
  variantId?: number;
}): Promise<{ success: boolean; error?: string }> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: 'Authentication required' };
    }

    // Server-side input validation
    if (!Number.isInteger(data.productId) || data.productId <= 0) {
      return { success: false, error: 'Invalid product ID' };
    }

    if (data.variantId !== undefined && (!Number.isInteger(data.variantId) || data.variantId <= 0)) {
      return { success: false, error: 'Invalid variant ID' };
    }

    if (!Number.isInteger(data.quantity) || data.quantity < 0 || data.quantity > 999) {
      return { success: false, error: 'Invalid quantity (must be 0-999)' };
    }

    if (data.quantity <= 0) {
      return await removeFromCart({ productId: data.productId, variantId: data.variantId });
    }

    const adminDb = getAdminFirestore();
    const cartRef = adminDb.doc(`carts/${user.uid}`);
    const cartSnap = await cartRef.get();

    if (!cartSnap.exists) {
      return { success: false, error: 'Cart not found' };
    }

    const cartData = cartSnap.data() as FirestoreCart;
    const itemIndex = cartData.items.findIndex(
      item => item.productId === data.productId && item.variantId === data.variantId
    );

    if (itemIndex === -1) {
      return { success: false, error: 'Item not found in cart' };
    }

    // Update quantity
    cartData.items[itemIndex].quantity = data.quantity;

    // Recalculate totals (shipping and taxes will be applied via pricing rules at checkout)
    const subtotal = cartData.items.reduce((sum, item) => sum + item.price * item.quantity, 0);
    const total = subtotal;

    const updatedCart: FirestoreCart = {
      ...cartData,
      totals: {
        subtotal,
        total,
      },
      itemCount: cartData.items.reduce((sum, item) => sum + item.quantity, 0),
      updatedAt: new Date(),
    };

    await cartRef.set(updatedCart);

    // Invalidate cart query cache to ensure totals are updated
    // Note: This is handled by the React Query hook, but we ensure server-side consistency

    return { success: true };
  } catch (error) {
    console.error('Error updating cart quantity:', error);
    return { success: false, error: 'Failed to update item quantity' };
  }
}

/**
 * Remove item from cart
 */
export async function removeFromCart(data: {
  productId: number;
  variantId?: number;
}): Promise<{ success: boolean; error?: string }> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: 'Authentication required' };
    }

    const adminDb = getAdminFirestore();
    const cartRef = adminDb.doc(`carts/${user.uid}`);
    const cartSnap = await cartRef.get();

    if (!cartSnap.exists) {
      return { success: false, error: 'Cart not found' };
    }

    const cartData = cartSnap.data() as FirestoreCart;

    // Remove item
    cartData.items = cartData.items.filter(
      item => !(item.productId === data.productId && item.variantId === data.variantId)
    );

    if (cartData.items.length === 0) {
      // Delete empty cart
      await cartRef.set({});
      return { success: true };
    }

    // Recalculate totals (shipping and taxes will be applied via pricing rules at checkout)
    const subtotal = cartData.items.reduce((sum, item) => sum + item.price * item.quantity, 0);
    const total = subtotal;

    const updatedCart: FirestoreCart = {
      ...cartData,
      totals: {
        subtotal,
        total,
      },
      itemCount: cartData.items.reduce((sum, item) => sum + item.quantity, 0),
      updatedAt: new Date(),
    };

    await cartRef.set(updatedCart);

    return { success: true };
  } catch (error) {
    console.error('Error removing from cart:', error);
    return { success: false, error: 'Failed to remove item from cart' };
  }
}

/**
 * Get minimum quantity for a product
 */
export async function getProductMinQuantity(productId: number): Promise<{ minQuantity: number; error?: string }> {
  try {
    const { prisma } = await import('@/lib/prisma');
    const product = await prisma.products.findUnique({
      where: { id: productId },
      select: {
        offers: {
          orderBy: { min_quantity: 'asc' },
          take: 1,
          select: { min_quantity: true },
        },
      },
    });

    if (!product || product.offers.length === 0) {
      return { minQuantity: 1 };
    }

    return { minQuantity: Number(product.offers[0].min_quantity) || 1 };
  } catch (error) {
    console.error('Error getting product min quantity:', error);
    return { minQuantity: 1, error: 'Failed to get minimum quantity' };
  }
}

/**
 * Clear entire cart
 */
export async function clearCart(): Promise<{ success: boolean; error?: string }> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: 'Authentication required' };
    }

    const adminDb = getAdminFirestore();
    const cartRef = adminDb.doc(`carts/${user.uid}`);

    // Delete the cart document
    await cartRef.set({});

    return { success: true };
  } catch (error) {
    console.error('Error clearing cart:', error);
    return { success: false, error: 'Failed to clear cart' };
  }
}

/**
 * Migrate cart from localStorage to Firestore (for existing users)
 */
export async function migrateCart(localCartItems: Array<{
  productId: number;
  variantId?: number;
  quantity: number;
}>): Promise<{ success: boolean; error?: string }> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: 'Authentication required' };
    }

    // Process each item through addToCart
    for (const item of localCartItems) {
      const result = await addToCart(item);
      if (!result.success) {
        console.warn('Failed to migrate cart item:', item, result.error);
      }
    }

    return { success: true };
  } catch (error) {
    console.error('Error migrating cart:', error);
    return { success: false, error: 'Failed to migrate cart' };
  }
}

/**
 * Get buy now cart for checkout
 */
export async function getBuyNowCart(): Promise<CartData | null> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return null;
    }

    const adminDb = getAdminFirestore();
    const cartRef = adminDb.doc(`buy_now_carts/${user.uid}`);
    const cartSnap = await cartRef.get();

    if (!cartSnap.exists) {
      return null;
    }

    const cartData = cartSnap.data() as FirestoreCart;

    // Convert to client format
    const clientItems = cartData.items.map(item => ({
      productId: item.productId,
      variantId: item.variantId,
      quantity: item.quantity,
      productName: item.productName,
      productSlug: item.productSlug,
      variantName: item.variantName,
      price: item.price,
      currency: item.currency,
      imageUrl: item.imageUrl,
      marketplace: item.marketplace,
    }));

    return {
      items: clientItems,
      totals: {
        subtotal: cartData.totals.subtotal,
        total: cartData.totals.total,
        currency: cartData.currency,
      },
      itemCount: cartData.itemCount,
    };
  } catch (error) {
    console.error('Error getting buy now cart:', error);
    return null;
  }
}

/**
 * Clear buy now cart after checkout
 */
export async function clearBuyNowCart(): Promise<{ success: boolean; error?: string }> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: 'Authentication required' };
    }

    const adminDb = getAdminFirestore();
    const cartRef = adminDb.doc(`buy_now_carts/${user.uid}`);
    await cartRef.set({});

    return { success: true };
  } catch (error) {
    console.error('Error clearing buy now cart:', error);
    return { success: false, error: 'Failed to clear buy now cart' };
  }
}