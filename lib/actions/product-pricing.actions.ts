'use server';

// lib/actions/product-pricing.actions.ts
// Server Actions for product pricing calculations

import { prisma } from '@/lib/prisma';
import { pricingService } from '@/lib/services/pricing';

interface BatchPricingRequest {
  productIds: number[];
  currency: string;
  locale: string;
}

export interface BatchPricingResponse {
  [productId: number]: {
    offers: Array<{
      id: number;
      min_quantity: number | null;
      price_low: number;
      price_high: number | null;
      currency: string;
      quantity_info: string | null;
      display_price?: number;
      display_currency?: string;
      exchange_rate?: number;
    }>;
    variants: Array<{
      id: number;
      original_variant_name: string;
      original_variant_type: string;
      price_low: number;
      price_high: number | null;
      currency: string;
      available_quantity: number | null;
      min_quantity: number | null;
      display_price?: number;
      display_currency?: string;
      exchange_rate?: number;
    }>;
  };
}

/**
 * Get personalized pricing for a single product
 */
export async function getProductPricing(productId: number, currency: string = 'USD', locale: string = 'en') {
  try {
    // Find product by ID to get slug for fetching
    const productRecord = await prisma.products.findUnique({
      where: {
        id: productId,
        can_show: true,
      },
      select: {
        translations: {
          where: { language_code: locale },
          take: 1,
          select: { slug: true },
        },
      },
    });

    if (!productRecord?.translations[0]?.slug) {
      return null;
    }

    const slug = productRecord.translations[0].slug;

    // Fetch product with personalized pricing
    const { getProductBySlug } = await import('@/lib/actions/product.actions');
    const product = await getProductBySlug(slug, locale, currency);

    if (!product) {
      return null;
    }

    // Return only the pricing-related data (with display pricing added by getProductBySlug)
    const pricingData = {
      id: product.id,
      offers: product.offers,
      variants: product.variants,
    };

    return pricingData;
  } catch (error) {
    console.error('Error in getProductPricing:', error);
    return null;
  }
}

/**
 * Get batch pricing for multiple products
 */
export async function getBatchPricing({ productIds, currency, locale }: BatchPricingRequest): Promise<BatchPricingResponse | null> {
  try {
    if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
      return null;
    }

    if (!currency || typeof currency !== 'string') {
      return null;
    }

    // Limit batch size to prevent abuse
    if (productIds.length > 100) {
      return null;
    }

    // Fetch product data for pricing calculation
    const products = await fetchProductsForPricing(productIds, locale);

    if (products.length === 0) {
      return null;
    }

    // Prepare pricing inputs
    const pricingInputs = products.flatMap(product => [
      // Offers pricing
      ...product.offers.map(offer => ({
        costPriceCNY: Number(offer.price_low),
        context: {
          productId: product.id,
          categoryId: product.categories[0]?.category.id,
          marketplace: product.marketplace as string,
          userCurrency: currency,
        },
      })),
      // Variants pricing
      ...product.variants.map(variant => ({
        costPriceCNY: Number(variant.price_low),
        context: {
          productId: product.id,
          categoryId: product.categories[0]?.category.id,
          marketplace: product.marketplace as string,
          userCurrency: currency,
        },
      })),
    ]);

    // Calculate prices in batch
    const pricingResults = await pricingService.calculatePrices(pricingInputs);

    // Organize results by product
    const result: BatchPricingResponse = {};
    let resultIndex = 0;

    for (const product of products) {
      result[product.id] = {
        offers: product.offers.map((offer, offerIndex) => {
          const pricingResult = pricingResults[resultIndex + offerIndex];
          return {
            id: offer.id,
            min_quantity: offer.min_quantity ? Number(offer.min_quantity) : null,
            price_low: Number(offer.price_low),
            price_high: offer.price_high ? Number(offer.price_high) : null,
            currency: offer.currency,
            quantity_info: offer.quantity_info,
            ...(pricingResult && {
              display_price: pricingResult.displayPrice,
              display_currency: pricingResult.currency,
              exchange_rate: pricingResult.exchangeRate,
            }),
          };
        }),
        variants: product.variants.map((variant, variantIndex) => {
          const pricingResult = pricingResults[resultIndex + product.offers.length + variantIndex];
          return {
            id: variant.id,
            original_variant_name: variant.original_variant_name,
            original_variant_type: variant.original_variant_type,
            price_low: Number(variant.price_low),
            price_high: variant.price_high ? Number(variant.price_high) : null,
            currency: variant.currency,
            available_quantity: variant.available_quantity ? Number(variant.available_quantity) : null,
            min_quantity: variant.min_quantity ? Number(variant.min_quantity) : null,
            ...(pricingResult && {
              display_price: pricingResult.displayPrice,
              display_currency: pricingResult.currency,
              exchange_rate: pricingResult.exchangeRate,
            }),
          };
        }),
      };

      resultIndex += product.offers.length + product.variants.length;
    }

    return result;
  } catch (error) {
    console.error('Error in batch pricing Server Action:', error);
    return null;
  }
}

async function fetchProductsForPricing(productIds: number[], locale: string) {
  return await prisma.products.findMany({
    where: {
      id: { in: productIds },
      can_show: true,
    },
    select: {
      id: true,
      marketplace: true,
      offers: {
        orderBy: { min_quantity: 'asc' },
        take: 20, // Limit offers
      },
      variants: {
        take: 50, // Limit variants
      },
      categories: {
        take: 1,
        select: {
          category: {
            select: { id: true },
          },
        },
      },
    },
  });
}