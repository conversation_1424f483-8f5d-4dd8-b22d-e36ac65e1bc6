### Foundational Security & Configuration

#### **NEW: `lib/auth/permissions.ts`**
*   **Purpose**: To provide a centralized, reusable, and secure way to check user permissions on the server. This is the core of the RBAC system.
*   **Backend Logic**:
    *   Contains a primary function, `checkPermission(firebaseUid, requiredPermission)`.
    *   This function takes a user's Firebase UID and a specific `PermissionAction` enum (e.g., `PRODUCT_UPDATE`).
    *   It performs a single, efficient Prisma query to fetch the user, their assigned `Roles`, and all `Permissions` associated with those roles.
    *   It returns `true` if the user has the required permission through any of their roles, and `false` otherwise. This function will be used in layouts, pages, and Server Actions to enforce security.

#### **`middleware.ts` (Admin Security Update)**
*   **Purpose**: To provide the first, edge-level line of defense for the entire `/admin` area.
*   **Backend Logic**:
    *   It intercepts all requests where the pathname starts with `/admin`.
    *   It retrieves the secure session cookie (`__session`). If the cookie is missing or invalid (verified by the Firebase Admin SDK), it immediately redirects to `/login`.
    *   If the cookie is valid, it retrieves the user's Firebase UID.
    *   It then performs a crucial check to see if the user has the `ACCESS_ADMIN_DASHBOARD` permission. This is done by calling a lightweight helper or a targeted Prisma query.
    *   If the user lacks this specific permission, they are redirected away from the admin area (e.g., to their `/account` page), preventing any attempt to render admin components.

---

### `src/app/admin/` (Core Admin Routes)

#### **`layout.tsx` (Admin Root Layout)**
*   **Purpose**: The main shell for the admin dashboard, acting as the second, definitive server-side security checkpoint.
*   **UI Features**: Renders the `AdminSidebar` and `AdminHeader`, providing consistent navigation and branding across the admin panel.
*   **Backend Logic (Critical Security)**:
    *   As a **Server Component**, it fetches the current user's session from the verified cookie.
    *   It performs its own non-negotiable permission check: `if (!await checkPermission(user.uid, PermissionAction.ACCESS_ADMIN_DASHBOARD)) { redirect('/login'); }`.
    *   This ensures that even if the middleware logic were to change, no part of the admin UI can ever be rendered for an unauthorized user.
*   **Navigation Flow**: This layout is always active for any route under `/admin`. The sidebar links will navigate between the different admin pages, and the visibility of these links can be conditionally rendered based on the user's specific permissions.

#### **`page.tsx` (Admin Dashboard)**
*   **Purpose**: The main landing page for an administrator, providing a high-level overview of the store's activity.
*   **UI Features**: A grid of `StatCard` components for key metrics (revenue, new orders, pending payments) and a `SalesChart` for visualizing data. It also shows a list of recent orders for quick access.
*   **Backend Logic**: This **Server Component** first confirms the user has `ACCESS_ADMIN_DASHBOARD` permission. It then makes parallel Prisma calls to efficiently fetch all the data required for the dashboard widgets.

---

### `src/app/admin/orders/` (Order Management)

#### **`page.tsx` (Order List Page)**
*   **Purpose**: To view, search, and filter all orders in the system.
*   **UI Features**: A `PageHeader`, search/filter inputs, and an interactive `OrderTable` (using TanStack Table) with sortable columns and pagination. Each order row links to its detail page.
*   **Backend Logic**: Before rendering, this **Server Component** asserts that the user has the `ORDER_READ_ALL` permission. It then uses URL `searchParams` to build a dynamic Prisma query to fetch the exact orders that match the admin's criteria.

#### **`[orderId]/page.tsx` (Order Detail & Management Page)**
*   **Purpose**: The primary workspace for managing an individual order, including confirming manual payments.
*   **UI Features**:
    *   Detailed order and customer information.
    *   An `UpdateStatusForm` which allows an admin to change the order status (`pending` -> `processing`, etc.) and add details like a tracking number.
*   **Backend Logic**:
    *   The page component first checks for `ORDER_READ_ALL` permission to display the order details.
    *   The `UpdateStatusForm` submits to a **Server Action**. This action is the critical part: it begins by checking if the user has the `ORDER_UPDATE_STATUS` permission. This ensures that a user who can only *view* orders cannot change their status. If the check passes, it updates the order in the database via Prisma and uses `revalidatePath` to refresh the UI.
*   **Workflow**: This is the heart of the manual payment flow. An admin receives payment confirmation externally, finds the order on this page, and updates its status, which is then reflected in the customer's account view.

---

### `src/app/admin/products/` (Product Management)

#### **`page.tsx` (Product List Page)**
*   **Purpose**: To browse and manage the product catalog.
*   **UI Features**: A data table of all products. A "Create New Product" button is **conditionally rendered**, only appearing if the logged-in admin has the `PRODUCT_CREATE` permission.
*   **Backend Logic**: The page is protected by the `PRODUCT_READ` permission.

#### **`new/page.tsx` & `[productId]/page.tsx` (Create/Edit Product Pages)**
*   **Purpose**: Provides a form for creating or editing product details.
*   **UI Features**: A comprehensive `ProductForm` (Client Component) to manage all product fields and its related entities (variants, attributes, offers).
*   **Backend Logic**:
    *   The `new` page requires `PRODUCT_CREATE` permission.
    *   The `edit` (`[productId]`) page requires `PRODUCT_UPDATE` permission.
    *   The `ProductForm` submits to a `createOrUpdateProduct` **Server Action**. This action checks for the appropriate permission (`PRODUCT_CREATE` or `PRODUCT_UPDATE`) before executing a `prisma.$transaction` to ensure all related product data is saved atomically.

---

### `src/app/admin/customers/` & `src/app/admin/pricing/` (RBAC-aware)

#### **`customers/page.tsx`**
*   **Purpose**: To view and manage registered users and their roles.
*   **UI Features**: A data table listing all customers. Each row links to a detail page.
*   **Backend Logic**: Protected by the `CUSTOMER_READ` permission.

#### **`customers/[customerId]/page.tsx`**
*   **Purpose**: View a customer's details and manage their roles.
*   **UI Features**: Displays the customer's profile and order history. Critically, it includes a form with a multi-select or checkboxes to assign/unassign `Roles` to this user.
*   **Backend Logic**:
    *   Viewing the page requires `CUSTOMER_READ`.
    *   The role assignment form submits to a **Server Action** that is protected by the `CUSTOMER_ASSIGN_ROLE` permission. This ensures only specific, high-level admins can change user permissions.

#### **`pricing/page.tsx`**
*   **Purpose**: To manage the business's pricing strategy via the `pricing_rules` table.
*   **UI Features**: A data table listing all pricing rules, with forms (likely in modals) to create, edit, or delete them.
*   **Backend Logic**: Every **Server Action** on this page (e.g., `createRule`, `updateRule`) is protected by a single, powerful `PRICING_RULE_MANAGE` permission.