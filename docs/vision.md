### Core App Vision:

*   **Marketplace Aggregator:** You are creating a unified storefront that aggregates products from multiple Chinese e-commerce platforms. This simplifies the shopping experience for international buyers who may find it difficult to navigate these marketplaces directly due to language barriers, payment restrictions, or shipping complexities.
*   **Logistics and Consolidation Hub:** The presence of the `warehouse_receipts` model is a key indicator. It shows that you are not a simple dropshipping service. Instead, you operate a warehouse (likely in China) where items from different marketplace orders are received, inspected, and consolidated before being shipped internationally to the end customer. This allows customers to buy from multiple sellers and receive a single package, saving significantly on shipping costs.
*   **Full-Service Procurement Agent:** Your platform manages the entire process:
    *   It presents products to a global audience.
    *   It handles payments in foreign currencies and deals with exchange rates.
    *   It places the actual orders on the Chinese marketplaces on behalf of the customer.
    *   It manages inbound domestic logistics (from the marketplace seller to your warehouse).
    *   It handles outbound international logistics (from your warehouse to the customer).
*   **Data-Driven and Scalable:** Features like `user_activity` tracking, `product_similarity`, and dynamic `pricing_rules` show a vision for a smart, scalable platform. You aim to personalize the user experience and automate your pricing strategy to ensure profitability.
*   **Robust and Secure for Team Management:** The detailed Role-Based Access Control (RBAC) system (`Role`, `Permission`) indicates that this application is intended to be operated by a team with different responsibilities (e.g., product managers, order processors, administrators), ensuring a secure and organized workflow.

### Likely Application Workflow:

Here is a breakdown of the probable workflow based on your schema.

#### Part 1: The Customer's Journey

1.  **Discovery:** A customer browses your application, viewing products that have been sourced and listed from Taobao, Pinduoduo, and Alibaba. The system tracks their viewing history (`user_activity`) to potentially offer recommendations (`product_similarity`).
2.  **Ordering:** The customer adds items, including specific `variants`, to their cart and places an `order`. They provide their international `shipping_address`.
3.  **Payment:** The customer pays the `total_amount` in their `preferred_currency` (e.g., USD). Your system processes this through the `payments` model, recording the `exchange_rate` applied at the time of the transaction. The order `status` is now `pending` or `processing`.

#### Part 2: The Backend/Administrative Workflow

4.  **Procurement:** Your administrative team (or an automated system) is notified of the new order. For each `order_item` in the customer's order, your team places a corresponding order on the original Chinese marketplace using the `marketplace_product_url`. The `marketplace_order_id` from this purchase is saved.
5.  **Inbound Logistics (Warehouse Arrival):**
    *   The marketplace seller ships the item to your warehouse in China.
    *   When the package arrives, a warehouse operator creates a `warehouse_receipts` record. They note the `package_weight` and match the package to the internal `order_item` using the `marketplace_order_id`. The receipt `status` is changed from `pending` to `matched`.
6.  **Consolidation and Outbound Shipping:**
    *   Once all `order_items` for a customer's `order` have arrived at the warehouse (i.e., all have a `matched` warehouse receipt), the items are consolidated into a single shipment.
    *   The final package is prepared for international shipping. The main `order` status is updated to `shipped`. The `shipping_label_url`, `tracking_number`, and `carrier` are updated in the relevant `warehouse_receipts` to provide tracking information to the customer.
7.  **Delivery and Completion:** The package is delivered to the customer. The order `status` is updated to `delivered`, completing the workflow.

This entire process is managed by your internal team, whose members have specific permissions (e.g., an `ORDER_UPDATE_STATUS` permission) as defined by your robust RBAC system, ensuring a controlled and secure operational flow.