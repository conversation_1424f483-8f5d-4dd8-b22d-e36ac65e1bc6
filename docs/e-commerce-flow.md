### Foundational Files & Configuration

*   **`prisma/schema.prisma`**
    *   **Purpose**: The single source of truth for your database structure.
    *   **Features**: Defines the entire database schema using Prisma models, including the critical `customers`, `Role`, and `Permission` models for granular Role-Based Access Control (RBAC). It also establishes all relationships, indexes, and constraints, ensuring data integrity.

*   **`.env.local`**
    *   **Purpose**: Stores all secret credentials and environment-specific variables.
    *   **Features**: Contains the `DATABASE_URL` for your Neon PostgreSQL database. Critically, it also holds two sets of Firebase credentials:
        1.  **Server-side Admin SDK credentials** (`FIREBASE_PROJECT_ID`, `FIREBASE_CLIENT_EMAIL`, `FIREBASE_PRIVATE_KEY`) used for verifying users and managing sessions on the server.
        2.  **Client-side SDK configuration** (`NEXT_PUBLIC_FIREBASE_...`) which are public keys used in the browser to power the login/register UI.

*   **`middleware.ts`**
    *   **Purpose**: Acts as the primary security gatekeeper for the application, protecting routes at the network edge before any rendering occurs.
    *   **Backend Logic**:
        *   Intercepts requests to protected routes (e.g., `/account/*`, `/checkout/*`).
        *   Looks for a secure, `HttpOnly` session cookie (e.g., `__session`) that was set by your backend upon login.
        *   If the cookie is present, it uses the **Firebase Admin SDK** to verify its validity.
        *   If the cookie is missing or invalid, it redirects the user to the `/login` page.
        *   If a logged-in user tries to access `/login` or `/register`, it redirects them to their account dashboard (`/account`).
    *   **Navigation Flow**: Enforces the application's authentication state, ensuring only authenticated users can access private areas.

---

### `src/app/` (Root App Directory)

#### **`layout.tsx` (Root Layout)**
*   **Purpose**: The main shell and entry point for the entire application, rendered on the server.
*   **UI Features**:
    *   Renders the `<html>` and `<body>` tags.
    *   Imports and renders the global `Navbar` and `Footer` components.
    *   Wraps the application in necessary client-side providers, such as a `ThemeProvider` (for dark mode) and a custom `FirebaseAuthProvider`. This custom provider will manage the Firebase client-side auth state (`onAuthStateChanged`) and make it available to client components.

#### **`globals.css`**
*   **Purpose**: Contains the base Tailwind CSS directives and any custom global styles, ensuring a consistent design foundation.

---

### `src/app/(marketing)/` (Publicly Accessible Pages)

#### **`page.tsx` (Homepage)**
*   **Purpose**: The main landing page to attract customers. This is a **Server Component**.
*   **UI Features**: A hero section, grids of featured or new products, and links to major product categories.
*   **Backend Logic**: Fetches a curated list of products directly from the database using Prisma (e.g., `prisma.products.findMany(...)`) for optimal performance.
*   **Navigation Flow**: Serves as the primary entry point, navigating users to the main Product Listing Page (`/products`) or specific Product Detail Pages.

#### **`products/page.tsx` (Product Listing Page - PLP)**
*   **Purpose**: The main catalog page for browsing, searching, and filtering all products. This is a **Server Component**.
*   **UI Features**:
    *   A grid of `ProductCard` components.
    *   Client-side components for filtering and sorting, which update the URL's `searchParams` without a full page reload.
    *   Pagination controls to navigate through the product catalog.
*   **Backend Logic**: Reads `searchParams` from the URL to construct a dynamic, filtered, and paginated Prisma query, ensuring the server only fetches the exact products needed.
*   **Navigation Flow**: Users arrive from the homepage or navbar. They navigate to the PDP by clicking a product or refresh the view by applying filters.

#### **`products/[slug]/page.tsx` (Product Detail Page - PDP)**
*   **Purpose**: Displays all information for a single product. This is a **Server Component**.
*   **UI Features**: Product image gallery, name, calculated selling price (from `pricing_rules`), variant selectors, a quantity input, and an "Add to Cart" button. These interactive elements are **Client Components**.
*   **Backend Logic**: Fetches all data for a single product (including its variants, images, attributes, etc.) using the `slug` parameter. It also applies the relevant pricing rules to calculate the final display price.
*   **Navigation Flow**: Arriving from the PLP or homepage. The "Add to Cart" button updates the client-side cart state and provides visual feedback.

#### **`cart/page.tsx` (Shopping Cart)**
*   **Purpose**: Allows users to review and manage their selected items before checkout. This is a **Client Component**.
*   **UI Features**: A list of items in the cart, with controls to update quantity or remove items. It displays a real-time summary of the subtotal and total.
*   **Backend Logic**: The cart's state is primarily managed on the client using a Zustand store (`use-cart-store`) for a fast, interactive experience.
*   **Navigation Flow**: Accessed via the cart icon in the navbar or after adding a product. The "Proceed to Checkout" button, enabled only when the cart is not empty, directs users to the checkout flow (`/checkout/shipping`).

---

### `src/app/(auth)/` (Authentication Pages)

#### **`login/page.tsx` & `register/page.tsx`**
*   **Purpose**: To handle user sign-in and sign-up using Firebase. These pages are primarily **Client Components**.
*   **UI Features**: Forms for email/password input, validation powered by `react-hook-form` and `zod`, and links to switch between login and registration.
*   **Backend Logic (The Firebase Flow)**:
    1.  **Registration**: The form uses the **Firebase Client SDK** (`createUserWithEmailAndPassword`). On success, it receives the new user's `uid` and `email` from Firebase. It then calls a `createNewCustomer` **Server Action**, passing this data to create a corresponding entry in your Prisma `customers` table.
    2.  **Login**: The form uses the **Firebase Client SDK** (`signInWithEmailAndPassword`). On success, it receives a short-lived **ID Token**. The client then sends this token to a dedicated API route (e.g., `/api/auth/login`), which verifies it using the **Firebase Admin SDK** and sets the secure, server-side session cookie in response.
*   **Navigation Flow**: On successful login, the user is redirected to their account dashboard (`/account`) or their originally intended protected page.

---

### `src/app/(account)/` (Protected User Pages)

#### **`layout.tsx`**
*   **Purpose**: Provides a consistent layout for the user's private account section. This is a **Server Component**.
*   **UI Features**: A sidebar with navigation links to "My Orders," "Profile," etc., alongside the main content area.
*   **Backend Logic**: Fetches the current user's data from your Prisma database (using the ID from the verified session cookie) to display their name in the layout.

#### **`page.tsx` (Account Dashboard)**
*   **Purpose**: The main landing page for a logged-in user. This is a **Server Component**.
*   **UI Features**: A welcome message, a summary of recent orders, and quick links to other account sections.
*   **Backend Logic**: Fetches the authenticated user's profile and their most recent orders from the database.
*   **Navigation Flow**: The destination after a successful login or by clicking "My Account."

#### **`profile/page.tsx`**
*   **Purpose**: Allows users to manage their personal information and shipping addresses.
*   **UI Features**: Forms (as Client Components) to update their name, phone, and manage their saved addresses stored in the `addresses` JSONB field.
*   **Backend Logic**: The page is a Server Component that fetches the current user data to pre-fill the forms. The forms themselves submit to **Server Actions** (`updateProfile`, `addAddress`), which first verify the user's session before updating the database.

#### **`orders/page.tsx`**
*   **Purpose**: Displays a complete history of all orders placed by the user. This is a **Server Component**.
*   **UI Features**: A table listing all orders with their ID, date, total, and current status. Each order is a link to its detail page.
*   **Backend Logic**: Fetches all orders from the database where the `customer_id` matches that of the currently authenticated user.

#### **`orders/[orderId]/page.tsx`**
*   **Purpose**: Shows the complete details for a single order, focusing on payment status. This is a **Server Component**.
*   **UI Features**:
    *   Full order details, including the item list and shipping address.
    *   **Payment Status Section**: A critical UI element for your manual payment flow. It clearly displays "Awaiting Payment Confirmation" if the order status is 'pending' and shows the company's payment instructions. If the status is 'processing' or higher, it shows "Payment Received."
*   **Backend Logic**: Fetches the specified `orderId`, but includes a crucial security check to ensure the order belongs to the currently logged-in user before displaying any data.

---

### `src/app/(checkout)/` (Focused Checkout Flow)

#### **`layout.tsx`**
*   **Purpose**: A minimal, distraction-free layout for the checkout process.
*   **UI Features**: Typically hides the main navigation and footer, keeping the user focused on completing the purchase.

#### **`shipping/page.tsx`**
*   **Purpose**: Step 1 of checkout, where the user selects or enters their shipping address.
*   **UI Features**: Displays the user's saved addresses for quick selection and a form to add a new one.
*   **Backend Logic**: This Server Component fetches the user's saved addresses. The selected address is then passed to the next step.

#### **`payment/page.tsx`**
*   **Purpose**: The final step where the user reviews their order and is instructed on how to pay.
*   **UI Features**: An `OrderSummary` component, and most importantly, the `PaymentInstructions` component detailing how and where to send the manual payment.
*   **Backend Logic**: The "Place Order" button triggers a `createOrder` **Server Action**. This is a critical transactional operation that:
    1.  Verifies the user is authenticated.
    2.  Reads the cart and shipping info.
    3.  Creates new `orders` and `order_items` records in the database within a `prisma.$transaction` block to ensure atomicity.
    4.  Sets the initial order `status` to 'pending'.
    5.  Clears the user's cart.
*   **Navigation Flow**: Upon successful creation, it redirects the user to the `success` page, passing the new `orderId`.

#### **`success/[orderId]/page.tsx`**
*   **Purpose**: Confirms to the user that their order has been successfully logged in the system.
*   **UI Features**: A "Thank You" message, the unique `orderId` for their reference, and a final reminder of the payment instructions and next steps.
*   **Navigation Flow**: The final step of the checkout flow, from which users can navigate to their "My Orders" page to track their new order's status.