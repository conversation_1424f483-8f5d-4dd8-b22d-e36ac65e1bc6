// test-isr-performance.js
// Simple performance test for ISR implementation

const { performance } = require('perf_hooks');

async function testProductPagePerformance() {
  console.log('🚀 Testing ISR Product Page Performance...\n');

  const baseUrl = 'http://localhost:3000';
  
  // Test URLs - using common product slugs
  const testUrls = [
    `${baseUrl}/en/products/test-product-1`,
    `${baseUrl}/en/products/test-product-2`,
    `${baseUrl}/fr/products/test-product-1`,
  ];

  for (const url of testUrls) {
    console.log(`Testing: ${url}`);
    
    try {
      const startTime = performance.now();
      
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'ISR-Performance-Test/1.0',
        },
      });
      
      const endTime = performance.now();
      const responseTime = Math.round(endTime - startTime);
      
      console.log(`  Status: ${response.status}`);
      console.log(`  Response Time: ${responseTime}ms`);
      console.log(`  Cache Status: ${response.headers.get('x-nextjs-cache') || 'MISS'}`);
      
      if (response.ok) {
        const html = await response.text();
        
        // Check for ISR indicators
        const hasStaticContent = html.includes('ProductDetailISRClient');
        const hasApiEndpoints = html.includes('/api/products/');
        
        console.log(`  Static Content: ${hasStaticContent ? '✅' : '❌'}`);
        console.log(`  API Integration: ${hasApiEndpoints ? '✅' : '❌'}`);
        
        // Check for performance indicators
        if (responseTime < 100) {
          console.log(`  Performance: ✅ Excellent (${responseTime}ms)`);
        } else if (responseTime < 500) {
          console.log(`  Performance: ⚠️  Good (${responseTime}ms)`);
        } else {
          console.log(`  Performance: ❌ Slow (${responseTime}ms)`);
        }
      } else {
        console.log(`  Error: ${response.statusText}`);
      }
      
    } catch (error) {
      console.log(`  Error: ${error.message}`);
    }
    
    console.log('');
  }
}

async function testApiEndpoints() {
  console.log('🔌 Testing API Endpoints...\n');

  const baseUrl = 'http://localhost:3000';
  
  const apiTests = [
    {
      name: 'Product Pricing API',
      url: `${baseUrl}/api/products/test-product-1/pricing?currency=USD&locale=en`,
    },
    {
      name: 'Related Products API',
      url: `${baseUrl}/api/products/test-product-1/related?currency=USD&locale=en&limit=6`,
    },
    {
      name: 'Product Availability API',
      url: `${baseUrl}/api/products/test-product-1/availability?locale=en`,
    },
    {
      name: 'Wishlist Status API',
      url: `${baseUrl}/api/user/wishlist/status/1`,
    },
  ];

  for (const test of apiTests) {
    console.log(`Testing: ${test.name}`);
    
    try {
      const startTime = performance.now();
      
      const response = await fetch(test.url, {
        headers: {
          'User-Agent': 'ISR-Performance-Test/1.0',
        },
      });
      
      const endTime = performance.now();
      const responseTime = Math.round(endTime - startTime);
      
      console.log(`  Status: ${response.status}`);
      console.log(`  Response Time: ${responseTime}ms`);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`  Response: ${JSON.stringify(data, null, 2).substring(0, 100)}...`);
        
        if (responseTime < 200) {
          console.log(`  Performance: ✅ Fast (${responseTime}ms)`);
        } else if (responseTime < 1000) {
          console.log(`  Performance: ⚠️  Acceptable (${responseTime}ms)`);
        } else {
          console.log(`  Performance: ❌ Slow (${responseTime}ms)`);
        }
      } else {
        console.log(`  Error: ${response.statusText}`);
      }
      
    } catch (error) {
      console.log(`  Error: ${error.message}`);
    }
    
    console.log('');
  }
}

async function runTests() {
  console.log('🧪 ISR Performance Test Suite\n');
  console.log('=' .repeat(50));
  
  // Wait a moment for server to be ready
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  await testProductPagePerformance();
  console.log('=' .repeat(50));
  await testApiEndpoints();
  
  console.log('✅ Test suite completed!');
}

// Run tests
runTests().catch(console.error);
