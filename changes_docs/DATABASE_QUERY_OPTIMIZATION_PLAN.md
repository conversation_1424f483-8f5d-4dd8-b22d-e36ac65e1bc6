# Database Query Optimization Plan 🚀

## 🎯 Objective
Optimize all database queries to leverage the full power and flexibility of the Prisma schema for maximum performance and cost efficiency.

---

## 📊 Current State Analysis

### Existing Optimizations ✅
1. **Parallel Queries**: Using `Promise.all()` for count and data queries
2. **Pagination**: Implemented with `skip` and `take`
3. **Selective Includes**: Only including necessary relations
4. **Indexes**: 26 indexes already defined in schema

### Areas for Improvement 🔧
1. **Selective Field Selection**: Many queries fetch all fields when only a subset is needed
2. **Permission Check Caching**: Permission checks happen on every request
3. **N+1 Query Prevention**: Some queries could be further optimized
4. **Computed Fields**: Some calculations happen in application layer
5. **Missing Indexes**: Some frequently queried fields lack indexes

---

## 🎯 Optimization Strategies

### 1. **Selective Field Selection (SELECT)**
**Impact**: Reduces data transfer by 40-60%

**Before:**
```typescript
prisma.customers.findMany({
  where,
  include: {
    roles: {
      include: {
        role: true  // Fetches ALL role fields
      }
    }
  }
})
```

**After:**
```typescript
prisma.customers.findMany({
  where,
  select: {
    id: true,
    email: true,
    full_name: true,
    created: true,
    roles: {
      select: {
        role: {
          select: {
            id: true,
            name: true  // Only fields we need
          }
        }
      }
    }
  }
})
```

### 2. **Limit Nested Relations**
**Impact**: Prevents over-fetching

**Current:**
```typescript
categories: {
  include: {
    category: {
      include: {
        translations: true  // All translations
      }
    }
  }
}
```

**Optimized:**
```typescript
categories: {
  take: 5,  // Limit to 5 categories
  select: {
    category: {
      select: {
        id: true,
        translations: {
          where: { language_code: locale },
          take: 1,  // Only one translation
          select: {
            name: true  // Only name field
          }
        }
      }
    }
  }
}
```

### 3. **Computed Fields at Database Level**
**Impact**: Reduces application-level calculations

**Example: Order Total Items Count**
```typescript
_count: {
  select: {
    order_items: true  // Database counts, not application
  }
}
```

### 4. **Index Optimization**
**Impact**: Faster query execution

**Recommended Additional Indexes:**
```prisma
// customers table
@@index([email])  // For search
@@index([created])  // For sorting

// products table
@@index([can_show, created])  // Composite for filtering + sorting
@@index([created])  // For newest products

// orders table
@@index([created])  // For sorting
@@index([customer_id, status])  // Composite for customer orders by status

// product_categories table
@@index([product_id, category_id])  // Already has composite PK, but good for lookups

// product_translations table
@@index([slug])  // For slug lookups
```

### 5. **Query Result Caching**
**Impact**: Reduces database load for frequently accessed data

**Candidates for Caching:**
- User permissions (cache for 5-10 minutes)
- Product categories list (cache for 1 hour)
- Dashboard stats (cache for 5 minutes)
- Product details (cache for 15 minutes)

### 6. **Batch Operations**
**Impact**: Reduces round trips

**Example:**
```typescript
// Instead of multiple findUnique calls
const products = await prisma.products.findMany({
  where: {
    id: { in: productIds }
  }
});
```

---

## 📋 Implementation Checklist

### Phase 1: Selective Field Selection
- [ ] `lib/actions/admin/customer.actions.ts`
  - [ ] `getAllCustomers()` - Use select instead of include
  - [ ] `getCustomerByIdAdmin()` - Select only needed fields
  
- [ ] `lib/actions/admin/product.actions.ts`
  - [ ] `getAllProductsAdmin()` - Select only needed fields
  - [ ] `getProductByIdAdmin()` - Optimize nested includes
  
- [ ] `lib/actions/admin/order.actions.ts`
  - [ ] `getAllOrders()` - Select only needed fields
  - [ ] `getOrderByIdAdmin()` - Optimize nested includes
  - [ ] `getDashboardStats()` - Use aggregations

- [ ] `lib/actions/product.actions.ts`
  - [ ] `getProducts()` - Select only needed fields
  - [ ] `getProductBySlug()` - Optimize nested includes
  - [ ] `getCategories()` - Select only needed fields

### Phase 2: Add Missing Indexes
- [ ] Add `@@index([email])` to customers
- [ ] Add `@@index([created])` to customers
- [ ] Add `@@index([can_show, created])` to products
- [ ] Add `@@index([created])` to orders
- [ ] Add `@@index([slug])` to product_translations

### Phase 3: Implement Caching
- [ ] Create caching utility with Redis or in-memory cache
- [ ] Cache user permissions
- [ ] Cache product categories
- [ ] Cache dashboard stats
- [ ] Add cache invalidation on mutations

### Phase 4: Advanced Optimizations
- [ ] Implement database-level full-text search for products
- [ ] Add materialized views for complex aggregations
- [ ] Implement read replicas for heavy read operations
- [ ] Add connection pooling optimization

---

## 📈 Expected Performance Improvements

| Optimization | Data Transfer Reduction | Query Time Reduction | Cost Reduction |
|--------------|------------------------|---------------------|----------------|
| Selective Fields | 40-60% | 20-30% | 30-40% |
| Limit Nested Relations | 30-50% | 15-25% | 20-30% |
| Proper Indexes | N/A | 50-80% | 40-60% |
| Query Caching | 70-90% | 80-95% | 60-80% |
| **Total Estimated** | **50-70%** | **60-80%** | **50-70%** |

---

## 🔍 Query Performance Monitoring

### Metrics to Track
1. **Query Execution Time**: Log slow queries (>100ms)
2. **Data Transfer Size**: Monitor bytes transferred
3. **Query Frequency**: Identify hot paths
4. **Cache Hit Rate**: Track cache effectiveness

### Tools
- Prisma Query Logging: `log: ['query', 'info', 'warn', 'error']`
- PostgreSQL `pg_stat_statements`
- Application Performance Monitoring (APM)

---

## 🚀 Next Steps

1. **Implement Phase 1** (Selective Field Selection) - Immediate impact
2. **Add Missing Indexes** (Phase 2) - Quick wins
3. **Test Performance** - Measure improvements
4. **Implement Caching** (Phase 3) - Long-term optimization
5. **Monitor & Iterate** - Continuous improvement

---

## 📝 Notes

- All optimizations maintain backward compatibility
- Type safety is preserved with Prisma's type system
- Changes are incremental and can be rolled back
- Performance testing should be done with production-like data volumes

---

**Status**: 📋 **PLAN READY - IMPLEMENTATION STARTING**

