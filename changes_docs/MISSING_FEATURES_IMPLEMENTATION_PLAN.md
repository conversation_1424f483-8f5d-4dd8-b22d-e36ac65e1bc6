# 🚧 Missing Features - Implementation Plan

## 📋 Overview

This document outlines the missing features identified during admin flow testing and provides detailed implementation plans for each.

---

## 🔴 **PRIORITY 1: CRITICAL FEATURES**

### **1. Warehouse Management System** ⚠️ **MOST CRITICAL**

#### **Business Impact**
This is the **core differentiator** of the MaoMao platform. Without it, the platform is just a dropshipping service, not a "Logistics and Consolidation Hub" as envisioned.

#### **Schema Already Defined**
```prisma
model warehouse_receipts {
  id                   String               @id @default(uuid())
  order_item_id        BigInt
  received_at          DateTime             @default(now())
  marketplace_order_id String
  package_weight       Decimal
  package_weight_unit  String               @default("g")
  status               WarehouseReceiptStatus @default(pending)
  shipping_label_url   String?
  tracking_number      String?
  carrier              String?

  order_item order_items @relation(fields: [order_item_id], references: [id])

  @@index([marketplace_order_id])
  @@index([status])
}

enum WarehouseReceiptStatus {
  pending
  matched
  shipped
}
```

#### **Required Permissions** (Need to Add)
```prisma
enum PermissionAction {
  // ... existing permissions
  
  // Warehouse Permissions
  WAREHOUSE_RECEIPT_CREATE
  WAREHOUSE_RECEIPT_READ
  WAREHOUSE_RECEIPT_UPDATE
  WAREHOUSE_RECEIPT_DELETE
  WAREHOUSE_MATCH_PACKAGE
  WAREHOUSE_UPDATE_TRACKING
}
```

#### **Implementation Tasks**

##### **A. Server Actions** (`lib/actions/admin/warehouse.actions.ts`)
```typescript
'use server';

import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/actions/auth.actions';
import { checkPermission } from '@/lib/auth/permissions';
import { PermissionAction, WarehouseReceiptStatus } from '@/app/generated/prisma';

/**
 * Get all warehouse receipts with filters
 */
export async function getAllWarehouseReceipts(filters: {
  status?: WarehouseReceiptStatus;
  marketplaceOrderId?: string;
  page?: number;
}) {
  // Check permission: WAREHOUSE_RECEIPT_READ
  // Query warehouse_receipts with order_item details
  // Return paginated results
}

/**
 * Create warehouse receipt when package arrives
 */
export async function createWarehouseReceipt(data: {
  marketplace_order_id: string;
  package_weight: number;
  package_weight_unit: string;
}) {
  // Check permission: WAREHOUSE_RECEIPT_CREATE
  // Find matching order_item by marketplace_order_id
  // Create warehouse_receipt with status 'pending'
  // Return receipt ID
}

/**
 * Match receipt to order item (change status to 'matched')
 */
export async function matchWarehouseReceipt(
  receiptId: string,
  orderItemId: number
) {
  // Check permission: WAREHOUSE_MATCH_PACKAGE
  // Update receipt with order_item_id
  // Change status to 'matched'
  // Check if all items for order are matched
  // If yes, update order status to 'processing'
}

/**
 * Update tracking info and mark as shipped
 */
export async function updateWarehouseTracking(
  receiptId: string,
  data: {
    tracking_number: string;
    carrier: string;
    shipping_label_url?: string;
  }
) {
  // Check permission: WAREHOUSE_UPDATE_TRACKING
  // Update receipt with tracking info
  // Change status to 'shipped'
  // Update order status to 'shipped'
}

/**
 * Get consolidation status for an order
 */
export async function getOrderConsolidationStatus(orderId: string) {
  // Get all order_items for order
  // Get warehouse_receipts for each item
  // Calculate: total items, matched items, shipped items
  // Return consolidation progress
}
```

##### **B. Admin Pages**

**`app/[locale]/admin/warehouse/page.tsx`** - Warehouse Receipts List
```typescript
// Features:
// - List all warehouse receipts
// - Filter by status (pending, matched, shipped)
// - Search by marketplace order ID
// - Show package weight, received date
// - Link to order details
// - Pagination
```

**`app/[locale]/admin/warehouse/new/page.tsx`** - Create Receipt
```typescript
// Features:
// - Form to input marketplace order ID
// - Input package weight and unit
// - Auto-match to order item if found
// - Show matching order item details
// - Create receipt button
```

**`app/[locale]/admin/warehouse/[receiptId]/page.tsx`** - Receipt Detail
```typescript
// Features:
// - Show receipt details
// - Show linked order item
// - Show order customer info
// - Form to update tracking info
// - Button to mark as shipped
// - Status timeline
```

**`app/[locale]/admin/orders/[orderId]/consolidation/page.tsx`** - Order Consolidation View
```typescript
// Features:
// - Show all order items
// - Show warehouse receipt status for each item
// - Show which items are pending/matched/shipped
// - Progress bar for consolidation
// - Button to consolidate and ship when all matched
```

##### **C. UI Components**

**`components/admin/WarehouseReceiptsTable.tsx`**
- Table with filters and search
- Status badges
- Quick actions

**`components/admin/WarehouseReceiptForm.tsx`**
- Form for creating/editing receipts
- Marketplace order ID input with validation
- Package weight input

**`components/admin/ConsolidationStatus.tsx`**
- Visual progress indicator
- List of items with status
- Consolidation actions

##### **D. Update Admin Sidebar**
```typescript
// Add to navigation:
{
  name: t('warehouse'),
  href: `/${locale}/admin/warehouse`,
  icon: Warehouse, // from lucide-react
}
```

##### **E. Update Order Detail Page**
Add "Warehouse Status" section showing:
- Consolidation progress
- Link to consolidation view
- Tracking info when shipped

---

### **2. Payment Management System** ⚠️ **CRITICAL**

#### **Business Impact**
Essential for financial tracking, reconciliation, and order fulfillment workflow.

#### **Schema Already Defined**
```prisma
model payments {
  id             String        @id @default(uuid())
  order_id       String
  amount         Decimal       @db.Decimal(12, 2)
  currency       String        @db.VarChar(3)
  payment_method String        @db.VarChar(50)
  transaction_id String        @unique
  status         PaymentStatus
  created        DateTime      @default(now())

  order orders @relation(fields: [order_id], references: [id])

  @@index([status])
  @@index([order_id])
}

enum PaymentStatus {
  pending
  succeeded
  failed
  refunded
}
```

#### **Required Permissions** (Need to Add)
```prisma
enum PermissionAction {
  // ... existing permissions
  
  // Payment Permissions
  PAYMENT_CREATE
  PAYMENT_READ
  PAYMENT_UPDATE_STATUS
  PAYMENT_REFUND
  PAYMENT_VIEW_SENSITIVE_DATA
}
```

#### **Implementation Tasks**

##### **A. Server Actions** (`lib/actions/admin/payment.actions.ts`)
```typescript
'use server';

/**
 * Get all payments with filters
 */
export async function getAllPayments(filters: {
  status?: PaymentStatus;
  orderId?: string;
  page?: number;
}) {
  // Check permission: PAYMENT_READ
  // Query payments with order details
  // Return paginated results
}

/**
 * Record payment receipt
 */
export async function recordPayment(data: {
  order_id: string;
  amount: number;
  currency: string;
  payment_method: string;
  transaction_id: string;
}) {
  // Check permission: PAYMENT_CREATE
  // Create payment record with status 'pending'
  // Return payment ID
}

/**
 * Update payment status
 */
export async function updatePaymentStatus(
  paymentId: string,
  status: PaymentStatus
) {
  // Check permission: PAYMENT_UPDATE_STATUS
  // Update payment status
  // If status is 'succeeded', update order status to 'processing'
  // If status is 'failed', keep order as 'pending'
}

/**
 * Process refund
 */
export async function processRefund(
  paymentId: string,
  refundAmount: number
) {
  // Check permission: PAYMENT_REFUND
  // Update payment status to 'refunded'
  // Update order status to 'refunded'
  // Create refund record
}
```

##### **B. Admin Pages**

**`app/[locale]/admin/payments/page.tsx`** - Payments List
```typescript
// Features:
// - List all payments
// - Filter by status
// - Search by order ID or transaction ID
// - Show amount, currency, method
// - Link to order details
// - Pagination
```

**`app/[locale]/admin/payments/[paymentId]/page.tsx`** - Payment Detail
```typescript
// Features:
// - Show payment details
// - Show linked order
// - Show customer info
// - Update status form
// - Refund button
// - Payment timeline
```

**`app/[locale]/admin/orders/[orderId]/payment/page.tsx`** - Record Payment for Order
```typescript
// Features:
// - Form to record payment
// - Input transaction ID
// - Select payment method
// - Input amount (pre-filled from order total)
// - Submit button
```

##### **C. Update Order Detail Page**
Add "Payment Status" section showing:
- Payment status badge
- Payment method
- Transaction ID
- Button to record payment if not exists
- Button to update status

##### **D. Update Admin Sidebar**
```typescript
{
  name: t('payments'),
  href: `/${locale}/admin/payments`,
  icon: CreditCard, // from lucide-react
}
```

---

### **3. Marketplace Order ID Input** ⚠️ **CRITICAL**

#### **Business Impact**
Essential for procurement workflow - admin needs to record marketplace order ID after placing order on Taobao/Pinduoduo/Alibaba.

#### **Schema Already Has Fields**
```prisma
model order_items {
  // ... existing fields
  marketplace_order_id    String?
  marketplace_product_url String
  marketplace_notes       String
}
```

#### **Implementation Tasks**

##### **A. Update Server Action** (`lib/actions/admin/order.actions.ts`)
```typescript
/**
 * Update marketplace order ID for order item
 */
export async function updateMarketplaceOrderId(
  orderItemId: number,
  marketplaceOrderId: string,
  notes?: string
) {
  // Check permission: ORDER_UPDATE_STATUS
  // Update order_item with marketplace_order_id
  // Update marketplace_notes if provided
  // Return success
}
```

##### **B. Update Order Detail Page**
Add "Procurement Status" section for each order item:
- Show marketplace product URL (clickable link)
- Input field for marketplace order ID
- Text area for notes
- "Save" button
- Status indicator (pending procurement / ordered / received)

##### **C. UI Component**
**`components/admin/MarketplaceOrderForm.tsx`**
```typescript
// Features:
// - Input for marketplace order ID
// - Link to marketplace product URL
// - Notes text area
// - Save button
// - Status badge
```

---

## 🟡 **PRIORITY 2: HIGH IMPORTANCE**

### **4. Analytics Dashboard**

#### **Implementation Tasks**

##### **A. Server Actions** (`lib/actions/admin/analytics.actions.ts`)
```typescript
/**
 * Get sales analytics
 */
export async function getSalesAnalytics(dateRange: {
  start: Date;
  end: Date;
}) {
  // Revenue by day/week/month
  // Orders by status
  // Top products
  // Top customers
  // Average order value
}

/**
 * Get customer analytics
 */
export async function getCustomerAnalytics() {
  // New customers by period
  // Customer lifetime value
  // Repeat purchase rate
  // Customer segments
}

/**
 * Get product analytics
 */
export async function getProductAnalytics() {
  // Best sellers
  // Low stock alerts
  // Product views vs purchases
  // Category performance
}
```

##### **B. Admin Page**
**`app/[locale]/admin/analytics/page.tsx`**
- Charts for revenue trends
- Order status breakdown
- Top products table
- Customer insights
- Date range selector

---

### **5. Role Management UI**

#### **Implementation Tasks**

##### **A. Server Actions** (`lib/actions/admin/role.actions.ts`)
```typescript
/**
 * Create role
 */
export async function createRole(data: {
  name: string;
  description: string;
  permissionIds: number[];
}) {
  // Check permission: ROLE_CREATE
  // Create role
  // Assign permissions via role_permissions join table
}

/**
 * Update role permissions
 */
export async function updateRolePermissions(
  roleId: number,
  permissionIds: number[]
) {
  // Check permission: PERMISSION_ASSIGN
  // Delete existing role_permissions
  // Create new role_permissions
}

/**
 * Get all permissions
 */
export async function getAllPermissions() {
  // Check permission: ROLE_READ
  // Return all permissions grouped by category
}
```

##### **B. Admin Pages**
**`app/[locale]/admin/roles/page.tsx`** - Roles List
**`app/[locale]/admin/roles/new/page.tsx`** - Create Role
**`app/[locale]/admin/roles/[roleId]/page.tsx`** - Edit Role

---

## 🟢 **PRIORITY 3: MEDIUM IMPORTANCE**

### **6. User Activity Tracking**

#### **Implementation**
- Add middleware to track page views
- Track cart additions in cart store
- Track purchases in order creation
- Create analytics from activity data

### **7. Product Recommendations**

#### **Implementation**
- Calculate similarity based on attributes
- Track "purchased together" patterns
- Show related products on product detail page
- Show "Customers also bought" on cart page

---

## 📊 **IMPLEMENTATION TIMELINE**

### **Phase 1: Critical Features (2-3 weeks)**
- Week 1: Warehouse Management System
- Week 2: Payment Management System
- Week 3: Marketplace Order ID Input + Testing

### **Phase 2: High Priority (1-2 weeks)**
- Week 4: Analytics Dashboard
- Week 5: Role Management UI

### **Phase 3: Medium Priority (1-2 weeks)**
- Week 6: User Activity Tracking
- Week 7: Product Recommendations

---

## 🎯 **SUCCESS CRITERIA**

### **Phase 1 Complete When:**
- ✅ Warehouse operator can create receipts
- ✅ Warehouse operator can match packages to orders
- ✅ Warehouse operator can add tracking info
- ✅ Admin can see consolidation status
- ✅ Admin can record payments
- ✅ Admin can update payment status
- ✅ Admin can input marketplace order IDs
- ✅ Full procurement workflow functional

### **Phase 2 Complete When:**
- ✅ Admin can view sales analytics
- ✅ Admin can generate reports
- ✅ Admin can create/edit roles
- ✅ Admin can assign permissions to roles

### **Phase 3 Complete When:**
- ✅ User activity is tracked
- ✅ Product recommendations are shown
- ✅ Analytics include user behavior data

---

## 📝 **NOTES**

1. **Database Migration**: No schema changes needed - all tables already exist
2. **Permissions**: Need to add warehouse and payment permissions to schema
3. **Seed Data**: Update seed script to include new permissions
4. **Testing**: Each feature needs comprehensive testing
5. **Documentation**: Update user guides for warehouse operators

---

**Document Created**: 2025-09-30  
**Priority**: URGENT - Phase 1 features are blocking full production deployment  
**Estimated Effort**: 6-7 weeks for complete implementation

