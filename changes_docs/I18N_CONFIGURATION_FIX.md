# Internationalization (i18n) Configuration Fix

## Issue
The application was returning 404 errors when accessing localized routes (e.g., `/en`, `/fr`, `/ar`). The error manifested as:
```
GET /en 404 in 9694ms
```

## Root Cause
The internationalization setup was not properly configured for `next-intl` v4.x with Next.js 15. The main issues were:

1. **Missing routing configuration**: `next-intl` v4.x requires a separate routing configuration file using `defineRouting`
2. **Incorrect middleware setup**: The middleware was not using the centralized routing configuration
3. **Missing locale parameter in getMessages()**: The layout was calling `getMessages()` without passing the locale parameter

## Changes Made

### 1. Created Routing Configuration (`i18n/routing.ts`)
Created a new centralized routing configuration file that defines all supported locales and routing behavior:

```typescript
import { defineRouting } from 'next-intl/routing';

export const routing = defineRouting({
  locales: ['en', 'fr', 'ar'],
  defaultLocale: 'en',
  localePrefix: 'always',
});
```

### 2. Updated i18n Configuration (`i18n.ts`)
Updated the main i18n configuration to use the new routing configuration and the `requestLocale` parameter:

**Before:**
```typescript
export default getRequestConfig(async ({ locale }) => {
  if (!locales.includes(locale as Locale)) {
    notFound();
  }
  return {
    locale: locale as string,
    messages: (await import(`./messages/${locale}.json`)).default,
  };
});
```

**After:**
```typescript
import { routing } from './i18n/routing';

export default getRequestConfig(async ({ requestLocale }) => {
  let locale = await requestLocale;
  
  if (!locale || !(routing.locales as readonly string[]).includes(locale)) {
    locale = routing.defaultLocale;
  }
  
  return {
    locale,
    messages: (await import(`./messages/${locale}.json`)).default,
  };
});
```

### 3. Updated Middleware (`middleware.ts`)
Updated the middleware to use the centralized routing configuration:

**Before:**
```typescript
const intlMiddleware = createMiddleware({
  locales: ['en', 'fr', 'ar'],
  defaultLocale: 'en',
  localePrefix: 'always',
});
```

**After:**
```typescript
import { routing } from './i18n/routing';

const intlMiddleware = createMiddleware(routing);
```

Also fixed a minor linting issue by removing the unused `error` variable in the catch block.

### 4. Updated Layout (`app/[locale]/layout.tsx`)
Updated the layout to pass the locale parameter to `getMessages()`:

**Before:**
```typescript
const messages = await getMessages();
```

**After:**
```typescript
const messages = await getMessages({ locale });
```

### 5. Created Navigation Utilities (`i18n/navigation.ts`)
Created type-safe navigation utilities that use the routing configuration:

```typescript
import { createNavigation } from 'next-intl/navigation';
import { routing } from './routing';

export const { Link, redirect, usePathname, useRouter, getPathname } =
  createNavigation(routing);
```

This provides type-safe navigation components and hooks that can be used throughout the application.

### 6. Confirmed Next.js Config (`next.config.ts`)
Ensured the Next.js configuration properly references the i18n configuration file:

```typescript
const withNextIntl = createNextIntlPlugin('./i18n.ts');
```

## Testing Results
After implementing these changes, the application now successfully loads localized routes:

```
✓ Compiled /[locale] in 5.4s
GET /en 200 in 10378ms
```

The 404 errors have been resolved, and all localized routes (`/en`, `/fr`, `/ar`) are now accessible.

## Benefits of the New Setup

1. **Centralized Configuration**: All locale settings are now in one place (`i18n/routing.ts`)
2. **Type Safety**: The new navigation utilities provide type-safe routing
3. **Better Maintainability**: Changes to locale configuration only need to be made in one file
4. **Next.js 15 Compatibility**: Properly configured for Next.js 15's async params pattern
5. **next-intl v4 Best Practices**: Follows the latest recommended patterns from next-intl documentation

## Files Modified
- `i18n.ts` - Updated to use routing configuration and requestLocale
- `middleware.ts` - Updated to use routing configuration
- `app/[locale]/layout.tsx` - Updated getMessages() call
- `next.config.ts` - Confirmed proper configuration

## Files Created
- `i18n/routing.ts` - Centralized routing configuration
- `i18n/navigation.ts` - Type-safe navigation utilities

## Migration Notes
If other parts of the codebase are using hardcoded locale arrays or creating their own navigation utilities, they should be updated to use:
- `routing.locales` from `i18n/routing.ts` for locale lists
- Navigation utilities from `i18n/navigation.ts` for routing

## References
- [next-intl v4 Documentation](https://next-intl.dev/)
- [Next.js 15 App Router](https://nextjs.org/docs)

