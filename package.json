{"name": "ma<PERSON>o", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "prisma generate && next build --turbopack", "start": "next start", "lint": "eslint", "seed": "tsx prisma/seed.ts", "upgrade-to-super-admin": "tsx prisma/upgrade-to-super-admin.ts"}, "dependencies": {"@hookform/resolvers": "^5.2.2", "@prisma/client": "^6.16.2", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-visually-hidden": "^1.2.3", "@tanstack/react-query": "^5.90.5", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "csrf": "^3.1.0", "firebase": "^12.3.0", "firebase-admin": "^13.5.0", "framer-motion": "^12.23.24", "lucide-react": "^0.544.0", "next": "15.5.4", "next-firebase-auth-edge": "^1.11.1", "next-intl": "^4.3.9", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.63.0", "recharts": "^3.2.1", "redis": "^5.9.0", "swr": "^2.3.6", "tailwind-merge": "^3.3.1", "zod": "^4.1.11", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.4", "prisma": "^6.16.2", "tailwindcss": "^4", "tsx": "^4.19.2", "tw-animate-css": "^1.4.0", "typescript": "^5"}}