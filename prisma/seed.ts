// prisma/seed.ts
// Comprehensive RBAC seed with all permissions and roles

import { PrismaClient } from '../app/generated/prisma/index.js';
import { PermissionAction } from '../app/generated/prisma/index.js';

const prisma = new PrismaClient();

// Define all permissions
const allPermissions = [
  // Admin Dashboard Access
  { action: PermissionAction.ACCESS_ADMIN_DASHBOARD, name: 'Access Admin Dashboard' },
  { action: PermissionAction.VIEW_DASHBOARD_STATS, name: 'View Dashboard Statistics' },

  // Product Permissions
  { action: PermissionAction.PRODUCT_CREATE, name: 'Create Products' },
  { action: PermissionAction.PRODUCT_READ, name: 'Read Products' },
  { action: PermissionAction.PRODUCT_UPDATE, name: 'Update Products' },
  { action: PermissionAction.PRODUCT_DELETE, name: 'Delete Products' },
  { action: PermissionAction.PRODUCT_MANAGE_CATEGORIES, name: 'Manage Product Categories' },
  { action: PermissionAction.PRODUCT_MANAGE_VARIANTS, name: 'Manage Product Variants' },
  { action: PermissionAction.PRODUCT_MANAGE_IMAGES, name: 'Manage Product Images' },

  // Order Permissions
  { action: PermissionAction.ORDER_READ_ALL, name: 'Read All Orders' },
  { action: PermissionAction.ORDER_READ_OWN, name: 'Read Own Orders' },
  { action: PermissionAction.ORDER_UPDATE_STATUS, name: 'Update Order Status' },
  { action: PermissionAction.ORDER_CANCEL, name: 'Cancel Orders' },
  { action: PermissionAction.ORDER_REFUND, name: 'Refund Orders' },
  { action: PermissionAction.ORDER_DELETE, name: 'Delete Orders' },
  { action: PermissionAction.ORDER_UPDATE_MARKETPLACE_INFO, name: 'Update Marketplace Order Info' },

  // Customer Permissions
  { action: PermissionAction.CUSTOMER_READ, name: 'Read Customers' },
  { action: PermissionAction.CUSTOMER_UPDATE, name: 'Update Customers' },
  { action: PermissionAction.CUSTOMER_DELETE, name: 'Delete Customers' },
  { action: PermissionAction.CUSTOMER_ASSIGN_ROLE, name: 'Assign Customer Roles' },
  { action: PermissionAction.CUSTOMER_REMOVE_ROLE, name: 'Remove Customer Roles' },
  { action: PermissionAction.CUSTOMER_VIEW_SENSITIVE_DATA, name: 'View Sensitive Customer Data' },

  // Pricing Rule Permissions
  { action: PermissionAction.PRICING_RULE_CREATE, name: 'Create Pricing Rules' },
  { action: PermissionAction.PRICING_RULE_READ, name: 'Read Pricing Rules' },
  { action: PermissionAction.PRICING_RULE_UPDATE, name: 'Update Pricing Rules' },
  { action: PermissionAction.PRICING_RULE_DELETE, name: 'Delete Pricing Rules' },

  // Warehouse Permissions
  { action: PermissionAction.WAREHOUSE_RECEIPT_CREATE, name: 'Create Warehouse Receipts' },
  { action: PermissionAction.WAREHOUSE_RECEIPT_READ, name: 'Read Warehouse Receipts' },
  { action: PermissionAction.WAREHOUSE_RECEIPT_UPDATE, name: 'Update Warehouse Receipts' },
  { action: PermissionAction.WAREHOUSE_RECEIPT_DELETE, name: 'Delete Warehouse Receipts' },
  { action: PermissionAction.WAREHOUSE_MATCH_PACKAGE, name: 'Match Warehouse Packages' },
  { action: PermissionAction.WAREHOUSE_UPDATE_TRACKING, name: 'Update Warehouse Tracking' },
  { action: PermissionAction.WAREHOUSE_VIEW_ALL, name: 'View All Warehouse Data' },

  // Payment Permissions
  { action: PermissionAction.PAYMENT_CREATE, name: 'Create Payments' },
  { action: PermissionAction.PAYMENT_READ, name: 'Read Payments' },
  { action: PermissionAction.PAYMENT_UPDATE_STATUS, name: 'Update Payment Status' },
  { action: PermissionAction.PAYMENT_REFUND, name: 'Process Refunds' },
  { action: PermissionAction.PAYMENT_VIEW_SENSITIVE_DATA, name: 'View Sensitive Payment Data' },
  { action: PermissionAction.PAYMENT_DELETE, name: 'Delete Payments' },

  // Role & Permission Management
  { action: PermissionAction.ROLE_CREATE, name: 'Create Roles' },
  { action: PermissionAction.ROLE_READ, name: 'Read Roles' },
  { action: PermissionAction.ROLE_UPDATE, name: 'Update Roles' },
  { action: PermissionAction.ROLE_DELETE, name: 'Delete Roles' },
  { action: PermissionAction.PERMISSION_ASSIGN, name: 'Assign Permissions' },

  // Analytics & Reports
  { action: PermissionAction.ANALYTICS_VIEW, name: 'View Analytics' },
  { action: PermissionAction.REPORTS_GENERATE, name: 'Generate Reports' },
  { action: PermissionAction.REPORTS_EXPORT, name: 'Export Reports' },

  // System Settings
  { action: PermissionAction.SETTINGS_VIEW, name: 'View Settings' },
  { action: PermissionAction.SETTINGS_UPDATE, name: 'Update Settings' },
];

// Helper function to assign permissions to a role
async function assignPermissionsToRole(
  roleId: number,
  permissions: PermissionAction[],
  permissionIds: { [key: string]: number }
) {
  for (const action of permissions) {
    await prisma.role_permissions.upsert({
      where: {
        role_id_permission_id: {
          role_id: roleId,
          permission_id: permissionIds[action],
        },
      },
      update: {},
      create: {
        role_id: roleId,
        permission_id: permissionIds[action],
      },
    });
  }
}

async function main() {
  console.log('🌱 Seeding database with permissions and roles...\n');

  // 1. Create all permissions
  console.log('📋 Creating permissions...');
  const createdPermissions: { [key: string]: number } = {};

  for (const p of allPermissions) {
    const permission = await prisma.permission.upsert({
      where: { action: p.action },
      update: { name: p.name },
      create: p,
    });
    createdPermissions[p.action] = permission.id;
  }
  console.log(`✅ Created ${allPermissions.length} permissions\n`);

  // 2. Create "Super Admin" role with ALL permissions
  console.log('👑 Creating Super Admin role...');
  const superAdminRole = await prisma.role.upsert({
    where: { name: 'Super Admin' },
    update: {},
    create: {
      name: 'Super Admin',
      description: 'Has complete access to all system features and settings. Can manage everything.',
    },
  });

  // Assign all permissions to Super Admin using explicit join table
  await assignPermissionsToRole(
    superAdminRole.id,
    allPermissions.map(p => p.action),
    createdPermissions
  );
  console.log('✅ Super Admin role created with all permissions\n');

  // 3. Create "Admin" role (most admin features, but not system-critical)
  console.log('🔧 Creating Admin role...');
  const adminRole = await prisma.role.upsert({
    where: { name: 'Admin' },
    update: {},
    create: {
      name: 'Admin',
      description: 'Can manage products, orders, customers, and pricing. Cannot manage roles or system settings.',
    },
  });

  // Assign permissions to Admin role
  const adminPermissions = [
    // Dashboard
    PermissionAction.ACCESS_ADMIN_DASHBOARD,
    PermissionAction.VIEW_DASHBOARD_STATS,
    // Products
    PermissionAction.PRODUCT_CREATE,
    PermissionAction.PRODUCT_READ,
    PermissionAction.PRODUCT_UPDATE,
    PermissionAction.PRODUCT_DELETE,
    PermissionAction.PRODUCT_MANAGE_CATEGORIES,
    PermissionAction.PRODUCT_MANAGE_VARIANTS,
    PermissionAction.PRODUCT_MANAGE_IMAGES,
    // Orders
    PermissionAction.ORDER_READ_ALL,
    PermissionAction.ORDER_UPDATE_STATUS,
    PermissionAction.ORDER_CANCEL,
    PermissionAction.ORDER_REFUND,
    PermissionAction.ORDER_UPDATE_MARKETPLACE_INFO,
    // Customers
    PermissionAction.CUSTOMER_READ,
    PermissionAction.CUSTOMER_UPDATE,
    PermissionAction.CUSTOMER_VIEW_SENSITIVE_DATA,
    // Pricing
    PermissionAction.PRICING_RULE_CREATE,
    PermissionAction.PRICING_RULE_READ,
    PermissionAction.PRICING_RULE_UPDATE,
    PermissionAction.PRICING_RULE_DELETE,
    // Warehouse
    PermissionAction.WAREHOUSE_RECEIPT_CREATE,
    PermissionAction.WAREHOUSE_RECEIPT_READ,
    PermissionAction.WAREHOUSE_RECEIPT_UPDATE,
    PermissionAction.WAREHOUSE_RECEIPT_DELETE,
    PermissionAction.WAREHOUSE_MATCH_PACKAGE,
    PermissionAction.WAREHOUSE_UPDATE_TRACKING,
    PermissionAction.WAREHOUSE_VIEW_ALL,
    // Payments
    PermissionAction.PAYMENT_CREATE,
    PermissionAction.PAYMENT_READ,
    PermissionAction.PAYMENT_UPDATE_STATUS,
    PermissionAction.PAYMENT_REFUND,
    PermissionAction.PAYMENT_VIEW_SENSITIVE_DATA,
    // Analytics
    PermissionAction.ANALYTICS_VIEW,
    PermissionAction.REPORTS_GENERATE,
    PermissionAction.REPORTS_EXPORT,
  ];

  await assignPermissionsToRole(adminRole.id, adminPermissions, createdPermissions);
  console.log('✅ Admin role created\n');

  // 4. Create "Product Manager" role
  console.log('📦 Creating Product Manager role...');
  const productManagerRole = await prisma.role.upsert({
    where: { name: 'Product Manager' },
    update: {},
    create: {
      name: 'Product Manager',
      description: 'Can manage products, categories, variants, and images. Read-only access to orders.',
    },
  });

  await assignPermissionsToRole(productManagerRole.id, [
    PermissionAction.ACCESS_ADMIN_DASHBOARD,
    PermissionAction.VIEW_DASHBOARD_STATS,
    PermissionAction.PRODUCT_CREATE,
    PermissionAction.PRODUCT_READ,
    PermissionAction.PRODUCT_UPDATE,
    PermissionAction.PRODUCT_DELETE,
    PermissionAction.PRODUCT_MANAGE_CATEGORIES,
    PermissionAction.PRODUCT_MANAGE_VARIANTS,
    PermissionAction.PRODUCT_MANAGE_IMAGES,
    PermissionAction.ORDER_READ_ALL,
    PermissionAction.CUSTOMER_READ,
    PermissionAction.ANALYTICS_VIEW,
  ], createdPermissions);
  console.log('✅ Product Manager role created\n');

  // 5. Create "Order Manager" role
  console.log('📋 Creating Order Manager role...');
  const orderManagerRole = await prisma.role.upsert({
    where: { name: 'Order Manager' },
    update: {},
    create: {
      name: 'Order Manager',
      description: 'Can manage orders, update status, process refunds. Read-only access to products and customers.',
    },
  });

  await assignPermissionsToRole(orderManagerRole.id, [
    PermissionAction.ACCESS_ADMIN_DASHBOARD,
    PermissionAction.VIEW_DASHBOARD_STATS,
    PermissionAction.ORDER_READ_ALL,
    PermissionAction.ORDER_UPDATE_STATUS,
    PermissionAction.ORDER_CANCEL,
    PermissionAction.ORDER_REFUND,
    PermissionAction.ORDER_UPDATE_MARKETPLACE_INFO,
    PermissionAction.PRODUCT_READ,
    PermissionAction.CUSTOMER_READ,
    PermissionAction.CUSTOMER_VIEW_SENSITIVE_DATA,
    PermissionAction.WAREHOUSE_RECEIPT_READ,
    PermissionAction.WAREHOUSE_VIEW_ALL,
    PermissionAction.PAYMENT_READ,
    PermissionAction.ANALYTICS_VIEW,
  ], createdPermissions);
  console.log('✅ Order Manager role created\n');

  // 6. Create "Customer Support" role
  console.log('💬 Creating Customer Support role...');
  const customerSupportRole = await prisma.role.upsert({
    where: { name: 'Customer Support' },
    update: {},
    create: {
      name: 'Customer Support',
      description: 'Can view orders and customers, update order status. Limited product access.',
    },
  });

  await assignPermissionsToRole(customerSupportRole.id, [
    PermissionAction.ACCESS_ADMIN_DASHBOARD,
    PermissionAction.ORDER_READ_ALL,
    PermissionAction.ORDER_UPDATE_STATUS,
    PermissionAction.CUSTOMER_READ,
    PermissionAction.CUSTOMER_VIEW_SENSITIVE_DATA,
    PermissionAction.PRODUCT_READ,
    PermissionAction.WAREHOUSE_RECEIPT_READ,
    PermissionAction.PAYMENT_READ,
  ], createdPermissions);
  console.log('✅ Customer Support role created\n');

  // 7. Create "Warehouse Manager" role
  console.log('📦 Creating Warehouse Manager role...');
  const warehouseManagerRole = await prisma.role.upsert({
    where: { name: 'Warehouse Manager' },
    update: {},
    create: {
      name: 'Warehouse Manager',
      description: 'Can manage warehouse operations, create receipts, update tracking. Read-only access to orders.',
    },
  });

  await assignPermissionsToRole(warehouseManagerRole.id, [
    PermissionAction.ACCESS_ADMIN_DASHBOARD,
    PermissionAction.VIEW_DASHBOARD_STATS,
    PermissionAction.WAREHOUSE_RECEIPT_CREATE,
    PermissionAction.WAREHOUSE_RECEIPT_READ,
    PermissionAction.WAREHOUSE_RECEIPT_UPDATE,
    PermissionAction.WAREHOUSE_MATCH_PACKAGE,
    PermissionAction.WAREHOUSE_UPDATE_TRACKING,
    PermissionAction.WAREHOUSE_VIEW_ALL,
    PermissionAction.ORDER_READ_ALL,
    PermissionAction.ORDER_UPDATE_STATUS,
    PermissionAction.PRODUCT_READ,
    PermissionAction.CUSTOMER_READ,
  ], createdPermissions);
  console.log('✅ Warehouse Manager role created\n');

  // 8. Create "Pricing Manager" role
  console.log('💰 Creating Pricing Manager role...');
  const pricingManagerRole = await prisma.role.upsert({
    where: { name: 'Pricing Manager' },
    update: {},
    create: {
      name: 'Pricing Manager',
      description: 'Can manage pricing rules and view products. Read-only access to orders.',
    },
  });

  await assignPermissionsToRole(pricingManagerRole.id, [
    PermissionAction.ACCESS_ADMIN_DASHBOARD,
    PermissionAction.VIEW_DASHBOARD_STATS,
    PermissionAction.PRICING_RULE_CREATE,
    PermissionAction.PRICING_RULE_READ,
    PermissionAction.PRICING_RULE_UPDATE,
    PermissionAction.PRICING_RULE_DELETE,
    PermissionAction.PRODUCT_READ,
    PermissionAction.ORDER_READ_ALL,
    PermissionAction.ANALYTICS_VIEW,
  ], createdPermissions);
  console.log('✅ Pricing Manager role created\n');

  // 8. Create "Analyst" role
  console.log('📊 Creating Analyst role...');
  const analystRole = await prisma.role.upsert({
    where: { name: 'Analyst' },
    update: {},
    create: {
      name: 'Analyst',
      description: 'Can view analytics, generate reports. Read-only access to all data.',
    },
  });

  await assignPermissionsToRole(analystRole.id, [
    PermissionAction.ACCESS_ADMIN_DASHBOARD,
    PermissionAction.VIEW_DASHBOARD_STATS,
    PermissionAction.PRODUCT_READ,
    PermissionAction.ORDER_READ_ALL,
    PermissionAction.CUSTOMER_READ,
    PermissionAction.PRICING_RULE_READ,
    PermissionAction.ANALYTICS_VIEW,
    PermissionAction.REPORTS_GENERATE,
    PermissionAction.REPORTS_EXPORT,
  ], createdPermissions);
  console.log('✅ Analyst role created\n');

  // 9. Create "Viewer" role (read-only)
  console.log('👁️  Creating Viewer role...');
  const viewerRole = await prisma.role.upsert({
    where: { name: 'Viewer' },
    update: {},
    create: {
      name: 'Viewer',
      description: 'Read-only access to admin dashboard. Cannot modify anything.',
    },
  });

  await assignPermissionsToRole(viewerRole.id, [
    PermissionAction.ACCESS_ADMIN_DASHBOARD,
    PermissionAction.VIEW_DASHBOARD_STATS,
    PermissionAction.PRODUCT_READ,
    PermissionAction.ORDER_READ_ALL,
    PermissionAction.CUSTOMER_READ,
    PermissionAction.PRICING_RULE_READ,
  ], createdPermissions);
  console.log('✅ Viewer role created\n');

  console.log('🎉 Seeding completed successfully!');
  console.log('📊 Summary:');
  console.log(`   - ${allPermissions.length} permissions created`);
  console.log('   - 9 roles created:');
  console.log('     1. Super Admin (all permissions)');
  console.log('     2. Admin (most features)');
  console.log('     3. Product Manager');
  console.log('     4. Order Manager');
  console.log('     5. Customer Support');
  console.log('     6. Pricing Manager');
  console.log('     7. Analyst');
  console.log('     8. Viewer (read-only)');
  console.log('\n✨ Ready to assign roles to users!\n');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });