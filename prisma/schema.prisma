generator client {
  provider = "prisma-client-js"
  output   = "../app/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// -------------------
// ENUMS
// -------------------

enum UserRole {
  USER
  ADMIN
}

enum Marketplace {
  taobao
  pinduoduo
  alibaba
}

enum ProductImageType {
  preview
  description
  video
}

enum OrderStatus {
  unpaid
  paid
  processing
  shipped
  delivered
  cancelled
  refunded
}

enum WarehouseReceiptStatus {
  pending
  matched
  shipped
}

enum PaymentStatus {
  pending
  succeeded
  failed
  refunded
}

enum ShippingMethod {
  boat
  plane
}

enum UserActivityType {
  view
  cart_add
  purchase
}

enum SimilarityType {
  attributes
  purchased_together
  marketplace_trend
}

enum PermissionAction {
  // Admin Dashboard Access
  ACCESS_ADMIN_DASHBOARD
  VIEW_DASHBOARD_STATS

  // Product Permissions
  PRODUCT_CREATE
  PRODUCT_READ
  PRODUCT_UPDATE
  PRODUCT_DELETE
  PRODUCT_MANAGE_CATEGORIES
  PRODUCT_MANAGE_VARIANTS
  PRODUCT_MANAGE_IMAGES

  // Order Permissions
  ORDER_READ_ALL
  ORDER_READ_OWN
  ORDER_UPDATE_STATUS
  ORDER_CANCEL
  ORDER_REFUND
  ORDER_DELETE
  ORDER_UPDATE_MARKETPLACE_INFO

  // Customer Permissions
  CUSTOMER_READ
  CUSTOMER_UPDATE
  CUSTOMER_DELETE
  CUSTOMER_ASSIGN_ROLE
  CUSTOMER_REMOVE_ROLE
  CUSTOMER_VIEW_SENSITIVE_DATA

  // Pricing Rule Permissions
  PRICING_RULE_CREATE
  PRICING_RULE_READ
  PRICING_RULE_UPDATE
  PRICING_RULE_DELETE

  // Warehouse Permissions
  WAREHOUSE_RECEIPT_CREATE
  WAREHOUSE_RECEIPT_READ
  WAREHOUSE_RECEIPT_UPDATE
  WAREHOUSE_RECEIPT_DELETE
  WAREHOUSE_MATCH_PACKAGE
  WAREHOUSE_UPDATE_TRACKING
  WAREHOUSE_VIEW_ALL

  // Payment Permissions
  PAYMENT_CREATE
  PAYMENT_READ
  PAYMENT_UPDATE_STATUS
  PAYMENT_REFUND
  PAYMENT_VIEW_SENSITIVE_DATA
  PAYMENT_DELETE

  // Role & Permission Management
  ROLE_CREATE
  ROLE_READ
  ROLE_UPDATE
  ROLE_DELETE
  PERMISSION_ASSIGN

  // Analytics & Reports
  ANALYTICS_VIEW
  REPORTS_GENERATE
  REPORTS_EXPORT

  // System Settings
  SETTINGS_VIEW
  SETTINGS_UPDATE
}

// Exchange rates cache - stores rates fetched once per day
model exchange_rates_cache {
  id                 Int      @id @default(autoincrement())
  base_currency      String   @db.VarChar(3) // e.g., 'CNY'
  target_currency    String   @db.VarChar(3) // e.g., 'XAF', 'USD'
  rate               Decimal  @db.Decimal(15, 8) // Exchange rate with high precision
  fetched_at         DateTime @default(now()) @db.Timestamptz() // When this rate was fetched
  expires_at         DateTime @db.Timestamptz() // When this rate expires (24 hours later)

  @@unique([base_currency, target_currency])
  @@index([expires_at])
}

// -------------------
// MODELS
// -------------------
// -------------------
// MODELS
// -------------------

// 1. CUSTOMERS (Global buyers)
model customers {
  id                 Int      @id @default(autoincrement())
  firebase_uid       String   @unique
  email              String   @unique
  full_name          String
  phone              String?
  addresses          Json?
  preferred_currency String?  @db.VarChar(3)
  created            DateTime @default(now()) @db.Timestamptz()
  updated            DateTime @updatedAt @db.Timestamptz()

  // --- RBAC RELATIONSHIP ---
  roles customer_roles[]

  // --- Other Relationships ---
  orders       orders[]
  user_activity user_activity[]
  wishlist_items wishlist_items[]
}

// --- NEW RBAC MODELS ---

model Role {
  id          Int      @id @default(autoincrement())
  name        String   @unique // e.g., "Super Admin", "Order Manager", "Product Manager"
  description String?
  created     DateTime @default(now()) @db.Timestamptz()
  updated     DateTime @updatedAt @db.Timestamptz()

  permissions role_permissions[]
  customers   customer_roles[]
}

model Permission {
  id     Int              @id @default(autoincrement())
  action PermissionAction @unique // The specific permission, e.g., PRODUCT_UPDATE
  name   String // A user-friendly name, e.g., "Can update products"

  roles role_permissions[]
}

// EXPLICIT JOIN TABLE for Roles and Permissions
model role_permissions {
  role_id       Int
  permission_id Int

  role       Role       @relation(fields: [role_id], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permission_id], references: [id], onDelete: Cascade)

  @@id([role_id, permission_id])
  @@index([permission_id])
}

// EXPLICIT JOIN TABLE for Customers and Roles
model customer_roles {
  customer_id Int
  role_id     Int

  customer customers @relation(fields: [customer_id], references: [id], onDelete: Cascade)
  role     Role      @relation(fields: [role_id], references: [id], onDelete: Cascade)

  @@id([customer_id, role_id])
  @@index([role_id])
}

// 2. PRODUCTS (Your catalog)
model products {
  id            Int      @id @default(autoincrement())
  original_name          String?
  product_url   String   @unique
  marketplace   Marketplace @default(alibaba)
  can_show      Boolean? @default(false)
  featured      Boolean? @default(false)
  created       DateTime @default(now()) @db.Timestamptz()
  updated       DateTime @updatedAt @db.Timestamptz()
  weight        Decimal?
  weight_unit   String?  @db.VarChar(10)
  min_price_cny  Decimal? @db.Decimal(12, 2) // Minimum price in CNY for sorting

  offers             offers[]
  variants           variants[]
  product_attributes product_attributes[]
  product_images     product_images[]
  custom_services    custom_services[]
  order_items        order_items[]
  user_activity      user_activity[]
  similar_products_1 product_similarity[] @relation("product1")
  similar_products_2 product_similarity[] @relation("product2")
  translations product_translations[]
  categories   product_categories[]
  wishlist_items wishlist_items[]

  @@index([marketplace])
  @@index([can_show, created, id]) // Optimized for product listing with cursor pagination
  @@index([can_show, marketplace]) // For marketplace filtering
  @@index([can_show, min_price_cny, id]) // For price sorting with cursor pagination
}

model product_translations {
  id          Int   @id @default(autoincrement())
  product_id  Int
  language_code String   @db.VarChar(5) // e.g., "en", "fr-FR", "ar"

  name        String
  slug        String
  // You can add other translatable fields here, like 'materials', etc.

  product products @relation(fields: [product_id], references: [id], onDelete: Cascade)

  // A product can only have one entry per language
  @@unique([product_id, language_code])
  @@unique([language_code, slug]) // A slug must be unique for a given language
  @@index([language_code])
  @@index([language_code, name]) // For search functionality
}

// -------------------
// NEW CATEGORY MODELS
// -------------------

model categories {
  id          Int      @id @default(autoincrement())
  parent_id   Int?     // For creating parent-child hierarchies (subcategories)
  created     DateTime @default(now()) @db.Timestamptz()
  updated     DateTime @updatedAt @db.Timestamptz()

  // --- Relationships ---
  parent      categories?  @relation("Subcategories", fields: [parent_id], references: [id], onDelete: SetNull)
  children    categories[] @relation("Subcategories")
  translations category_translations[]
  products    product_categories[]   // The link to the products table

  @@index([parent_id])
}

model category_translations {
  id            Int   @id @default(autoincrement())
  category_id   Int
  language_code String   @db.VarChar(5) // e.g., "en", "es", "ar"
  name          String   // "Electronics", "Electrónica", etc.
  slug          String   // "electronics", "electronica", etc.
  description   String?

  category categories @relation(fields: [category_id], references: [id], onDelete: Cascade)

  @@unique([category_id, language_code])
  @@unique([language_code, slug])
}

// This is the explicit join table for the many-to-many relationship
model product_categories {
  product_id  Int
  category_id Int

  // Define the relationships to the other models
  product    products   @relation(fields: [product_id], references: [id], onDelete: Cascade)
  category   categories @relation(fields: [category_id], references: [id], onDelete: Cascade)

  // Create a composite primary key to ensure each product-category pair is unique
  @@id([product_id, category_id])

  // Add an index to the category_id for faster lookups
  @@index([category_id])
}

// 3. OFFERS (Pricing from marketplace)
model offers {
  id            Int    @id @default(autoincrement())
  product_id    Int
  min_quantity  Decimal
  price_low     Decimal
  price_high    Decimal?
  currency      String    @default("CNY") @db.VarChar(10)
  quantity_info String?

  product products @relation(fields: [product_id], references: [id], onDelete: Cascade)

  @@index([product_id])
  @@index([price_low])
  @@index([product_id, price_low]) // Optimized for getting lowest price per product
  @@index([price_low, id]) // For cursor pagination price sorting
}

// 4. VARIANTS (Product options)
model variants {
  id                 Int   @id @default(autoincrement())
  product_id         Int
  original_variant_name       String
  original_variant_type       String
  available_quantity Decimal?
  min_quantity       Decimal?
  price_low          Decimal
  price_high         Decimal?
  currency           String   @default("CNY") @db.VarChar(10)

  product     products      @relation(fields: [product_id], references: [id], onDelete: Cascade)
  order_items order_items[]
  translations variant_translations[]
  wishlist_items wishlist_items[]

  @@index([product_id])
  @@index([original_variant_type])
}

model variant_translations {
  id             Int   @id @default(autoincrement())
  variant_id     Int
  language_code  String   @db.VarChar(5)
  variant_name   String
  variant_type   String

  variant variants @relation(fields: [variant_id], references: [id], onDelete: Cascade)

  @@unique([variant_id, language_code])
}

// 5. PRODUCT ATTRIBUTES (For filtering)
model product_attributes {
  id         Int @id @default(autoincrement())
  product_id Int
  original_attr_key   String
  original_attr_value String

  product products @relation(fields: [product_id], references: [id], onDelete: Cascade)
  translations product_attributes_translations[]

  @@index([original_attr_key, original_attr_value])
}

model product_attributes_translations {
  id             Int   @id @default(autoincrement())
  attributes_id   Int
  language_code  String   @db.VarChar(5)
  attr_key   String
  attr_value   String

  attributes product_attributes @relation(fields: [attributes_id], references: [id], onDelete: Cascade)

  @@unique([attributes_id, language_code])
}

// 6. PRODUCT MEDIA
model product_images {
  id         Int           @id @default(autoincrement())
  product_id Int
  image_url  String
  image_type ProductImageType

  product products @relation(fields: [product_id], references: [id], onDelete: Cascade)

  @@index([product_id, image_type])
}

// 4. Custom Services
model custom_services {
  id           Int   @id @default(autoincrement())
  product_id   Int
  original_service_name String?
  original_service_type String?
  min_quantity Decimal?
  price        Decimal?
  currency     String?  @db.VarChar(10)

  product products @relation(fields: [product_id], references: [id], onDelete: Cascade)
  translations custom_services_translations[]

  @@index([product_id])
}

model custom_services_translations {
  id             Int   @id @default(autoincrement())
  custom_services_id   Int
  language_code  String   @db.VarChar(5)
  service_name   String
  service_type   String

  custom_services custom_services @relation(fields: [custom_services_id], references: [id], onDelete: Cascade)

  @@unique([custom_services_id, language_code])
}

// 7. ORDERS (Customer-facing)
model orders {
  id               String         @id @default(uuid())
  customer_id      Int
  shipping_address Json
  shipping_method  ShippingMethod @default(boat)
  status           OrderStatus    @default(unpaid)
  total_amount     Decimal        @db.Decimal(12, 2)
  currency         String         @db.VarChar(3)
  exchange_rate    Decimal        @db.Decimal(10, 6)
  shipping_cost    Decimal        @db.Decimal(12, 2)
  created          DateTime       @default(now()) @db.Timestamptz()
  updated          DateTime       @updatedAt @db.Timestamptz()

  customer    customers     @relation(fields: [customer_id], references: [id], onDelete: Cascade)
  order_items order_items[]
  payments    payments[]

  @@index([status])
  @@index([customer_id])
}

// 8. ORDER ITEMS
model order_items {
  id                      Int    @id @default(autoincrement())
  order_id                String
  product_id              Int
  variant_id              Int?
  quantity                Decimal
  price_per_unit          Decimal   @db.Decimal(12, 2)
  image_url               String?
  marketplace_order_id    String?
  marketplace_product_url String
  marketplace_notes       String

  order            orders             @relation(fields: [order_id], references: [id], onDelete: Cascade)
  product          products           @relation(fields: [product_id], references: [id], onDelete: Restrict)
  variant          variants?          @relation(fields: [variant_id], references: [id], onDelete: SetNull)
  warehouse_receipts warehouse_receipts[]

  @@index([marketplace_order_id])
  @@index([product_id])
}

// 9. WAREHOUSE RECEIPTS
model warehouse_receipts {
  id                   String               @id @default(uuid())
  order_item_id        Int
  received_at          DateTime             @default(now()) @db.Timestamptz()
  marketplace_order_id String
  package_weight       Decimal
  package_weight_unit  String               @default("g") @db.VarChar(10)
  status               WarehouseReceiptStatus @default(pending)
  shipping_label_url   String?
  tracking_number      String?
  carrier              String?              @db.VarChar(50)

  order_item order_items @relation(fields: [order_item_id], references: [id], onDelete: Cascade)

  @@index([marketplace_order_id])
  @@index([status])
}

// 10. WISHLIST ITEMS
model wishlist_items {
  id                 Int   @id @default(autoincrement())
  customer_id        Int
  product_id         Int
  variant_id         Int?
  price_when_added   Decimal  @db.Decimal(12, 2)
  currency_when_added String  @db.VarChar(3)
  notes              String?
  created_at         DateTime @default(now()) @db.Timestamptz()
  updated_at         DateTime @updatedAt @db.Timestamptz()

  customer customers @relation(fields: [customer_id], references: [id], onDelete: Cascade)
  product  products  @relation(fields: [product_id], references: [id], onDelete: Cascade)
  variant  variants? @relation(fields: [variant_id], references: [id], onDelete: SetNull)

  @@unique([customer_id, product_id, variant_id])
  @@index([customer_id])
  @@index([product_id])
}

// 10. PAYMENTS
model payments {
  id             String        @id @default(uuid())
  order_id       String
  amount         Decimal       @db.Decimal(12, 2)
  currency       String        @db.VarChar(3)
  payment_method String        @db.VarChar(50)
  transaction_id String        @unique
  status         PaymentStatus
  created        DateTime      @default(now()) @db.Timestamptz()

  order orders @relation(fields: [order_id], references: [id], onDelete: Cascade)

  @@index([status])
  @@index([order_id])
}

// 1. User Activity Tracking
model user_activity {
  id            Int           @id @default(autoincrement())
  customer_id   Int
  product_id    Int
  activity_type UserActivityType
  created       DateTime         @default(now()) @db.Timestamptz()
  session_id    String
  marketplace   String           @db.VarChar(20)

  customer customers @relation(fields: [customer_id], references: [id], onDelete: Cascade)
  product  products  @relation(fields: [product_id], references: [id], onDelete: Cascade)

  @@index([customer_id])
  @@index([product_id])
  @@index([created])
}

// 2. Product Similarity
model product_similarity {
  product_id1     Int
  product_id2     Int
  similarity_score Decimal @db.Decimal(4, 3)
  similarity_type SimilarityType

  product1 products @relation("product1", fields: [product_id1], references: [id], onDelete: Cascade)
  product2 products @relation("product2", fields: [product_id2], references: [id], onDelete: Cascade)

  @@id([product_id1, product_id2, similarity_type])
  @@index([product_id1])
}

// Pricing Rules Table
model pricing_rules {
  id              Int      @id @default(autoincrement())
  rule_name       String
  priority        Int      @default(0) // Higher number = higher priority
  // Condition: What does this rule apply to?
  // e.g., 'GLOBAL', 'CATEGORY', 'PRODUCT_ID', 'MARKETPLACE'
  condition_type  String   @db.VarChar(50)
  condition_value String? // e.g., NULL for GLOBAL, 'electronics' for CATEGORY, '12345' for PRODUCT_ID
  // Action: What markup to apply?
  // e.g., 'PERCENTAGE', 'FIXED_AMOUNT_ADD'
  markup_type     String   @db.VarChar(50)
  markup_value    Decimal  @db.Decimal(10, 4)
  is_active       Boolean? @default(true)
  created_at      DateTime @default(now()) @db.Timestamptz()

  @@unique([condition_type, condition_value])
  @@index([condition_type, condition_value, is_active])
  @@index([is_active, priority]) // Optimized for rule fetching with priority ordering
}