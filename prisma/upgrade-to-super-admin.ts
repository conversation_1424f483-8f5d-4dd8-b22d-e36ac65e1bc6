// prisma/upgrade-to-super-admin.ts
// <PERSON><PERSON>t to assign Super Admin role to a user account

import { PrismaClient } from '../app/generated/prisma/index.js';

const prisma = new PrismaClient();

async function main() {
  // Get email from command line argument
  const email = process.argv[2];

  if (!email) {
    console.error('❌ Error: Please provide an email address as an argument');
    console.log('Usage: npx tsx prisma/upgrade-to-super-admin.ts <EMAIL>');
    process.exit(1);
  }

  console.log(`🔍 Looking up customer with email: ${email}\n`);

  try {
    // 1. Find the customer by email
    const customer = await prisma.customers.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        full_name: true,
        roles: {
          select: {
            role: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });

    if (!customer) {
      console.error(`❌ Error: No customer found with email: ${email}`);
      console.log('Make sure the email is correct and the user has registered.');
      process.exit(1);
    }

    console.log(`✅ Found customer: ${customer.full_name} (${customer.email})`);
    console.log(`   Current roles: ${customer.roles.map(cr => cr.role.name).join(', ') || 'None'}\n`);

    // 2. Find the Super Admin role
    const superAdminRole = await prisma.role.findUnique({
      where: { name: 'Super Admin' },
      select: { id: true, name: true, description: true },
    });

    if (!superAdminRole) {
      console.error('❌ Error: Super Admin role not found. Please run the seed script first.');
      process.exit(1);
    }

    console.log(`✅ Found Super Admin role: ${superAdminRole.name}`);
    console.log(`   Description: ${superAdminRole.description}\n`);

    // 3. Check if role is already assigned
    const existingAssignment = await prisma.customer_roles.findUnique({
      where: {
        customer_id_role_id: {
          customer_id: customer.id,
          role_id: superAdminRole.id,
        },
      },
    });

    if (existingAssignment) {
      console.log('⚠️  Super Admin role is already assigned to this user.');
      process.exit(0);
    }

    // 4. Assign the Super Admin role
    console.log('👑 Assigning Super Admin role...');
    await prisma.customer_roles.create({
      data: {
        customer_id: customer.id,
        role_id: superAdminRole.id,
      },
    });

    console.log('\n🎉 Success! User has been upgraded to Super Admin.');
    console.log('The user now has complete access to all system features and settings.');
    console.log('\nNext steps:');
    console.log('1. Log in with the user account');
    console.log('2. Navigate to /admin to access the admin dashboard');
    console.log('3. You can now manage roles, permissions, and all system settings');

  } catch (error) {
    console.error('❌ Error during upgrade:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();