// middleware.ts
// Unified middleware for locale handling and route protection

// Use Edge runtime for better performance

import createMiddleware from 'next-intl/middleware';
import { NextRequest, NextResponse } from 'next/server';
import { getFirebaseAuth } from 'next-firebase-auth-edge/lib/auth';
import { AUTH_ROUTES, PROTECTED_ROUTES, ID_TOKEN_COOKIE_NAME, COUNTRY_CURRENCY_MAP, DEFAULT_CURRENCY } from '@/lib/constants';
import { routing } from './i18n/routing';

// Initialize Firebase Auth for edge runtime
const serviceAccount = {
  projectId: process.env.FIREBASE_PROJECT_ID!,
  privateKey: process.env.FIREBASE_PRIVATE_KEY!.replace(/\\n/g, '\n'),
  clientEmail: process.env.FIREBASE_CLIENT_EMAIL!,
};

const auth = getFirebaseAuth({
  serviceAccount,
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY!,
});

// Locale configuration
const intlMiddleware = createMiddleware(routing);

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip middleware for static files and API routes
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    pathname.includes('/favicon.ico') ||
    pathname.match(/\.(svg|png|jpg|jpeg|gif|webp|ico)$/)
  ) {
    return NextResponse.next();
  }

  // Apply internationalization first
  const response = intlMiddleware(request);

  // Extract locale from pathname (e.g., /en/products -> en)
  const localeMatch = pathname.match(/^\/([a-z]{2})(\/|$)/);
  const locale = localeMatch ? localeMatch[1] : 'en';

  // Get the path without locale (e.g., /en/account -> /account)
  const pathWithoutLocale = localeMatch ? pathname.slice(3) : pathname;

  // Currency detection logic - only set header for currency, don't make API calls here
  let detectedCurrency: string = DEFAULT_CURRENCY;

  // 1. Check for currency cookie (user preference)
  const currencyCookie = request.cookies.get('preferred_currency')?.value;
  if (currencyCookie && currencyCookie.length === 3) {
    detectedCurrency = currencyCookie.toUpperCase();
  }
  // Removed geolocation API call from middleware to prevent duplicate requests
  // Geolocation detection now handled client-side in CurrencyProvider

  // Set currency in response headers for server components
  response.headers.set('x-user-currency', detectedCurrency);

  // Check if path is protected
  const isProtectedRoute = PROTECTED_ROUTES.some(route =>
    pathWithoutLocale.startsWith(route)
  );

  // Check if path is an auth route (login/register)
  const isAuthRoute = AUTH_ROUTES.some(route =>
    pathWithoutLocale === route
  );

  // Get ID token from cookie
  const idToken = request.cookies.get(ID_TOKEN_COOKIE_NAME)?.value;

  // Verify ID token if cookie exists
  let isAuthenticated = false;
  let firebaseUid: string | null = null;

  if (idToken) {
    try {
      const decodedClaims = await auth.verifyIdToken(idToken);
      isAuthenticated = true;
      firebaseUid = decodedClaims.uid;
    } catch {
      // Token invalid or expired
      isAuthenticated = false;
    }
  }

  // Check if path is admin route
  const isAdminRoute = pathWithoutLocale.startsWith('/admin');

  // Admin route protection - require authentication
  // Note: Detailed permission checks (ACCESS_ADMIN_DASHBOARD) are done in the admin layout
  // because Edge runtime doesn't support Prisma database queries
  if (isAdminRoute) {
    if (!isAuthenticated || !firebaseUid) {
      // Not authenticated, redirect to login
      const loginUrl = new URL(`/${locale}/login`, request.url);
      loginUrl.searchParams.set('redirect', pathWithoutLocale);
      return NextResponse.redirect(loginUrl);
    }
    // User is authenticated - let them through to admin routes
    // The admin layout will perform the actual permission check
  }

  // Redirect logic for other protected routes
  if (isProtectedRoute && !isAuthenticated) {
    // Redirect to login if trying to access protected route without auth
    const loginUrl = new URL(`/${locale}/login`, request.url);
    loginUrl.searchParams.set('redirect', pathWithoutLocale);
    return NextResponse.redirect(loginUrl);
  }

  if (isAuthRoute && isAuthenticated) {
    // Redirect to account if trying to access login/register while authenticated
    return NextResponse.redirect(new URL(`/${locale}/account`, request.url));
  }

  return response;
}

export const config = {
  matcher: [
    // Match all pathnames except for
    // - … if they start with `/api`, `/_next` or `/_vercel`
    // - … the ones containing a dot (e.g. `favicon.ico`)
    '/((?!api|_next|_vercel|.*\\..*).*)',
  ],
};