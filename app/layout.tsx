// app/layout.tsx
// Root layout - renders <html> and <body> tags

import type { Metadata } from 'next';
// import { <PERSON><PERSON><PERSON>, <PERSON>eist_Mono } from 'next/font/google';
import './globals.css';
import { SITE_CONFIG } from '@/lib/constants';

// const geistSans = Geist({
//   variable: '--font-geist-sans',
//   subsets: ['latin'],
// });

// const geistMono = Geist_Mono({
//   variable: '--font-geist-mono',
//   subsets: ['latin'],
// });

export const metadata: Metadata = {
  title: {
    default: SITE_CONFIG.name,
    template: `%s | ${SITE_CONFIG.name}`,
  },
  description: SITE_CONFIG.description,
  keywords: ['Chinese marketplace', 'Taobao', 'Pinduoduo', 'Alibaba', 'e-commerce', 'shopping', 'African marketplace', 'shopping in Africa', 'products for Africa', 'African e-commerce', 'import from China to Africa'],
  authors: [{ name: SITE_CONFIG.name }],
  creator: SITE_CONFIG.name,
  metadataBase: new URL(SITE_CONFIG.url),
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: SITE_CONFIG.url,
    title: SITE_CONFIG.name,
    description: SITE_CONFIG.description,
    siteName: SITE_CONFIG.name,
  },
  twitter: {
    card: 'summary_large_image',
    title: SITE_CONFIG.name,
    description: SITE_CONFIG.description,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return children;
}
