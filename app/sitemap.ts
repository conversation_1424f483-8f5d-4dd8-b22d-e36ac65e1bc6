// app/sitemap.ts
// Dynamic sitemap generation for SEO

import { MetadataRoute } from 'next';
import { prisma } from '@/lib/prisma';
import { LOCALES, SITE_CONFIG } from '@/lib/constants';

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = SITE_CONFIG.url;

  // Get all products with translations
  const products = await prisma.products.findMany({
    where: { can_show: true },
    include: {
      translations: true,
    },
    orderBy: { updated: 'desc' },
  });

  // Get all categories with translations
  const categories = await prisma.categories.findMany({
    include: {
      translations: true,
      _count: {
        select: { products: true },
      },
    },
  });

  const sitemapEntries: MetadataRoute.Sitemap = [];

  // Add homepage for each locale
  LOCALES.forEach((locale) => {
    sitemapEntries.push({
      url: `${baseUrl}/${locale}`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 1.0,
    });
  });

  // Add product pages for each locale
  LOCALES.forEach((locale) => {
    products.forEach((product) => {
      const translation = product.translations.find(t => t.language_code === locale);
      if (translation?.slug) {
        sitemapEntries.push({
          url: `${baseUrl}/${locale}/products/${translation.slug}`,
          lastModified: product.updated,
          changeFrequency: 'weekly',
          priority: 0.8,
        });
      }
    });
  });

  // Add category pages for each locale
  LOCALES.forEach((locale) => {
    categories.forEach((category) => {
      const translation = category.translations.find(t => t.language_code === locale);
      if (translation?.slug) {
        sitemapEntries.push({
          url: `${baseUrl}/${locale}/products?category=${category.id}`,
          lastModified: category.updated,
          changeFrequency: 'weekly',
          priority: 0.6,
        });
      }
    });
  });

  // Add static pages
  const staticPages = [
    'products',
    'cart',
    'login',
    'register',
    'account',
    'account/profile',
    'account/orders',
    'checkout/shipping',
    'checkout/payment',
  ];

  LOCALES.forEach((locale) => {
    staticPages.forEach((page) => {
      sitemapEntries.push({
        url: `${baseUrl}/${locale}/${page}`,
        lastModified: new Date(),
        changeFrequency: 'monthly',
        priority: 0.5,
      });
    });
  });

  return sitemapEntries;
}