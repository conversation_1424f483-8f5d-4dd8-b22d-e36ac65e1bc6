import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/actions/auth.actions';
import { prisma } from '@/lib/prisma';

export async function PATCH(request: NextRequest) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const body = await request.json();
    const { currency } = body;

    if (!currency || typeof currency !== 'string' || currency.length !== 3) {
      return NextResponse.json({ error: 'Invalid currency' }, { status: 400 });
    }

    // Update user's preferred currency
    await prisma.customers.update({
      where: { id: user.customerId },
      data: { preferred_currency: currency },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error updating currency:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}