// app/api/user/wishlist/status/[productId]/route.ts
// API endpoint for user-specific wishlist status

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/actions/auth.actions';
import { checkWishlistStatus } from '@/lib/actions/wishlist.actions';

interface RouteParams {
  params: Promise<{ productId: string }>;
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { productId } = await params;
    const productIdNum = parseInt(productId);

    if (isNaN(productIdNum)) {
      return NextResponse.json({ error: 'Invalid product ID' }, { status: 400 });
    }

    // Get current user
    const user = await getCurrentUser();
    
    if (!user) {
      // Return not wishlisted for unauthenticated users
      return NextResponse.json({
        productId: productIdNum,
        isWishlisted: false,
        requiresAuth: true,
      });
    }

    // Check wishlist status
    const isWishlisted = await checkWishlistStatus(user.uid, productIdNum);

    return NextResponse.json({
      productId: productIdNum,
      isWishlisted,
      requiresAuth: false,
    });

  } catch (error) {
    console.error('Error checking wishlist status:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const { productId } = await params;
    const productIdNum = parseInt(productId);

    if (isNaN(productIdNum)) {
      return NextResponse.json({ error: 'Invalid product ID' }, { status: 400 });
    }

    // Get current user
    const user = await getCurrentUser();
    
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const { action } = await request.json();

    if (action !== 'add' && action !== 'remove') {
      return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }

    // Import wishlist actions dynamically to avoid circular dependencies
    const { addToWishlist, removeFromWishlist } = await import('@/lib/actions/wishlist.actions');

    let result;
    if (action === 'add') {
      result = await addToWishlist(user.uid, productIdNum);
    } else {
      result = await removeFromWishlist(user.uid, productIdNum);
    }

    if (!result.success) {
      return NextResponse.json({ error: result.error }, { status: 400 });
    }

    return NextResponse.json({
      productId: productIdNum,
      isWishlisted: action === 'add',
      success: true,
    });

  } catch (error) {
    console.error('Error updating wishlist:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
