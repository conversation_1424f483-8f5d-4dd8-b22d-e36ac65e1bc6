// app/api/analytics/web-vitals/route.ts
// API endpoint for receiving Web Vitals metrics from clients

import { NextRequest, NextResponse } from 'next/server';

/**
 * POST /api/analytics/web-vitals
 * Receives Web Vitals metrics from clients
 */
export async function POST(request: NextRequest) {
  try {
    const metric = await request.json();

    // Log metrics in development
    if (process.env.NODE_ENV === 'development') {
      console.log('📊 Web Vital:', {
        name: metric.name,
        value: metric.value,
        rating: metric.rating,
        delta: metric.delta,
        id: metric.id,
      });
    }

    // In production, you would send this to your analytics service
    // Examples:
    // - Google Analytics
    // - Vercel Analytics
    // - Datadog
    // - New Relic
    // - Custom analytics database

    // For now, just log and return success
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error processing Web Vitals:', error);
    return NextResponse.json(
      { error: 'Failed to process Web Vitals' },
      { status: 500 }
    );
  }
}

