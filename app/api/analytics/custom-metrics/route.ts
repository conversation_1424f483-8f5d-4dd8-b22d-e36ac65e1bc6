// app/api/analytics/custom-metrics/route.ts
// API endpoint for receiving custom application metrics from clients

import { NextRequest, NextResponse } from 'next/server';

interface CustomMetric {
  name: string;
  value: number;
  timestamp: number;
  metadata?: Record<string, unknown>;
}

/**
 * POST /api/analytics/custom-metrics
 * Receives custom application metrics from clients
 */
export async function POST(request: NextRequest) {
  try {
    const metric: CustomMetric = await request.json();

    // Log metrics in development
    if (process.env.NODE_ENV === 'development') {
      console.log('📈 Custom Metric:', {
        name: metric.name,
        value: `${metric.value.toFixed(2)}ms`,
        timestamp: new Date(metric.timestamp).toISOString(),
        metadata: metric.metadata,
      });
    }

    // In production, you would send this to your analytics service
    // Examples:
    // - Custom database
    // - Time-series database (InfluxDB, Prometheus)
    // - Analytics service (Datadog, New Relic)
    // - Cloud logging (CloudWatch, Stackdriver)

    // For now, just log and return success
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error processing custom metric:', error);
    return NextResponse.json(
      { error: 'Failed to process custom metric' },
      { status: 500 }
    );
  }
}

