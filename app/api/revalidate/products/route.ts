// app/api/revalidate/products/route.ts
// API endpoint for on-demand revalidation of product pages

import { NextRequest, NextResponse } from 'next/server';
import { revalidatePath, revalidateTag } from 'next/cache';
import { getCurrentUser } from '@/lib/actions/auth.actions';
import { checkPermission } from '@/lib/auth/permissions';
import { PermissionAction } from '@/app/generated/prisma';
import { prisma } from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    // Verify authentication and permissions
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const hasPermission = await checkPermission(user.uid, PermissionAction.PRODUCT_UPDATE);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { productId, slug, action = 'update' } = await request.json();

    if (!productId && !slug) {
      return NextResponse.json({ error: 'Product ID or slug required' }, { status: 400 });
    }

    const revalidatedPaths: string[] = [];

    if (slug) {
      // Revalidate specific product page for all locales
      const { LOCALES } = await import('@/lib/constants');
      
      for (const locale of LOCALES) {
        const path = `/${locale}/products/${slug}`;
        revalidatePath(path);
        revalidatedPaths.push(path);
      }
    } else if (productId) {
      // Get product slug from database and revalidate
      const product = await prisma.products.findUnique({
        where: { id: parseInt(productId) },
        include: {
          translations: {
            select: {
              slug: true,
              language_code: true,
            },
          },
        },
      });

      if (product) {
        for (const translation of product.translations) {
          const path = `/${translation.language_code}/products/${translation.slug}`;
          revalidatePath(path);
          revalidatedPaths.push(path);
        }
      }
    }

    // Also revalidate related pages
    if (action === 'update' || action === 'create') {
      // Revalidate product listing pages
      const { LOCALES } = await import('@/lib/constants');
      for (const locale of LOCALES) {
        revalidatePath(`/${locale}/products`);
        revalidatedPaths.push(`/${locale}/products`);
        
        // Revalidate homepage (for featured products)
        revalidatePath(`/${locale}`);
        revalidatedPaths.push(`/${locale}`);
      }
    }

    // Revalidate by tags if using tag-based revalidation
    if (productId) {
      revalidateTag(`product-${productId}`);
      revalidateTag('products');
    }

    return NextResponse.json({
      success: true,
      revalidated: revalidatedPaths,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Error revalidating product pages:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Webhook endpoint for automated revalidation (e.g., from admin actions)
export async function PUT(request: NextRequest) {
  try {
    // Verify webhook secret
    const authHeader = request.headers.get('authorization');
    const expectedSecret = process.env.REVALIDATION_SECRET;

    if (!expectedSecret || authHeader !== `Bearer ${expectedSecret}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { type, productIds, slugs } = await request.json();

    const revalidatedPaths: string[] = [];

    switch (type) {
      case 'product-update':
        if (productIds && Array.isArray(productIds)) {
          for (const productId of productIds) {
            const product = await prisma.products.findUnique({
              where: { id: productId },
              include: {
                translations: {
                  select: {
                    slug: true,
                    language_code: true,
                  },
                },
              },
            });

            if (product) {
              for (const translation of product.translations) {
                const path = `/${translation.language_code}/products/${translation.slug}`;
                revalidatePath(path);
                revalidatedPaths.push(path);
              }
            }
          }
        }
        break;

      case 'product-create':
        // Revalidate listing pages for new products
        const { LOCALES } = await import('@/lib/constants');
        for (const locale of LOCALES) {
          revalidatePath(`/${locale}/products`);
          revalidatedPaths.push(`/${locale}/products`);
        }
        break;

      case 'bulk-update':
        // Revalidate all product-related pages
        revalidateTag('products');
        const locales = await import('@/lib/constants').then(m => m.LOCALES);
        for (const locale of locales) {
          revalidatePath(`/${locale}/products`);
          revalidatePath(`/${locale}`);
          revalidatedPaths.push(`/${locale}/products`, `/${locale}`);
        }
        break;

      default:
        return NextResponse.json({ error: 'Invalid revalidation type' }, { status: 400 });
    }

    return NextResponse.json({
      success: true,
      type,
      revalidated: revalidatedPaths,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Error in webhook revalidation:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
