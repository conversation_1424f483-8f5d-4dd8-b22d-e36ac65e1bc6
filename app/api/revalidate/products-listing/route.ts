// app/api/revalidate/products-listing/route.ts
// API endpoint for revalidating ISR product listing pages

import { NextRequest, NextResponse } from 'next/server';
import { revalidatePath, revalidateTag } from 'next/cache';
import { prisma } from '@/lib/prisma';

// Manual revalidation endpoint for product listing pages
export async function POST(request: NextRequest) {
  try {
    const { action, categoryId, marketplace, searchTerm } = await request.json();

    const revalidatedPaths: string[] = [];

    if (action === 'revalidate-all') {
      // Revalidate all product listing pages for all locales
      const { LOCALES } = await import('@/lib/constants');
      
      for (const locale of LOCALES) {
        const basePath = `/${locale}/products`;
        revalidatePath(basePath);
        revalidatedPaths.push(basePath);
        
        // Also revalidate homepage (for featured products)
        revalidatePath(`/${locale}`);
        revalidatedPaths.push(`/${locale}`);
      }
    } else if (action === 'revalidate-category' && categoryId) {
      // Revalidate specific category pages
      const { LOCALES } = await import('@/lib/constants');
      
      for (const locale of LOCALES) {
        const categoryPath = `/${locale}/products?category=${categoryId}`;
        revalidatePath(categoryPath);
        revalidatedPaths.push(categoryPath);
      }
    } else if (action === 'revalidate-marketplace' && marketplace) {
      // Revalidate specific marketplace pages
      const { LOCALES } = await import('@/lib/constants');
      
      for (const locale of LOCALES) {
        const marketplacePath = `/${locale}/products?marketplace=${marketplace}`;
        revalidatePath(marketplacePath);
        revalidatedPaths.push(marketplacePath);
      }
    } else if (action === 'revalidate-search' && searchTerm) {
      // Revalidate specific search pages
      const { LOCALES } = await import('@/lib/constants');
      
      for (const locale of LOCALES) {
        const searchPath = `/${locale}/products?search=${encodeURIComponent(searchTerm)}`;
        revalidatePath(searchPath);
        revalidatedPaths.push(searchPath);
      }
    }

    // Revalidate by tags for comprehensive cache invalidation
    revalidateTag('products');
    revalidateTag('product-listings');
    revalidateTag('categories');

    return NextResponse.json({
      success: true,
      action,
      revalidated: revalidatedPaths,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Error revalidating product listing pages:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Webhook endpoint for automated revalidation (e.g., from admin actions or external systems)
export async function PUT(request: NextRequest) {
  try {
    // Verify webhook secret
    const authHeader = request.headers.get('authorization');
    const expectedSecret = process.env.REVALIDATION_SECRET;

    if (!expectedSecret || authHeader !== `Bearer ${expectedSecret}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { type, productIds, categoryIds, marketplaces } = await request.json();

    const revalidatedPaths: string[] = [];

    switch (type) {
      case 'product-bulk-update':
        // When products are bulk updated, revalidate all listing pages
        const { LOCALES } = await import('@/lib/constants');
        for (const locale of LOCALES) {
          revalidatePath(`/${locale}/products`);
          revalidatedPaths.push(`/${locale}/products`);
          
          // Also revalidate homepage
          revalidatePath(`/${locale}`);
          revalidatedPaths.push(`/${locale}`);
        }
        break;

      case 'category-update':
        if (categoryIds && Array.isArray(categoryIds)) {
          const locales = await import('@/lib/constants').then(m => m.LOCALES);
          for (const locale of locales) {
            for (const categoryId of categoryIds) {
              const categoryPath = `/${locale}/products?category=${categoryId}`;
              revalidatePath(categoryPath);
              revalidatedPaths.push(categoryPath);
            }
            // Also revalidate main listing page
            revalidatePath(`/${locale}/products`);
            revalidatedPaths.push(`/${locale}/products`);
          }
        }
        break;

      case 'marketplace-update':
        if (marketplaces && Array.isArray(marketplaces)) {
          const locales = await import('@/lib/constants').then(m => m.LOCALES);
          for (const locale of locales) {
            for (const marketplace of marketplaces) {
              const marketplacePath = `/${locale}/products?marketplace=${marketplace}`;
              revalidatePath(marketplacePath);
              revalidatedPaths.push(marketplacePath);
            }
            // Also revalidate main listing page
            revalidatePath(`/${locale}/products`);
            revalidatedPaths.push(`/${locale}/products`);
          }
        }
        break;

      case 'pricing-update':
        // When pricing rules change, revalidate all listing pages
        revalidateTag('product-listings');
        revalidateTag('products');
        const pricingLocales = await import('@/lib/constants').then(m => m.LOCALES);
        for (const locale of pricingLocales) {
          revalidatePath(`/${locale}/products`);
          revalidatedPaths.push(`/${locale}/products`);
        }
        break;

      case 'full-revalidation':
        // Complete revalidation of all product-related pages
        revalidateTag('products');
        revalidateTag('product-listings');
        revalidateTag('categories');
        const allLocales = await import('@/lib/constants').then(m => m.LOCALES);
        for (const locale of allLocales) {
          revalidatePath(`/${locale}/products`);
          revalidatePath(`/${locale}`);
          revalidatedPaths.push(`/${locale}/products`, `/${locale}`);
        }
        break;

      default:
        return NextResponse.json({ error: 'Invalid revalidation type' }, { status: 400 });
    }

    return NextResponse.json({
      success: true,
      type,
      revalidated: revalidatedPaths,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Error in webhook revalidation for product listings:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// GET endpoint for checking revalidation status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const locale = searchParams.get('locale') || 'en';
    
    // Return information about what can be revalidated
    return NextResponse.json({
      success: true,
      availableActions: [
        'revalidate-all',
        'revalidate-category',
        'revalidate-marketplace',
        'revalidate-search',
      ],
      webhookTypes: [
        'product-bulk-update',
        'category-update',
        'marketplace-update',
        'pricing-update',
        'full-revalidation',
      ],
      locale,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Error in GET revalidation status:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
