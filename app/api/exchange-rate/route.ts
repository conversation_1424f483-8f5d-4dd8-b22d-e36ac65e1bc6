import { NextRequest, NextResponse } from 'next/server';
import { exchangeRateService } from '@/lib/services/exchange-rate';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const from = searchParams.get('from');
    const to = searchParams.get('to');

    if (!from || !to) {
      return NextResponse.json({ error: 'Missing from or to currency' }, { status: 400 });
    }

    const rate = await exchangeRateService.getExchangeRate(from, to);

    return NextResponse.json({ rate });
  } catch (error) {
    console.error('Exchange rate error:', error);
    return NextResponse.json({ error: 'Failed to get exchange rate' }, { status: 500 });
  }
}