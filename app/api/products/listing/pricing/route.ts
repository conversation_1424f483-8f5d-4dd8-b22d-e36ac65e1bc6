// app/api/products/listing/pricing/route.ts
// API endpoint for personalized product listing pricing
// This endpoint provides dynamic pricing for product listings without blocking the main content

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { pricingService } from '@/lib/services/pricing';
import { DEFAULT_CURRENCY } from '@/lib/constants';
import type { ProductListItem } from '@/lib/types';

export async function POST(request: NextRequest) {
  try {
    const { productIds, currency, locale } = await request.json();

    if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
      return NextResponse.json({ error: 'Product IDs are required' }, { status: 400 });
    }

    const finalCurrency = currency || DEFAULT_CURRENCY;
    const finalLocale = locale || 'en';

    // Fetch products with minimal data needed for pricing
    const products = await prisma.products.findMany({
      where: {
        id: { in: productIds },
        can_show: true,
      },
      select: {
        id: true,
        marketplace: true,
        offers: {
          orderBy: { price_low: 'asc' },
          take: 1, // Only get lowest price offer for listing
          select: {
            id: true,
            price_low: true,
            price_high: true,
            currency: true,
            min_quantity: true,
          },
        },
        categories: {
          take: 1,
          select: {
            category: {
              select: {
                id: true,
              },
            },
          },
        },
      },
    });

    if (products.length === 0) {
      return NextResponse.json({ error: 'No products found' }, { status: 404 });
    }

    // Calculate personalized pricing for each product
    const pricingResults: Record<string, any> = {};

    for (const product of products) {
      if (product.offers.length === 0) continue;

      const offer = product.offers[0];
      
      // Calculate personalized pricing
      const pricingInputs = [{
        costPriceCNY: Number(offer.price_low),
        context: {
          productId: product.id,
          categoryId: product.categories[0]?.category.id,
          marketplace: product.marketplace as string,
          userCurrency: finalCurrency,
        },
      }];

      try {
        const pricingResult = await pricingService.calculatePrices(pricingInputs);
        
        if (pricingResult.length > 0) {
          const pricing = pricingResult[0];
          pricingResults[product.id.toString()] = {
            productId: product.id,
            offer: {
              id: offer.id,
              price_low: pricing.displayPrice,
              price_high: offer.price_high ? Number(offer.price_high) * pricing.exchangeRate : null,
              currency: finalCurrency,
              min_quantity: offer.min_quantity,
              display_price: pricing.displayPrice,
              display_currency: finalCurrency,
              exchange_rate: pricing.exchangeRate,
            },
          };
        }
      } catch (pricingError) {
        console.error(`Error calculating pricing for product ${product.id}:`, pricingError);
        // Fallback to original pricing
        pricingResults[product.id.toString()] = {
          productId: product.id,
          offer: {
            id: offer.id,
            price_low: Number(offer.price_low),
            price_high: offer.price_high ? Number(offer.price_high) : null,
            currency: offer.currency,
            min_quantity: offer.min_quantity,
            display_price: Number(offer.price_low),
            display_currency: offer.currency,
            exchange_rate: 1,
          },
        };
      }
    }

    return NextResponse.json({
      success: true,
      currency: finalCurrency,
      pricing: pricingResults,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Error fetching product listing pricing:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// GET method for simple single product pricing queries
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const productId = searchParams.get('productId');
    const currency = searchParams.get('currency') || DEFAULT_CURRENCY;
    const locale = searchParams.get('locale') || 'en';

    if (!productId) {
      return NextResponse.json({ error: 'Product ID is required' }, { status: 400 });
    }

    // Use POST method internally for consistency
    const postResponse = await POST(new NextRequest(request.url, {
      method: 'POST',
      body: JSON.stringify({
        productIds: [parseInt(productId)],
        currency,
        locale,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    }));

    return postResponse;

  } catch (error) {
    console.error('Error in GET product listing pricing:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
