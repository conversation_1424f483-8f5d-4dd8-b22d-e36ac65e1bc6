// app/api/products/listing/availability/route.ts
// API endpoint for real-time product availability in listings
// This endpoint provides availability status without blocking the main static content

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    const { productIds, locale } = await request.json();

    if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
      return NextResponse.json({ error: 'Product IDs are required' }, { status: 400 });
    }

    const finalLocale = locale || 'en';

    // Fetch availability data for multiple products
    const products = await prisma.products.findMany({
      where: {
        id: { in: productIds },
        can_show: true,
      },
      select: {
        id: true,
        variants: {
          select: {
            id: true,
            available_quantity: true,
            min_quantity: true,
          },
        },
        offers: {
          select: {
            id: true,
            min_quantity: true,
            quantity_info: true,
          },
        },
      },
    });

    if (products.length === 0) {
      return NextResponse.json({ error: 'No products found' }, { status: 404 });
    }

    // Process availability for each product
    const availabilityResults: Record<string, any> = {};

    for (const product of products) {
      // Check if product has available variants
      const hasAvailableVariants = product.variants.length === 0 || 
        product.variants.some(variant => 
          variant.available_quantity === null || Number(variant.available_quantity) > 0
        );

      const isAvailable = hasAvailableVariants;

      // Process variant availability
      const variantAvailability = product.variants.map(variant => ({
        id: variant.id.toString(),
        available: variant.available_quantity === null || Number(variant.available_quantity) > 0,
        quantity: variant.available_quantity ? Number(variant.available_quantity) : null,
        minQuantity: variant.min_quantity ? Number(variant.min_quantity) : null,
      }));

      // Process offer availability
      const offerAvailability = product.offers.map(offer => ({
        id: offer.id.toString(),
        available: true, // Offers are generally available unless explicitly stated
        quantityInfo: offer.quantity_info,
        minQuantity: Number(offer.min_quantity),
      }));

      availabilityResults[product.id.toString()] = {
        productId: product.id,
        available: isAvailable,
        variants: variantAvailability,
        offers: offerAvailability,
      };
    }

    return NextResponse.json({
      success: true,
      availability: availabilityResults,
      lastUpdated: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Error fetching product listing availability:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// GET method for simple single product availability queries
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const productId = searchParams.get('productId');
    const locale = searchParams.get('locale') || 'en';

    if (!productId) {
      return NextResponse.json({ error: 'Product ID is required' }, { status: 400 });
    }

    // Use POST method internally for consistency
    const postResponse = await POST(new NextRequest(request.url, {
      method: 'POST',
      body: JSON.stringify({
        productIds: [parseInt(productId)],
        locale,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    }));

    return postResponse;

  } catch (error) {
    console.error('Error in GET product listing availability:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
