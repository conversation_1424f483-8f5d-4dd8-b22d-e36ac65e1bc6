// app/api/products/listing/search/route.ts
// API endpoint for dynamic product listing search and filtering
// This endpoint provides search results without blocking the main static content

import { NextRequest, NextResponse } from 'next/server';
import { getProducts } from '@/lib/actions/product.actions';
import { DEFAULT_CURRENCY } from '@/lib/constants';
import type { ProductFilters } from '@/lib/types';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse search parameters
    const filters: ProductFilters = {
      search: searchParams.get('search') || undefined,
      categoryId: searchParams.get('categoryId') ? parseInt(searchParams.get('categoryId')!) : undefined,
      marketplace: searchParams.get('marketplace') || undefined,
      minPrice: searchParams.get('minPrice') ? parseFloat(searchParams.get('minPrice')!) : undefined,
      maxPrice: searchParams.get('maxPrice') ? parseFloat(searchParams.get('maxPrice')!) : undefined,
      sortBy: (searchParams.get('sortBy') as 'newest' | 'price_asc' | 'price_desc' | 'popular') || 'newest',
      cursor: searchParams.get('cursor') || undefined,
      limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined,
    };

    const locale = searchParams.get('locale') || 'en';
    const currency = searchParams.get('currency') || DEFAULT_CURRENCY;

    // Use the existing dynamic product action for search
    const result = await getProducts(filters, locale, undefined, undefined, currency);

    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: result.pagination,
      filters: filters,
      locale,
      currency,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Error in product listing search:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { filters, locale, currency } = body;

    if (!filters) {
      return NextResponse.json({ error: 'Filters are required' }, { status: 400 });
    }

    const finalLocale = locale || 'en';
    const finalCurrency = currency || DEFAULT_CURRENCY;

    // Use the existing dynamic product action for search
    const result = await getProducts(filters, finalLocale, undefined, undefined, finalCurrency);

    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: result.pagination,
      filters: filters,
      locale: finalLocale,
      currency: finalCurrency,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Error in product listing search (POST):', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
