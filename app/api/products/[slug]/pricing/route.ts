// app/api/products/[slug]/pricing/route.ts
// API endpoint for personalized product pricing

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { pricingService } from '@/lib/services/pricing';
import { DEFAULT_CURRENCY } from '@/lib/constants';

interface RouteParams {
  params: Promise<{ slug: string }>;
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { slug } = await params;
    const { searchParams } = new URL(request.url);
    const currency = searchParams.get('currency') || DEFAULT_CURRENCY;
    const locale = searchParams.get('locale') || 'en';

    // Find product by slug
    const translation = await prisma.product_translations.findFirst({
      where: {
        slug,
        language_code: locale,
      },
      select: {
        product_id: true,
      },
    });

    if (!translation) {
      return NextResponse.json({ error: 'Product not found' }, { status: 404 });
    }

    // Get product offers for pricing
    const product = await prisma.products.findUnique({
      where: {
        id: translation.product_id,
        can_show: true,
      },
      select: {
        id: true,
        marketplace: true,
        offers: {
          orderBy: { min_quantity: 'asc' },
          select: {
            id: true,
            price_low: true,
            price_high: true,
            currency: true,
            min_quantity: true,
          },
        },
        categories: {
          take: 1,
          select: {
            category: {
              select: {
                id: true,
              },
            },
          },
        },
      },
    });

    if (!product || !product.offers.length) {
      return NextResponse.json({ error: 'Product offers not found' }, { status: 404 });
    }

    // Calculate personalized pricing
    const pricingInputs = product.offers.map(offer => ({
      costPriceCNY: Number(offer.price_low),
      context: {
        productId: product.id,
        categoryId: product.categories[0]?.category.id,
        marketplace: product.marketplace as string,
        userCurrency: currency,
      },
    }));

    const pricingResults = await pricingService.calculatePrices(pricingInputs);

    // Apply pricing results to offers
    const personalizedOffers = product.offers.map((offer, index) => {
      const pricingResult = pricingResults[index];
      if (pricingResult) {
        return {
          id: offer.id.toString(),
          price_low: offer.price_low,
          price_high: offer.price_high,
          currency: offer.currency,
          min_quantity: offer.min_quantity,
          display_price: pricingResult.displayPrice,
          display_currency: pricingResult.currency,
          exchange_rate: pricingResult.exchangeRate,
        };
      }
      return {
        id: offer.id.toString(),
        price_low: offer.price_low,
        price_high: offer.price_high,
        currency: offer.currency,
        min_quantity: offer.min_quantity,
        display_price: Number(offer.price_low),
        display_currency: currency,
        exchange_rate: 1,
      };
    });

    return NextResponse.json({
      productId: product.id,
      offers: personalizedOffers,
      currency,
    });

  } catch (error) {
    console.error('Error fetching product pricing:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
