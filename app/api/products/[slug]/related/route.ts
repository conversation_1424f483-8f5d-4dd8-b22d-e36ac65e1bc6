// app/api/products/[slug]/related/route.ts
// API endpoint for related products with personalized pricing

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { pricingService } from '@/lib/services/pricing';
import { DEFAULT_CURRENCY } from '@/lib/constants';

interface RouteParams {
  params: Promise<{ slug: string }>;
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { slug } = await params;
    const { searchParams } = new URL(request.url);
    const currency = searchParams.get('currency') || DEFAULT_CURRENCY;
    const locale = searchParams.get('locale') || 'en';
    const limit = parseInt(searchParams.get('limit') || '6');

    // Find product by slug
    const translation = await prisma.product_translations.findFirst({
      where: {
        slug,
        language_code: locale,
      },
      select: {
        product_id: true,
      },
    });

    if (!translation) {
      return NextResponse.json({ error: 'Product not found' }, { status: 404 });
    }

    // Get product categories for finding related products
    const product = await prisma.products.findUnique({
      where: { id: translation.product_id },
      select: {
        categories: {
          take: 1,
          select: { category_id: true },
        },
      },
    });

    if (!product || !product.categories.length) {
      return NextResponse.json({ data: [] });
    }

    const categoryId = product.categories[0].category_id;

    // Find related products in same category
    const relatedProducts = await prisma.products.findMany({
      where: {
        can_show: true,
        id: { not: translation.product_id },
        categories: {
          some: {
            category_id: categoryId,
          },
        },
      },
      take: limit,
      select: {
        id: true,
        original_name: true,
        marketplace: true,
        translations: {
          where: { language_code: locale },
          take: 1,
          select: {
            name: true,
            slug: true,
            language_code: true,
          },
        },
        product_images: {
          where: { image_type: 'preview' },
          take: 1,
          orderBy: { id: 'asc' },
          select: {
            image_url: true,
            image_type: true,
          },
        },
        offers: {
          orderBy: { price_low: 'asc' },
          take: 1,
          select: {
            id: true,
            price_low: true,
            price_high: true,
            currency: true,
            min_quantity: true,
          },
        },
        categories: {
          take: 3,
          select: {
            category: {
              select: {
                id: true,
                translations: {
                  where: { language_code: locale },
                  take: 1,
                  select: {
                    name: true,
                    slug: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    // Calculate personalized pricing for related products
    let finalRelatedProducts = relatedProducts;
    if (relatedProducts.length > 0) {
      const pricingInputs = relatedProducts
        .filter(product => product.offers[0])
        .map(product => ({
          costPriceCNY: Number(product.offers[0].price_low),
          context: {
            productId: product.id,
            categoryId: product.categories[0]?.category.id,
            marketplace: product.marketplace as string,
            userCurrency: currency,
          },
        }));

      const pricingResults = await pricingService.calculatePrices(pricingInputs);

      // Create a lookup map for O(1) access
      const pricingMap = new Map(
        pricingResults.map(result => [result.productId, result])
      );

      // Apply pricing results to products
      finalRelatedProducts = relatedProducts.map(product => {
        const offer = product.offers[0];
        if (!offer) return product;

        const pricingResult = pricingMap.get(product.id);
        if (!pricingResult) return product;

        // Update offer with calculated price
        return {
          ...product,
          offers: [{
            ...offer,
            display_price: pricingResult.displayPrice,
            display_currency: pricingResult.currency,
            exchange_rate: pricingResult.exchangeRate,
          }],
        };
      });
    }

    return NextResponse.json({
      data: JSON.parse(JSON.stringify(finalRelatedProducts.map)),
      currency,
    });

  } catch (error) {
    console.error('Error fetching related products:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
