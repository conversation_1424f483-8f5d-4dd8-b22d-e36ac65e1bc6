// app/api/products/[slug]/availability/route.ts
// API endpoint for real-time product availability

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

interface RouteParams {
  params: Promise<{ slug: string }>;
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { slug } = await params;
    const { searchParams } = new URL(request.url);
    const locale = searchParams.get('locale') || 'en';

    // Find product by slug
    const translation = await prisma.product_translations.findFirst({
      where: {
        slug,
        language_code: locale,
      },
      select: {
        product_id: true,
      },
    });

    if (!translation) {
      return NextResponse.json({ error: 'Product not found' }, { status: 404 });
    }

    // Get product availability data
    const product = await prisma.products.findUnique({
      where: {
        id: translation.product_id,
        can_show: true,
      },
      select: {
        id: true,
        variants: {
          select: {
            id: true,
            original_variant_name: true,
            original_variant_type: true,
            available_quantity: true,
            min_quantity: true,
            translations: {
              where: { language_code: locale },
              select: {
                variant_name: true,
                variant_type: true,
              },
            },
          },
        },
        offers: {
          select: {
            id: true,
            min_quantity: true,
            quantity_info: true,
          },
        },
      },
    });

    if (!product) {
      return NextResponse.json({ error: 'Product not found' }, { status: 404 });
    }

    // Calculate overall availability based on variants
    const hasAvailableVariants = product.variants.some(variant =>
      variant.available_quantity === null || Number(variant.available_quantity) > 0
    );
    const isAvailable = product.variants.length === 0 || hasAvailableVariants;

    // Process variant availability
    const variantAvailability = product.variants.map(variant => ({
      id: variant.id.toString(),
      name: variant.translations[0]?.variant_name || variant.original_variant_name,
      type: variant.translations[0]?.variant_type || variant.original_variant_type,
      available: variant.available_quantity === null || Number(variant.available_quantity) > 0,
      quantity: variant.available_quantity ? Number(variant.available_quantity) : null,
      minQuantity: variant.min_quantity ? Number(variant.min_quantity) : null,
    }));

    // Process offer availability (offers use quantity_info as text, not numeric)
    const offerAvailability = product.offers.map(offer => ({
      id: offer.id.toString(),
      available: true, // Offers are generally available unless explicitly stated
      quantityInfo: offer.quantity_info,
      minQuantity: Number(offer.min_quantity),
    }));

    return NextResponse.json({
      productId: product.id,
      available: isAvailable,
      variants: variantAvailability,
      offers: offerAvailability,
      lastUpdated: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Error fetching product availability:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
