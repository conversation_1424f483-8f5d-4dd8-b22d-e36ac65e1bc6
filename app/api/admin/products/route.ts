// app/api/admin/products/route.ts
// API routes for product management

import { NextRequest, NextResponse } from 'next/server';
import { createProduct } from '@/lib/actions/admin/product.actions';

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    
    const result = await createProduct(data);
    
    if (result.success) {
      return NextResponse.json({ 
        success: true, 
        id: result.productId 
      }, { status: 201 });
    } else {
      return NextResponse.json({ 
        error: result.error 
      }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in POST /api/admin/products:', error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}
