// app/api/admin/products/variants/route.ts
// API route for creating product variants

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/actions/auth.actions';
import { checkPermission } from '@/lib/auth/permissions';
import { PermissionAction } from '@/app/generated/prisma';

export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PRODUCT_MANAGE_VARIANTS
    );

    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const {
      productId,
      original_variant_name,
      original_variant_type,
      available_quantity,
      min_quantity,
      price_low,
      price_high,
      currency
    } = await request.json();

    if (!productId || !original_variant_name || !original_variant_type || !price_low || !currency) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    const variant = await prisma.variants.create({
      data: {
        product_id: parseInt(productId),
        original_variant_name,
        original_variant_type,
        available_quantity: available_quantity ? parseFloat(available_quantity) : null,
        min_quantity: min_quantity ? parseFloat(min_quantity) : null,
        price_low: parseFloat(price_low),
        price_high: price_high ? parseFloat(price_high) : null,
        currency,
      },
      include: {
        translations: true,
      },
    });

    return NextResponse.json(variant, { status: 201 });
  } catch (error) {
    console.error('Error creating variant:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}