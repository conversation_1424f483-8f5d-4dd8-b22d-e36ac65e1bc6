// app/api/admin/products/attributes/[attributeId]/route.ts
// API routes for individual attribute management

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/actions/auth.actions';
import { checkPermission } from '@/lib/auth/permissions';
import { PermissionAction } from '@/app/generated/prisma';

interface RouteParams {
  params: Promise<{ attributeId: string }>;
}

// PATCH /api/admin/products/attributes/[attributeId] - Update an attribute
export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PRODUCT_UPDATE
    );

    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { attributeId } = await params;
    const data = await request.json();

    // Handle translation updates if provided
    if (data.translations) {
      await prisma.$transaction(async (tx) => {
        // Delete existing translations
        await tx.product_attributes_translations.deleteMany({
          where: { attributes_id: Number(attributeId) },
        });

        // Create new translations
        for (const translation of data.translations) {
          await tx.product_attributes_translations.create({
            data: {
              attributes_id: Number(attributeId),
              language_code: translation.language_code,
              attr_key: translation.attr_key || data.original_attr_key,
              attr_value: translation.attr_value || data.original_attr_value,
            },
          });
        }
      });
    }

    // Update the main attribute if needed
    if (data.original_attr_key || data.original_attr_value) {
      await prisma.product_attributes.update({
        where: { id: Number(attributeId) },
        data: {
          original_attr_key: data.original_attr_key,
          original_attr_value: data.original_attr_value,
        },
      });
    }

    // Fetch and return the updated attribute
    const updatedAttribute = await prisma.product_attributes.findUnique({
      where: { id: Number(attributeId) },
      include: {
        translations: true,
      },
    });

    return NextResponse.json(updatedAttribute);
  } catch (error) {
    console.error('Error updating attribute:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// DELETE /api/admin/products/attributes/[attributeId] - Delete an attribute
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PRODUCT_UPDATE
    );

    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { attributeId } = await params;

    await prisma.product_attributes.delete({
      where: { id: Number(attributeId) },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting attribute:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}