// app/api/admin/products/[productId]/route.ts
// API routes for individual product management

import { NextRequest, NextResponse } from 'next/server';
import { updateProduct } from '@/lib/actions/admin/product.actions';

interface RouteParams {
  params: Promise<{ productId: string }>;
}

export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    const { productId } = await params;
    const data = await request.json();
    
    const result = await updateProduct(parseInt(productId), data);
    
    if (result.success) {
      return NextResponse.json({ success: true });
    } else {
      return NextResponse.json({ 
        error: result.error 
      }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in PATCH /api/admin/products/[productId]:', error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}
