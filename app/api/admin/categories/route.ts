// app/api/admin/categories/route.ts
// API routes for category management

import { NextRequest, NextResponse } from 'next/server';
import { createCategory, getAllCategories } from '@/lib/actions/admin/category.actions';

export async function GET() {
  try {
    const categories = await getAllCategories();
    
    if ('error' in categories) {
      return NextResponse.json({ 
        error: categories.error 
      }, { status: 400 });
    }

    return NextResponse.json(categories);
  } catch (error) {
    console.error('Error in GET /api/admin/categories:', error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    
    const result = await createCategory(data);
    
    if (result.success) {
      return NextResponse.json({ 
        success: true, 
        id: result.categoryId 
      }, { status: 201 });
    } else {
      return NextResponse.json({ 
        error: result.error 
      }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in POST /api/admin/categories:', error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}
