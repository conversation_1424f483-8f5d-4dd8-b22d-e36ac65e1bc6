// app/api/admin/categories/[categoryId]/route.ts
// API routes for individual category management

import { NextRequest, NextResponse } from 'next/server';
import { getCategoryById, updateCategory, deleteCategory } from '@/lib/actions/admin/category.actions';

interface RouteParams {
  params: Promise<{ categoryId: string }>;
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { categoryId } = await params;
    
    const category = await getCategoryById(parseInt(categoryId));
    
    if ('error' in category) {
      return NextResponse.json({ 
        error: category.error 
      }, { status: 404 });
    }

    return NextResponse.json(category);
  } catch (error) {
    console.error('Error in GET /api/admin/categories/[categoryId]:', error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}

export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    const { categoryId } = await params;
    const data = await request.json();
    
    const result = await updateCategory(parseInt(categoryId), data);
    
    if (result.success) {
      return NextResponse.json({ success: true });
    } else {
      return NextResponse.json({ 
        error: result.error 
      }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in PATCH /api/admin/categories/[categoryId]:', error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { categoryId } = await params;
    
    const result = await deleteCategory(parseInt(categoryId));
    
    if (result.success) {
      return NextResponse.json({ success: true });
    } else {
      return NextResponse.json({ 
        error: result.error 
      }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in DELETE /api/admin/categories/[categoryId]:', error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}
