// app/robots.ts
// Robots.txt configuration for SEO

import { MetadataRoute } from 'next';
import { SITE_CONFIG } from '@/lib/constants';

export default function robots(): MetadataRoute.Robots {
  return {
    rules: {
      userAgent: '*',
      allow: '/',
      disallow: [
        '/admin/',
        '/account/',
        '/checkout/',
        '/api/',
        '/_next/',
        '/private/',
      ],
    },
    sitemap: `${SITE_CONFIG.url}/sitemap.xml`,
  };
}