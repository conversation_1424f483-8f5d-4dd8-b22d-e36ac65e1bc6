// app/[locale]/(checkout)/payment/page.tsx
// Refactored: Now calculates totals server-side for consistency.

import { getTranslations } from 'next-intl/server';
import { redirect } from 'next/navigation';
import { getCurrentUser } from '@/lib/actions/auth.actions';
import type { Address, CartItem } from '@/lib/types';
import PaymentClient from './payment-client';
import { DEFAULT_CURRENCY } from '@/lib/constants';

interface PageProps {
  params: Promise<{ locale: string }>;
  searchParams: Promise<{
    orderId?: string;
  }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'checkout' });

  return {
    title: t('paymentMethod'),
  };
}

export default async function PaymentPage({ params, searchParams }: PageProps) {
  const { locale } = await params;
  const search = await searchParams;
  const t = await getTranslations({ locale, namespace: 'checkout' });
  const user = await getCurrentUser();

  // User must be authenticated to proceed
  if (!user) {
    redirect(`/${locale}/login`);
  }

  // An orderId is required to access the payment page
  const orderId = search.orderId;
  if (!orderId) {
    redirect(`/${locale}/cart`);
  }

  try {
    // Fetch the order from the database
    const { getOrderById } = await import('@/lib/actions/order.actions');
    const order = await getOrderById(orderId);

    // Validate that the order exists
    if (!order) {
      console.error(t('orderNotFound', { orderId }));
      redirect(`/${locale}/cart`);
    }

    // Security check: Ensure the order belongs to the currently logged-in user
    if (order.customer_id !== user.customerId) {
      console.error(t('unauthorizedAccess'));
      redirect(`/${locale}/cart`);
    }

    // Transform order items into a clean format for the client components
    const cartItems: CartItem[] = order.order_items.map(item => ({
      id: Number(item.id),
      productId: item.product_id,
      variantId: item.variant_id ? Number(item.variant_id) : undefined,
      quantity: Number(item.quantity),
      productName: item.product?.translations?.[0]?.name || item.product?.original_name || 'Unknown Product',
      price: Number(item.price_per_unit),
      currency: order.currency,
      imageUrl: item.image_url || item.product?.product_images?.[0]?.image_url,
      marketplace: item.product?.marketplace || 'Unknown Marketplace',
      productSlug: item.product?.translations?.[0]?.slug || `product-${item.product_id}`,
    }));

    // **Refactor Improvement:**
    // Calculate subtotal and determine currency on the server.
    // This prevents layout shifts or inconsistencies on the client.
    const subtotal = cartItems.reduce((sum, item) => sum + item.price * item.quantity, 0);
    const currency = order.currency || DEFAULT_CURRENCY;

    const shippingAddress = order.shipping_address as unknown as Address;

    // Pass the prepared data to the main client component which will handle the UI.
    return (
      <PaymentClient
        locale={locale}
        orderId={orderId}
        orderStatus={order.status}
        initialCartItems={cartItems}
        initialSubtotal={subtotal}
        initialCurrency={currency}
        initialShippingAddress={shippingAddress}
      />
    );
  } catch (error) {
    console.error(t('failedFetchOrder'), error);
    redirect(`/${locale}/cart?error=order_not_found`);
  }
}