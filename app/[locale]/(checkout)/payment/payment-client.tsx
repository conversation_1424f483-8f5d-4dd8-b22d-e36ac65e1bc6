'use client';

// app/[locale]/(checkout)/payment/payment-client.tsx
// Refactored: Manages the new responsive two-column layout.

import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

import { But<PERSON> } from '@/components/ui/button';
import { updateOrderStatus } from '@/lib/actions/order.actions';
import { trackPurchase } from '@/lib/actions/user-activity.actions';
import type { Address, CartItem } from '@/lib/types';

// Import the components for each step of the payment flow
import { PaymentMethodSelection } from '@/components/checkout/PaymentMethodSelection';
import { MaomaoPayStep1 } from '@/components/checkout/MaomaoPayStep1';
import { MaomaoPayStep2 } from '@/components/checkout/MaomaoPayStep2';
import { BankTransferDetails } from '@/components/checkout/BankTransferDetails';
import { OrderSummary } from '@/components/checkout/OrderSummary'; // The new Order Summary component

interface PaymentClientProps {
  locale: string;
  orderId: string;
  orderStatus?: string;
  initialCartItems: CartItem[];
  initialSubtotal: number;
  initialCurrency: string;
  initialShippingAddress: Address;
}

export default function PaymentClient({
  locale,
  orderId,
  initialCartItems = [],
  initialSubtotal,
  initialCurrency,
  initialShippingAddress,
}: PaymentClientProps) {
  const t = useTranslations('checkout');
  const router = useRouter();

  // State to manage the current step in the payment process
  const [paymentFlow, setPaymentFlow] = useState<'selection' | 'maomao_step1' | 'maomao_step2' | 'bank_transfer'>('selection');
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  // --- Handlers for navigating the payment flow ---

  const handlePaymentMethodSelect = (methodId: string) => {
    setSelectedPaymentMethod(methodId);
    if (methodId === 'mobile_money') {
      setPaymentFlow('maomao_step1');
    } else if (methodId === 'bank_transfer') {
      setPaymentFlow('bank_transfer');
    } else if (methodId === 'card_payment') {
      // Future implementation for card payments
      alert(t('cardPaymentComingSoon'));
    }
  };

  const handleMaomaoConfirm = (phoneNumber: string, countryCode: string) => {
    console.log('Initiating mobile money payment:', { phoneNumber, countryCode });
    // In a real application, this would trigger an API call to the payment provider.
    // For this UI flow, we will proceed to the confirmation step.
    setPaymentFlow('maomao_step2');
  };

  const handleBackToSelection = () => {
    setPaymentFlow('selection');
    setSelectedPaymentMethod(null);
  };

  // This function is now only relevant for bank transfers where payment is offline.
  // For other methods, the final step would trigger this.
  const handleConfirmOrder = async () => {
    setIsProcessing(true);
    try {
      if (!orderId || initialCartItems.length === 0 || !initialShippingAddress?.id) {
        throw new Error(t('missingOrderInfo'));
      }

      // Track purchase activity
      const sessionId = `payment_${Date.now()}`;
      for (const item of initialCartItems) {
        await trackPurchase(item.productId, item.marketplace, sessionId);
      }

      // Mark the order as 'pending' for offline payments, or 'paid' for instant ones.
      // Here we assume bank transfer is an offline method.
      const newStatus = selectedPaymentMethod === 'bank_transfer' ? 'unpaid' : 'paid';
      const result = await updateOrderStatus(orderId, newStatus);

      // Dispatch event to update unpaid orders count
      if (result.success) {
        window.dispatchEvent(new CustomEvent('order-status-changed', {
          detail: { orderId, newStatus, previousStatus: 'unpaid' as const }
        }));
      }

      if (result.success) {
        router.push(`/${locale}/success/${orderId}`);
      } else {
        throw new Error(result.error || t('paymentFailed'));
      }
    } catch (error) {
      console.error('Order confirmation error:', error);
      router.push(`/${locale}/cart?error=payment_failed`);
    } finally {
      setIsProcessing(false);
    }
  };


  // --- Helper data for payment components ---

  const customerInfo = {
    name: initialShippingAddress.fullName || 'N/A',
    phone: initialShippingAddress.phone || '',
    whatsapp: initialShippingAddress.phone || '',
    country: initialShippingAddress.country || '',
    shippingAddress: [
      initialShippingAddress.country,
      initialShippingAddress.city,
      initialShippingAddress.addressLine1,
    ].filter(Boolean).join(', '),
  };

  const sepaBankDetails = {
    beneficiaryName: 'Brainstorm (Group) Holding Limited',
    accountNumber: '**********************',
    swiftCode: 'CHASIE4L',
    bankName: 'J.P. MORGAN BANK LUXEMBOURG S.A., DUBLIN BRANCH',
    address: '200 Capital Dock 79 Sir John Rogersons Quay Dublin 2 D02 RK57',
    country: 'IE',
    currency: 'EUR',
    processingFee: '0%',
  };

  const globalBankDetails = {
    beneficiaryName: 'Brainstorm (Group) Holding Limited',
    currencies: 'EUR, USD, HKD, GBP, CNH, CAD, SGD, JPY, AUD, NZD',
    accountNumber: '***********',
    swiftCode: 'CHASHKHH',
    bankCode: '007',
    branchCode: '863',
    bankName: 'JPMorgan Chase Bank N.A., Hong Kong Branch',
    bankAddress: '18/F, 20/F, 22-29/F, CHATER HOUSE, 8 CONNAUGHT ROAD CENTRAL, HONG KONG',
  };


  // --- Main Render Method ---

  return (
    <div className="container mx-auto max-w-6xl px-4 py-8">
      {/* 
        This is the core of the new responsive layout.
        - On mobile: A single column stack (default behavior).
        - On medium screens and up (md:): A two-column grid.
      */}
      <div className="grid grid-cols-1 gap-x-12 md:grid-cols-2">

        {/* Left Column: Payment Steps (Main Interaction Area) */}
        <div className="order-2 md:order-1">
          {/* We conditionally render the correct component based on the paymentFlow state */}
          {paymentFlow === 'selection' && (
            <PaymentMethodSelection
              onSelect={handlePaymentMethodSelect}
            />
          )}

          {paymentFlow === 'maomao_step1' && (
            <MaomaoPayStep1
              onBack={handleBackToSelection}
              onConfirm={handleMaomaoConfirm}
              customerInfo={customerInfo}
              defaultCountryCode="+237"
            />
          )}

          {paymentFlow === 'maomao_step2' && (
            <MaomaoPayStep2
              onBack={() => setPaymentFlow('maomao_step1')}
              onCancel={handleBackToSelection}
              onConfirm={handleConfirmOrder}
              isProcessing={isProcessing}
              orderId={orderId}
            />
          )}

          {paymentFlow === 'bank_transfer' && (
            <BankTransferDetails
              onBack={handleBackToSelection}
              onConfirm={handleConfirmOrder}
              isProcessing={isProcessing}
              sepaBankDetails={sepaBankDetails}
              globalBankDetails={globalBankDetails}
            />
          )}
        </div>

        {/* 
          Right Column: Order Summary
          - On mobile (order-1), this appears *above* the payment steps.
          - On desktop (order-2), this appears to the right.
        */}
        <div className="order-1 md:order-2">
          <OrderSummary
            items={initialCartItems}
            subtotal={initialSubtotal}
            currency={initialCurrency}
            // In a real app, you would pass shipping and taxes here as well
            shippingCost={0}
            taxes={0}
          />
        </div>
      </div>
    </div>
  );
}