// app/[locale]/(checkout)/shipping/page.tsx
// Multi-step shipping and order confirmation page with SWR prefetching

export const dynamic = 'force-dynamic';

import { getTranslations } from 'next-intl/server';
import { getUserAddresses } from '@/lib/actions/user.actions';
import { ShippingCheckoutFlow } from '@/components/checkout/ShippingCheckoutFlow';
import { PrefetchCartOnServer } from '@/components/providers/PrefetchCartOnServer';
import { UnpaidOrdersBanner } from '@/components/ui/unpaid-orders-banner';

// Force dynamic to ensure cart store is initialized

interface PageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'checkout' });

  return {
    title: t('shippingAddress'),
  };
}

export default async function ShippingPage({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'checkout' });

  const addresses = await getUserAddresses();

  return (
    <PrefetchCartOnServer>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto space-y-6">

          <div className="animate-in fade-in slide-in-from-top-2 duration-500">
            <h1 className="text-4xl font-bold tracking-tight mb-2">{t('shippingAddress')}</h1>
            <p className="text-muted-foreground text-lg">{t('selectShippingAddress')}</p>
          </div>

          <ShippingCheckoutFlow addresses={addresses || []} locale={locale} />
        </div>
      </div>
    </PrefetchCartOnServer>
  );
}
