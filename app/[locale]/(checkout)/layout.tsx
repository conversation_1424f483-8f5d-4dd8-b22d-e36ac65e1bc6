// app/[locale]/(checkout)/layout.tsx
// Checkout layout
// NOTE: Authentication is handled by middleware - no need for getCurrentUser() here

interface LayoutProps {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}

export default async function CheckoutLayout({ children }: LayoutProps) {
  // Middleware handles authentication and redirects unauthenticated users to login
  // No need to call getCurrentUser() here - it's a blocking operation
  return <>{children}</>;
}
