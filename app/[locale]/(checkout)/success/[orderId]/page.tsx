// app/[locale]/(checkout)/success/[orderId]/page.tsx
// Enhanced order success page with SWR + TanStack Query utilities

import { getTranslations } from 'next-intl/server';
import { Suspense } from 'react';
import { OrderSuccessClient } from './success-client';

// Server component wrapper to handle metadata and translations
async function SuccessPageServer({ params }: { params: Promise<{ locale: string; orderId: string }> }) {
  const { locale, orderId } = await params;

  return <OrderSuccessClient orderId={orderId} locale={locale} />;
}

export async function generateMetadata({ params }: { params: Promise<{ locale: string; orderId: string }> }) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'checkout' });

  return {
    title: t('orderSuccess'),
  };
}

// Loading skeleton component
function OrderSuccessSkeleton() {
  return (
    <div className="container mx-auto px-4 py-16">
      <div className="max-w-2xl mx-auto">
        <div className="text-center mb-8">
          <div className="w-20 h-20 rounded-full bg-gray-200 dark:bg-gray-800 mx-auto mb-4 animate-pulse" />
          <div className="h-8 w-48 bg-gray-200 dark:bg-gray-800 mx-auto mb-2 animate-pulse" />
          <div className="h-4 w-32 bg-gray-200 dark:bg-gray-800 mx-auto animate-pulse" />
        </div>
        <div className="h-32 w-full bg-gray-200 dark:bg-gray-800 mb-6 animate-pulse rounded-lg" />
        <div className="h-48 w-full bg-gray-200 dark:bg-gray-800 mb-6 animate-pulse rounded-lg" />
        <div className="h-32 w-full bg-gray-200 dark:bg-gray-800 mb-6 animate-pulse rounded-lg" />
        <div className="flex gap-4">
          <div className="h-10 flex-1 bg-gray-200 dark:bg-gray-800 animate-pulse rounded" />
          <div className="h-10 flex-1 bg-gray-200 dark:bg-gray-800 animate-pulse rounded" />
        </div>
      </div>
    </div>
  );
}

export default async function OrderSuccessPage({ params }: { params: Promise<{ locale: string; orderId: string }> }) {
  return (
    <Suspense fallback={<OrderSuccessSkeleton />}>
      <SuccessPageServer params={params} />
    </Suspense>
  );
}
