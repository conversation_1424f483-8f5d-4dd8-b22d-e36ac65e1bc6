// app/[locale]/(checkout)/success/[orderId]/success-client.tsx
// Redesigned client component for an enhanced, professional, and responsive order success page.

'use client';

import { useEffect, type FC, type ReactNode } from 'react';
import Link from 'next/link';
import { CheckCircle, Package, Info, RefreshCw, MapPin, Ship, Plane, User, Phone } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { ImageWithFallback } from '@/components/ui/image-with-fallback';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useOrderDetails } from '@/hooks/queries/useOrderDetails';
import { usePrefetchOrderDetails } from '@/hooks/queries/usePrefetchOrderDetails';
import { useCartAnalytics } from '@/hooks/queries/useCartAnalytics';
import { useBatchCartOperations } from '@/hooks/queries/useBatchCartOperations';
import { useAddToCart } from '@/hooks/mutations/useAddToCart';
import { useToast } from '@/components/providers/ToastProvider';
import { clearBuyNowCart } from '@/lib/actions/cart.actions';
import { formatCurrency, cn } from '@/lib/utils';
import type { Address } from '@/lib/types';

// Define a more specific type for an order item based on the usage in the component
type OrderItem = NonNullable<ReturnType<typeof useOrderDetails>['data']>['order_items'][0];

// ========== Skeleton Loader ========== //
function OrderSuccessSkeleton() {
  return (
    <div className="container mx-auto px-4 py-12 md:py-16 animate-pulse">
      <div className="h-24 w-full bg-gray-200 dark:bg-gray-800 rounded-lg mb-8" />
      <div className="grid grid-cols-1 lg:grid-cols-3 lg:gap-8">
        <div className="lg:col-span-2 space-y-6">
          <div className="h-48 w-full bg-gray-200 dark:bg-gray-800 rounded-lg" />
          <div className="h-64 w-full bg-gray-200 dark:bg-gray-800 rounded-lg" />
        </div>
        <div className="space-y-6 mt-6 lg:mt-0">
          <div className="h-32 w-full bg-gray-200 dark:bg-gray-800 rounded-lg" />
          <div className="h-40 w-full bg-gray-200 dark:bg-gray-800 rounded-lg" />
        </div>
      </div>
    </div>
  );
}

// ========== Error State ========== //
interface ErrorStateProps {
  error: Error | null;
  refetch: () => void;
  isRefetching: boolean;
}
function ErrorState({ error, refetch, isRefetching }: ErrorStateProps) {
  const t = useTranslations('checkout');
  return (
    <div className="container mx-auto px-4 py-16 flex items-center justify-center">
      <Card className="max-w-md text-center">
        <CardHeader>
          <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900 mb-4">
            <RefreshCw className="h-8 w-8 text-red-600 dark:text-red-400" />
          </div>
          <CardTitle>{t('orderNotFound')}</CardTitle>
          <CardDescription>
            {error?.message || t('unableToLoadOrder')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={() => refetch()} disabled={isRefetching} variant="outline">
            {isRefetching && <RefreshCw className="h-4 w-4 animate-spin mr-2" />}
            {t('tryAgain')}
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}

// ========== Redesigned UI Sub-components ========== //

const SuccessHeader: FC<{ orderId: string; customerName?: string }> = ({ orderId, customerName }) => {
  const t = useTranslations('checkout');
  const greeting = customerName ? t('thankYouCustomer', { name: customerName }) : t('thankYou');

  return (
    <div className="text-center p-8 rounded-lg bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 mb-8">
      <CheckCircle className="h-16 w-16 text-green-600 dark:text-green-400 mx-auto mb-4" />
      <h1 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-2">{greeting}</h1>
      <p className="text-muted-foreground text-lg">{t('orderPlacedSuccessfully')}</p>
      <p className="text-muted-foreground mt-4">
        {t('orderNumber')}:{' '}
        <span className="font-semibold text-gray-900 dark:text-white tracking-wider bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
          #{orderId.slice(0, 8).toUpperCase()}
        </span>
      </p>
    </div>
  );
};

const OrderItems: FC<{
  items: OrderItem[];
  currency: string;
  onReorderItem: (item: OrderItem) => void;
  onReorderAll: () => void;
  isReorderingAll: boolean;
  isReorderingItem: boolean;
}> = ({ items, currency, onReorderItem, onReorderAll, isReorderingAll, isReorderingItem }) => {
  const t = useTranslations('checkout');

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>{t('orderItems')}</CardTitle>
          <CardDescription>{t('itemsInYourOrder', { count: items.length })}</CardDescription>
        </div>
        <Button onClick={onReorderAll} disabled={isReorderingAll} size="sm" variant="outline">
          {isReorderingAll && <RefreshCw className="h-4 w-4 animate-spin mr-2" />}
          {t('reorderAll')}
        </Button>
      </CardHeader>
      <CardContent>
        <ul className="divide-y divide-gray-200 dark:divide-gray-800">
          {items.map((item) => {
            const translation = item.product?.translations[0];
            const productName = translation?.name || item.product?.original_name || 'Product';
            const imageUrl = item.image_url || item.product?.product_images[0]?.image_url;
            const variantTranslation = item.variant?.translations[0];
            const variantName = variantTranslation?.variant_name || item.variant?.original_variant_name;

            return (
              <li key={item.id} className="py-4 flex flex-col sm:flex-row gap-4">
                <div className="relative w-24 h-24 flex-shrink-0 rounded-md overflow-hidden bg-muted border">
                  <ImageWithFallback src={imageUrl} alt={productName} className="object-cover" sizes="96px" fallbackText="No Image" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold">{productName}</h3>
                  {item.variant_id && <p className="text-sm text-muted-foreground">{variantName || t('variantNotSpecified')}</p>}
                  <p className="text-sm text-muted-foreground mt-1">
                    {t('quantity')}: {item.quantity.toString()}
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    {formatCurrency(Number(item.price_per_unit), currency)} each
                  </p>
                </div>
                <div className="flex flex-col items-start sm:items-end justify-between">
                  <p className="font-semibold text-base">
                    {formatCurrency(Number(item.price_per_unit) * Number(item.quantity), currency)}
                  </p>
                  <Button onClick={() => onReorderItem(item)} disabled={isReorderingItem} size="sm" variant="ghost" className="mt-2 sm:mt-0">
                    {isReorderingItem ? <RefreshCw className="h-3 w-3 animate-spin mr-1" /> : null}
                    {t('reorder')}
                  </Button>
                </div>
              </li>
            );
          })}
        </ul>
      </CardContent>
    </Card>
  );
};

const WhatNext: FC = () => {
  const t = useTranslations('checkout');
  const steps = [
    { title: t('step1Title'), description: t('step1Description') },
    { title: t('step2Title'), description: t('step2Description') },
    { title: t('step3Title'), description: t('step3Description') },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('whatNext')}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="relative">
          <div className="absolute left-4 top-4 h-full border-l-2 border-dashed border-primary/50" />
          <ul className="space-y-6">
            {steps.map((step, index) => (
              <li key={step.title} className="flex items-start gap-4 relative">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground font-bold z-10">
                  {index + 1}
                </div>
                <div>
                  <h4 className="font-semibold">{step.title}</h4>
                  <p className="text-sm text-muted-foreground">{step.description}</p>
                </div>
              </li>
            ))}
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

const OrderSummary: FC<{ order: NonNullable<ReturnType<typeof useOrderDetails>['data']> }> = ({ order }) => {
  const t = useTranslations('checkout');
  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('orderSummary')}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex justify-between text-sm">
          <span className="text-muted-foreground">{t('items')} ({order.order_items.length})</span>
          <span>{formatCurrency(Number(order.total_amount), order.currency)}</span>
        </div>
        <div className="flex justify-between text-sm">
          <span className="text-muted-foreground">{t('shipping')}</span>
          <span className="font-medium text-blue-600 dark:text-blue-400">{t('shippingCostMessage')}</span>
        </div>
        <div className="border-t border-dashed my-2" />
        <div className="flex justify-between font-bold text-lg">
          <span>{t('total')}</span>
          <span>{formatCurrency(Number(order.total_amount), order.currency)}</span>
        </div>
      </CardContent>
    </Card>
  );
};

const ShippingInfo: FC<{ order: NonNullable<ReturnType<typeof useOrderDetails>['data']> }> = ({ order }) => {
  const t = useTranslations('checkout');
  const address = order.shipping_address as unknown as Address;
  const ShippingIcon = order.shipping_method === 'boat' ? Ship : Plane;
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('shippingInformation')}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4 text-sm">
        <div>
          <h4 className="font-semibold mb-2 flex items-center gap-2"><MapPin className="h-4 w-4" />{t('shippingAddress')}</h4>
          <div className="pl-6 border-l ml-2 space-y-1 text-muted-foreground">
            {address?.fullName && <p className="font-medium text-foreground flex items-center gap-2"><User className="h-4 w-4" />{address.fullName}</p>}
            {address?.phone && <p className="flex items-center gap-2"><Phone className="h-4 w-4" />{address.phone}</p>}
            {address?.isWhatsApp && <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">WhatsApp Available</span>}
          </div>
        </div>
        <div>
          <h4 className="font-semibold mb-2 flex items-center gap-2"><ShippingIcon className="h-4 w-4" />{t('shippingMethod')}</h4>
          <div className="pl-6 border-l ml-2 text-muted-foreground">
            <p>{order.shipping_method === 'boat' ? t('shippingBoat') : t('shippingPlane')}</p>
            <p className="text-xs">{order.shipping_method === 'boat' ? `(${t('shippingBoatTime')})` : `(${t('shippingPlaneTime')})`}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// ========== Main Client Component ========== //

interface OrderSuccessClientProps {
  orderId: string;
  locale: string;
}

export function OrderSuccessClient({ orderId, locale }: OrderSuccessClientProps) {
  const t = useTranslations('checkout');
  const { showToast } = useToast();
  const addToCart = useAddToCart();
  const batchCartOps = useBatchCartOperations();
  const prefetchOrderDetails = usePrefetchOrderDetails();
  const { trackCartOperation } = useCartAnalytics({ trackPerformance: true });

  const { data: order, isLoading, error, refetch, isRefetching } = useOrderDetails(orderId, true);

  const prefetchHandlers = prefetchOrderDetails.prefetchOnInteraction(orderId, locale);

  useEffect(() => {
    const clearCart = async () => {
      try {
        await clearBuyNowCart();
        trackCartOperation('clear_cart', { success: true });
      } catch (error) {
        console.error('Failed to clear buy now cart:', error);
        trackCartOperation('clear_cart', {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    };
    clearCart();
  }, [trackCartOperation]);

  const handleReorderAll = async () => {
    if (!order?.order_items?.length) return;
    const operations = order.order_items.map((item) => ({
      type: 'add' as const,
      productId: item.product_id,
      variantId: item.variant_id ? Number(item.variant_id) : undefined,
      quantity: Number(item.quantity),
      imageUrl: item.image_url || undefined,
    }));

    try {
      await batchCartOps.batchOperationsAsync(operations);
      trackCartOperation('add_item', { quantity: operations.length, success: true });
      showToast('success', t('itemsAddedToCart'));
    } catch (error) {
      trackCartOperation('add_item', {
        quantity: operations.length,
        success: false,
        error: error instanceof Error ? error.message : 'Batch add failed',
      });
      showToast('error', t('failedToAddItems'));
    }
  };

  const handleReorderItem = async (item: OrderItem) => {
    try {
      await addToCart.mutateAsync({
        productId: item.product_id,
        variantId: item.variant_id ? Number(item.variant_id) : undefined,
        quantity: Number(item.quantity),
        imageUrl: item.image_url || undefined,
      });
      trackCartOperation('add_item', { productId: item.product_id, success: true });
      showToast('success', t('itemAddedToCart'));
    } catch (error) {
      trackCartOperation('add_item', { productId: item.product_id, success: false, error: error instanceof Error ? error.message : 'Add failed' });
      showToast('error', t('failedToAddItem'));
    }
  };

  if (isLoading) return <OrderSuccessSkeleton />;
  if (error || !order) return <ErrorState error={error} refetch={refetch} isRefetching={isRefetching} />;

  const customerName = (order.shipping_address as unknown as Address)?.fullName?.split(' ')[0];

  return (
    <div className="bg-gray-50 dark:bg-gray-950">
      <div className="container mx-auto px-4 py-12 md:py-16">
        <SuccessHeader orderId={order.id} customerName={customerName} />

        <div className="grid grid-cols-1 lg:grid-cols-3 lg:gap-8">
          {/* Main Content (Left Column on Desktop) */}
          <main className="lg:col-span-2 space-y-6">
            <OrderItems
              items={order.order_items}
              currency={order.currency}
              onReorderItem={handleReorderItem}
              onReorderAll={handleReorderAll}
              isReorderingAll={batchCartOps.isPending}
              isReorderingItem={addToCart.isPending}
            />
            <WhatNext />
          </main>
          
          {/* Sidebar (Right Column on Desktop) */}
          <aside className="space-y-6 mt-6 lg:mt-0">
            <OrderSummary order={order} />
            <ShippingInfo order={order} />
            
            {/* Action Buttons */}
            <div className="space-y-3">
              <Link href={`/${locale}/account/orders/${order.id}`} {...prefetchHandlers}>
                <Button variant="outline" className="w-full mb-2">{t('viewOrder')}</Button>
              </Link>
              <Link href={`/${locale}/products`}>
                <Button className="w-full">{t('continueShopping')}</Button>
              </Link>
            </div>
          </aside>
        </div>
      </div>
    </div>
  );
}