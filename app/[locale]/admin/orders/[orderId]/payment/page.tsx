// app/[locale]/admin/orders/[orderId]/payment/page.tsx
// Payment recording page for admin

import { notFound } from 'next/navigation';
import { getTranslations } from 'next-intl/server';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { PaymentRecordForm } from '@/components/admin/PaymentRecordForm';
import { getOrderByIdAdmin } from '@/lib/actions/admin/order.actions';

interface PageProps {
  params: Promise<{ locale: string; orderId: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  return {
    title: t('recordPayment'),
  };
}

export default async function PaymentRecordPage({ params }: PageProps) {
  const { locale, orderId } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  const order = await getOrderByIdAdmin(orderId);

  if ('error' in order) {
    notFound();
  }

  // Check if payment already exists
  const hasPayment = order.payments && order.payments.length > 0;

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <Link href={`/${locale}/admin/orders/${orderId}`}>
        <Button variant="ghost" size="sm">
          <ArrowLeft className="h-4 w-4 mr-2" />
          {t('backToOrder')}
        </Button>
      </Link>

      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold">{t('recordPayment')}</h1>
        <p className="text-muted-foreground mt-2">
          {t('recordPaymentDescription')}
        </p>
      </div>

      {/* Warning if payment already exists */}
      {hasPayment && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="font-medium text-yellow-800">
            {t('paymentAlreadyExists')}
          </h3>
          <p className="text-sm text-yellow-700 mt-1">
            {t('paymentAlreadyExistsDescription')}
          </p>
          <Link href={`/${locale}/admin/payments/${order.payments[0].id}`}>
            <Button variant="outline" size="sm" className="mt-2">
              {t('viewExistingPayment')}
            </Button>
          </Link>
        </div>
      )}

      {/* Payment Record Form */}
      {!hasPayment && (
        <PaymentRecordForm
          orderId={order.id}
          orderTotal={Number(order.total_amount)}
          orderCurrency={order.currency}
          locale={locale}
        />
      )}
    </div>
  );
}