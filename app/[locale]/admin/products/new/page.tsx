// app/[locale]/admin/products/new/page.tsx
// Admin new product creation page

import { getTranslations } from 'next-intl/server';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ProductForm } from '@/components/admin/ProductForm';
import { checkPermission } from '@/lib/auth/permissions';
import { getCurrentUser } from '@/lib/actions/auth.actions';
import { PermissionAction } from '@/app/generated/prisma';
import { redirect } from 'next/navigation';

interface PageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  return {
    title: t('createProduct'),
  };
}

export default async function AdminNewProductPage({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  // Check permissions
  const user = await getCurrentUser();
  if (!user) {
    redirect(`/${locale}/login?redirect=/${locale}/admin/products/new`);
  }

  const canCreate = await checkPermission(user.uid, PermissionAction.PRODUCT_CREATE);
  if (!canCreate) {
    redirect(`/${locale}/admin/products`);
  }

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <Link href={`/${locale}/admin/products`}>
        <Button variant="ghost" size="sm">
          <ArrowLeft className="h-4 w-4 mr-2" />
          {t('backToProducts')}
        </Button>
      </Link>

      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold">{t('createProduct')}</h1>
        <p className="text-muted-foreground mt-2">
          {t('createProductDescription')}
        </p>
      </div>

      {/* Product Form */}
      <Card>
        <CardHeader>
          <CardTitle>{t('productDetails')}</CardTitle>
        </CardHeader>
        <CardContent>
          <ProductForm
            mode="create"
            locale={locale}
          />
        </CardContent>
      </Card>
    </div>
  );
}
