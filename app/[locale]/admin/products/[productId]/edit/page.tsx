// app/[locale]/admin/products/[productId]/edit/page.tsx
// Admin product edit page

import { notFound, redirect } from 'next/navigation';
import { getTranslations } from 'next-intl/server';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ProductForm } from '@/components/admin/ProductForm';
import { getProductByIdAdmin } from '@/lib/actions/admin/product.actions';
import { checkPermission } from '@/lib/auth/permissions';
import { getCurrentUser } from '@/lib/actions/auth.actions';
import { PermissionAction } from '@/app/generated/prisma';

interface PageProps {
  params: Promise<{ locale: string; productId: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale, productId } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  const product = await getProductByIdAdmin(parseInt(productId));
  
  if ('error' in product) {
    return {
      title: t('productNotFound'),
    };
  }

  const translation = product.translations.find(t => t.language_code === locale) || 
                     product.translations.find(t => t.language_code === 'en') ||
                     product.translations[0];

  return {
    title: `${t('edit')} - ${translation?.name || product.original_name || t('product')}`,
  };
}

export default async function AdminProductEditPage({ params }: PageProps) {
  const { locale, productId } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  // Check permissions
  const user = await getCurrentUser();
  if (!user) {
    redirect(`/${locale}/login?redirect=/${locale}/admin/products/${productId}/edit`);
  }

  const canUpdate = await checkPermission(user.uid, PermissionAction.PRODUCT_UPDATE);
  if (!canUpdate) {
    redirect(`/${locale}/admin/products/${productId}`);
  }

  const product = await getProductByIdAdmin(parseInt(productId));

  if ('error' in product) {
    notFound();
  }

  const translation = product.translations.find(t => t.language_code === locale) || 
                     product.translations.find(t => t.language_code === 'en') ||
                     product.translations[0];

  const productName = translation?.name || product.original_name || t('unnamedProduct');

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <Link href={`/${locale}/admin/products/${productId}`}>
        <Button variant="ghost" size="sm">
          <ArrowLeft className="h-4 w-4 mr-2" />
          {t('backToProduct')}
        </Button>
      </Link>

      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold">{t('editProduct')}</h1>
        <p className="text-muted-foreground mt-2">
          {t('editing')}: {productName}
        </p>
      </div>

      {/* Product Form */}
      <Card>
        <CardHeader>
          <CardTitle>{t('productDetails')}</CardTitle>
        </CardHeader>
        <CardContent>
          <ProductForm
            mode="edit"
            product={product}
            locale={locale}
          />
        </CardContent>
      </Card>
    </div>
  );
}
