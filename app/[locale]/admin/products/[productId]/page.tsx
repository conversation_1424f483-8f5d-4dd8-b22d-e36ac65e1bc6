// app/[locale]/admin/products/[productId]/page.tsx
// Admin product detail page with full CRUD operations

import { notFound } from 'next/navigation';
import { getTranslations } from 'next-intl/server';
import Link from 'next/link';
import { ArrowLeft, Edit, Trash2, Plus, Eye, EyeOff } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { getProductByIdAdmin } from '@/lib/actions/admin/product.actions';
import { checkPermission } from '@/lib/auth/permissions';
import { getCurrentUser } from '@/lib/actions/auth.actions';
import { PermissionAction } from '@/app/generated/prisma';
import { formatCurrency, formatDate } from '@/lib/utils';
import { ProductVisibilityToggle } from '@/components/admin/ProductVisibilityToggle';
import { ProductImagesManager } from '@/components/admin/ProductImagesManager';
import { ProductVariantsManager } from '@/components/admin/ProductVariantsManager';
import { ProductAttributesManager } from '@/components/admin/ProductAttributesManager';
import { ProductOffersManager } from '@/components/admin/ProductOffersManager';
import { ProductMediaDisplay } from '@/components/admin/ProductMediaDisplay';

interface PageProps {
  params: Promise<{ locale: string; productId: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale, productId } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  const product = await getProductByIdAdmin(parseInt(productId));

  if ('error' in product) {
    return {
      title: t('productNotFound'),
    };
  }

  const translation = product.translations.find(t => t.language_code === locale) ||
                     product.translations.find(t => t.language_code === 'en') ||
                     product.translations[0];

  return {
    title: translation?.name || product.original_name || t('productDetails'),
  };
}

export default async function AdminProductDetailPage({ params }: PageProps) {
  const { locale, productId } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  const product = await getProductByIdAdmin(parseInt(productId));

  if ('error' in product) {
    notFound();
  }

  // Check permissions
  const user = await getCurrentUser();
  const canUpdate = user ? await checkPermission(user.uid, PermissionAction.PRODUCT_UPDATE) : false;
  const canDelete = user ? await checkPermission(user.uid, PermissionAction.PRODUCT_DELETE) : false;
  const canManageImages = user ? await checkPermission(user.uid, PermissionAction.PRODUCT_MANAGE_IMAGES) : false;
  const canManageVariants = user ? await checkPermission(user.uid, PermissionAction.PRODUCT_MANAGE_VARIANTS) : false;
  const canManageCategories = user ? await checkPermission(user.uid, PermissionAction.PRODUCT_MANAGE_CATEGORIES) : false;

  // Get current locale translation or fallback
  const translation = product.translations.find(t => t.language_code === locale) ||
                      product.translations.find(t => t.language_code === 'en') ||
                      product.translations[0];

  const productName = translation?.name || product.original_name || t('unnamedProduct');
  const previewImage = product.product_images.find(img => img.image_type === 'preview') ||
                       product.product_images[0];

  // Serialize Decimal objects for client components
  const serializedVariants = product.variants.map(variant => ({
    ...variant,
    available_quantity: variant.available_quantity ? Number(variant.available_quantity) : null,
    min_quantity: variant.min_quantity ? Number(variant.min_quantity) : null,
    price_low: Number(variant.price_low),
    price_high: variant.price_high ? Number(variant.price_high) : null,
  }));

  const serializedOffers = product.offers.map(offer => ({
    ...offer,
    min_quantity: Number(offer.min_quantity),
    price_low: Number(offer.price_low),
    price_high: offer.price_high ? Number(offer.price_high) : null,
    quantity_info: offer.quantity_info,
    original_offer_name: '',
    original_name: '',
  }));

  const serializedAttributes = product.product_attributes.map(attribute => ({
    ...attribute,
    id: Number(attribute.id),
    translations: attribute.translations.map(translation => ({
      ...translation,
      id: Number(translation.id),
      attributes_id: Number(translation.attributes_id),
    })),
  }));

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <Link href={`/${locale}/admin/products`}>
        <Button variant="ghost" size="sm">
          <ArrowLeft className="h-4 w-4 mr-2" />
          {t('backToProducts')}
        </Button>
      </Link>

      {/* Page Header */}
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-4">
          {previewImage && (
            <div className="w-20 h-20 rounded-lg overflow-hidden bg-muted">
              <img
                src={previewImage.image_url}
                alt={productName}
                className="w-full h-full object-cover"
              />
            </div>
          )}
          <div>
            <h1 className="text-3xl font-bold">{productName}</h1>
            <div className="flex items-center space-x-4 mt-2">
              <Badge variant={product.marketplace === 'alibaba' ? 'default' : 'secondary'}>
                {product.marketplace}
              </Badge>
              <Badge variant={product.can_show ? 'default' : 'secondary'}>
                {product.can_show ? (
                  <>
                    <Eye className="h-3 w-3 mr-1" />
                    {t('visible')}
                  </>
                ) : (
                  <>
                    <EyeOff className="h-3 w-3 mr-1" />
                    {t('hidden')}
                  </>
                )}
              </Badge>
              <span className="text-sm text-muted-foreground">
                {t('created')}: {formatDate(product.created)}
              </span>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {canUpdate && (
            <ProductVisibilityToggle
              productId={product.id}
              currentVisibility={product.can_show || false}
              locale={locale}
            />
          )}
          {canUpdate && (
            <Link href={`/${locale}/admin/products/${product.id}/edit`}>
              <Button variant="outline" size="sm">
                <Edit className="h-4 w-4 mr-2" />
                {t('edit')}
              </Button>
            </Link>
          )}
          {canDelete && (
            <Button variant="destructive" size="sm">
              <Trash2 className="h-4 w-4 mr-2" />
              {t('delete')}
            </Button>
          )}
        </div>
      </div>

      {/* Product Details Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">{t('overview')}</TabsTrigger>
          <TabsTrigger value="translations">{t('translations')}</TabsTrigger>
          <TabsTrigger value="images">{t('images')}</TabsTrigger>
          <TabsTrigger value="variants">{t('variants')}</TabsTrigger>
          <TabsTrigger value="attributes">{t('attributes')}</TabsTrigger>
          <TabsTrigger value="pricing">{t('pricing')}</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Basic Information */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>{t('basicInformation')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">{t('originalName')}</p>
                    <p className="font-medium">{product.original_name || t('notSet')}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">{t('marketplace')}</p>
                    <Badge variant="outline">{product.marketplace}</Badge>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">{t('productUrl')}</p>
                    <a
                      href={product.product_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:underline text-sm break-all"
                    >
                      {product.product_url}
                    </a>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">{t('visibility')}</p>
                    <Badge variant={product.can_show ? 'default' : 'secondary'}>
                      {product.can_show ? t('visible') : t('hidden')}
                    </Badge>
                  </div>
                  {product.weight && (
                    <>
                      <div>
                        <p className="text-sm text-muted-foreground">{t('weight')}</p>
                        <p className="font-medium">
                          {Number(product.weight)} {product.weight_unit || 'g'}
                        </p>
                      </div>
                    </>
                  )}
                  {product.min_price_cny && (
                    <div>
                      <p className="text-sm text-muted-foreground">{t('minPrice')}</p>
                      <p className="font-medium">
                        {formatCurrency(Number(product.min_price_cny), 'CNY')}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Categories */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>{t('categories')}</CardTitle>
                {canManageCategories && (
                  <Button variant="outline" size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    {t('manage')}
                  </Button>
                )}
              </CardHeader>
              <CardContent>
                {product.categories.length === 0 ? (
                  <p className="text-muted-foreground text-sm">{t('noCategories')}</p>
                ) : (
                  <div className="space-y-2">
                    {product.categories.map((pc) => {
                      const categoryTranslation = pc.category.translations.find(
                        t => t.language_code === locale
                      ) || pc.category.translations.find(
                        t => t.language_code === 'en'
                      ) || pc.category.translations[0];

                      return (
                        <Badge key={pc.category.id} variant="outline">
                          {categoryTranslation?.name || `Category ${pc.category.id}`}
                        </Badge>
                      );
                    })}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="text-center">
                  <p className="text-2xl font-bold">{product.translations.length}</p>
                  <p className="text-sm text-muted-foreground">{t('translations')}</p>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-center">
                  <p className="text-2xl font-bold">{product.product_images.length}</p>
                  <p className="text-sm text-muted-foreground">{t('images')}</p>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-center">
                  <p className="text-2xl font-bold">{product.variants.length}</p>
                  <p className="text-sm text-muted-foreground">{t('variants')}</p>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-center">
                  <p className="text-2xl font-bold">{product.offers.length}</p>
                  <p className="text-sm text-muted-foreground">{t('offers')}</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Translations Tab */}
        <TabsContent value="translations">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>{t('productTranslations')}</CardTitle>
              {canUpdate && (
                <Button variant="outline" size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  {t('addTranslation')}
                </Button>
              )}
            </CardHeader>
            <CardContent>
              {product.translations.length === 0 ? (
                <p className="text-muted-foreground text-center py-8">{t('noTranslations')}</p>
              ) : (
                <div className="space-y-4">
                  {product.translations.map((translation) => (
                    <div key={translation.id.toString()} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <Badge variant="outline">{translation.language_code}</Badge>
                        {canUpdate && (
                          <Button variant="ghost" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                      <div className="space-y-2">
                        <div>
                          <p className="text-sm text-muted-foreground">{t('name')}</p>
                          <p className="font-medium">{translation.name}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">{t('slug')}</p>
                          <p className="text-sm font-mono">{translation.slug}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Images Tab */}
        <TabsContent value="images">
          {canManageImages ? (
            <ProductImagesManager
              productId={product.id}
              images={product.product_images}
              locale={locale}
            />
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>{t('productImages')}</CardTitle>
              </CardHeader>
              <CardContent>
                {product.product_images.length === 0 ? (
                  <p className="text-muted-foreground text-center py-8">{t('noImages')}</p>
                ) : (
                  <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                    {product.product_images.map((image) => (
                       <div key={image.id.toString()} className="space-y-2">
                         <div className={`${image.image_type === 'video' ? 'aspect-video' : 'aspect-square'} rounded-lg overflow-hidden bg-muted`}>
                           <ProductMediaDisplay
                             src={image.image_url}
                             alt={`Product image ${image.id}`}
                             type={image.image_type}
                             className="w-full h-full object-cover"
                           />
                         </div>
                         <Badge variant="outline" className="text-xs">
                           {image.image_type}
                         </Badge>
                       </div>
                     ))}
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Variants Tab */}
        <TabsContent value="variants">
          {canManageVariants ? (
            <ProductVariantsManager
              productId={product.id}
              variants={serializedVariants}
              locale={locale}
            />
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>{t('productVariants')}</CardTitle>
              </CardHeader>
              <CardContent>
                {product.variants.length === 0 ? (
                  <p className="text-muted-foreground text-center py-8">{t('noVariants')}</p>
                ) : (
                  <div className="space-y-4">
                    {product.variants.map((variant) => {
                      const variantTranslation = variant.translations.find(
                        t => t.language_code === locale
                      ) || variant.translations.find(
                        t => t.language_code === 'en'
                      ) || variant.translations[0];

                      return (
                        <div key={variant.id.toString()} className="border rounded-lg p-4">
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div>
                              <p className="text-sm text-muted-foreground">{t('variantName')}</p>
                              <p className="font-medium">
                                {variantTranslation?.variant_name || variant.original_variant_name}
                              </p>
                            </div>
                            <div>
                              <p className="text-sm text-muted-foreground">{t('variantType')}</p>
                              <p className="font-medium">
                                {variantTranslation?.variant_type || variant.original_variant_type}
                              </p>
                            </div>
                            <div>
                              <p className="text-sm text-muted-foreground">{t('price')}</p>
                              <p className="font-medium">
                                {formatCurrency(Number(variant.price_low), variant.currency)}
                                {variant.price_high && Number(variant.price_high) !== Number(variant.price_low) && (
                                  <span> - {formatCurrency(Number(variant.price_high), variant.currency)}</span>
                                )}
                              </p>
                            </div>
                            <div>
                              <p className="text-sm text-muted-foreground">{t('quantity')}</p>
                              <p className="font-medium">
                                {variant.available_quantity ? Number(variant.available_quantity) : t('unlimited')}
                              </p>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Attributes Tab */}
        <TabsContent value="attributes">
          <ProductAttributesManager
            productId={product.id}
            attributes={serializedAttributes}
            locale={locale}
          />
        </TabsContent>

        {/* Pricing Tab */}
        <TabsContent value="pricing">
          <ProductOffersManager
            productId={product.id}
            offers={serializedOffers}
            locale={locale}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
