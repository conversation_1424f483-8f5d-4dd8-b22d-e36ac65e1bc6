// app/[locale]/admin/products/page.tsx
// Admin products list page

export const dynamic = 'force-dynamic';

import { getTranslations } from 'next-intl/server';
import Link from 'next/link';
import { Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ProductsTable } from '@/components/admin/ProductsTable';
import { getAllProductsAdmin } from '@/lib/actions/admin/product.actions';
import { checkPermission } from '@/lib/auth/permissions';
import { getCurrentUser } from '@/lib/actions/auth.actions';
import { PermissionAction } from '@/app/generated/prisma';

interface PageProps {
  params: Promise<{ locale: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  return {
    title: t('productManagement'),
  };
}

export default async function AdminProductsPage({ params, searchParams }: PageProps) {
  const { locale } = await params;
  const resolvedSearchParams = await searchParams;
  const t = await getTranslations({ locale, namespace: 'admin' });

  const search = typeof resolvedSearchParams.search === 'string' ? resolvedSearchParams.search : undefined;
  const category = typeof resolvedSearchParams.category === 'string' ? resolvedSearchParams.category : undefined;
  const cursor = typeof resolvedSearchParams.cursor === 'string' ? resolvedSearchParams.cursor : undefined;

  const productsData = await getAllProductsAdmin({ search, category, cursor });

  if ('error' in productsData) {
    return (
      <div className="text-center py-12">
        <p className="text-destructive">{productsData.error}</p>
      </div>
    );
  }

  // Check if user can create products
  const user = await getCurrentUser();
  const canCreate = user ? await checkPermission(user.uid, PermissionAction.PRODUCT_CREATE) : false;

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">{t('productManagement')}</h1>
          <p className="text-muted-foreground mt-2">
            Manage your product catalog
          </p>
        </div>
        {canCreate && (
          <Link href={`/${locale}/admin/products/new`}>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {t('createProduct')}
            </Button>
          </Link>
        )}
      </div>

      {/* Products Table */}
      <ProductsTable
        products={productsData.data}
        total={productsData.data.length}
        page={1}
        totalPages={productsData.hasMore ? 2 : 1}
        locale={locale}
      />
    </div>
  );
}
