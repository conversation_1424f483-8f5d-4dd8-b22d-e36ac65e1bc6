// app/[locale]/admin/categories/new/page.tsx
// Admin new category creation page

import { getTranslations } from 'next-intl/server';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CategoryForm } from '@/components/admin/CategoryForm';
import { getAllCategories } from '@/lib/actions/admin/category.actions';
import { checkPermission } from '@/lib/auth/permissions';
import { getCurrentUser } from '@/lib/actions/auth.actions';
import { PermissionAction } from '@/app/generated/prisma';
import { redirect } from 'next/navigation';

interface PageProps {
  params: Promise<{ locale: string }>;
  searchParams: Promise<{ parent?: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  return {
    title: t('createCategory'),
  };
}

export default async function AdminNewCategoryPage({ params, searchParams }: PageProps) {
  const { locale } = await params;
  const { parent } = await searchParams;
  const t = await getTranslations({ locale, namespace: 'admin' });

  // Check permissions
  const user = await getCurrentUser();
  if (!user) {
    redirect(`/${locale}/login?redirect=/${locale}/admin/categories/new`);
  }

  const canCreate = await checkPermission(user.uid, PermissionAction.PRODUCT_MANAGE_CATEGORIES);
  if (!canCreate) {
    redirect(`/${locale}/admin/categories`);
  }

  // Get all categories for parent selection
  const categoriesData = await getAllCategories();
  const categories = 'error' in categoriesData ? [] : categoriesData;

  // Find parent category if specified
  const parentCategory = parent ? categories.find(c => c.id === parseInt(parent)) : undefined;

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <Link href={`/${locale}/admin/categories`}>
        <Button variant="ghost" size="sm">
          <ArrowLeft className="h-4 w-4 mr-2" />
          {t('backToCategories')}
        </Button>
      </Link>

      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold">
          {parentCategory ? t('createSubcategory') : t('createCategory')}
        </h1>
        <p className="text-muted-foreground mt-2">
          {parentCategory 
            ? t('createSubcategoryDescription', { 
                parent: parentCategory.translations.find(t => t.language_code === locale)?.name || 
                       parentCategory.translations[0]?.name || 
                       `Category ${parentCategory.id}`
              })
            : t('createCategoryDescription')
          }
        </p>
      </div>

      {/* Category Form */}
      <Card>
        <CardHeader>
          <CardTitle>{t('categoryDetails')}</CardTitle>
        </CardHeader>
        <CardContent>
          <CategoryForm
            mode="create"
            parentId={parent ? parseInt(parent) : undefined}
            availableParents={categories}
            locale={locale}
          />
        </CardContent>
      </Card>
    </div>
  );
}
