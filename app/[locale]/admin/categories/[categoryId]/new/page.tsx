// app/[locale]/admin/categories/[categoryId]/new/page.tsx
// Admin new subcategory creation page

import { notFound, redirect } from 'next/navigation';
import { getTranslations } from 'next-intl/server';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CategoryForm } from '@/components/admin/CategoryForm';
import { getCategoryById, getAllCategories } from '@/lib/actions/admin/category.actions';
import { checkPermission } from '@/lib/auth/permissions';
import { getCurrentUser } from '@/lib/actions/auth.actions';
import { PermissionAction } from '@/app/generated/prisma';

interface PageProps {
  params: Promise<{ locale: string; categoryId: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale, categoryId } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  const category = await getCategoryById(parseInt(categoryId));
  
  if ('error' in category) {
    return {
      title: t('categoryNotFound'),
    };
  }

  const translation = category.translations.find(t => t.language_code === locale) || 
                     category.translations.find(t => t.language_code === 'en') ||
                     category.translations[0];

  return {
    title: `${t('createSubcategory')} - ${translation?.name || t('category')}`,
  };
}

export default async function AdminNewSubcategoryPage({ params }: PageProps) {
  const { locale, categoryId } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  // Check permissions
  const user = await getCurrentUser();
  if (!user) {
    redirect(`/${locale}/login?redirect=/${locale}/admin/categories/${categoryId}/new`);
  }

  const canCreate = await checkPermission(user.uid, PermissionAction.PRODUCT_MANAGE_CATEGORIES);
  if (!canCreate) {
    redirect(`/${locale}/admin/categories/${categoryId}`);
  }

  const parentCategory = await getCategoryById(parseInt(categoryId));

  if ('error' in parentCategory) {
    notFound();
  }

  // Get all categories for parent selection
  const categoriesData = await getAllCategories();
  const categories = 'error' in categoriesData ? [] : categoriesData;

  const translation = parentCategory.translations.find(t => t.language_code === locale) || 
                     parentCategory.translations.find(t => t.language_code === 'en') ||
                     parentCategory.translations[0];

  const parentName = translation?.name || t('unnamedCategory');

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <Link href={`/${locale}/admin/categories/${categoryId}`}>
        <Button variant="ghost" size="sm">
          <ArrowLeft className="h-4 w-4 mr-2" />
          {t('backToCategory')}
        </Button>
      </Link>

      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold">{t('createSubcategory')}</h1>
        <p className="text-muted-foreground mt-2">
          {t('createSubcategoryDescription', { parent: parentName })}
        </p>
      </div>

      {/* Category Form */}
      <Card>
        <CardHeader>
          <CardTitle>{t('subcategoryDetails')}</CardTitle>
        </CardHeader>
        <CardContent>
          <CategoryForm
            mode="create"
            parentId={parseInt(categoryId)}
            availableParents={categories}
            locale={locale}
          />
        </CardContent>
      </Card>
    </div>
  );
}
