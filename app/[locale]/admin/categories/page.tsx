// app/[locale]/admin/categories/page.tsx
// Admin categories management page

export const dynamic = 'force-dynamic';

import { getTranslations } from 'next-intl/server';
import Link from 'next/link';
import { Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { CategoriesTable } from '@/components/admin/CategoriesTable';
import { getAllCategories } from '@/lib/actions/admin/category.actions';
import { checkPermission } from '@/lib/auth/permissions';
import { getCurrentUser } from '@/lib/actions/auth.actions';
import { PermissionAction } from '@/app/generated/prisma';

interface PageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  return {
    title: t('categoryManagement'),
  };
}

export default async function AdminCategoriesPage({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  const categoriesData = await getAllCategories();

  if ('error' in categoriesData) {
    return (
      <div className="text-center py-12">
        <p className="text-destructive">{categoriesData.error}</p>
      </div>
    );
  }

  // Check if user can create categories
  const user = await getCurrentUser();
  const canCreate = user ? await checkPermission(user.uid, PermissionAction.PRODUCT_MANAGE_CATEGORIES) : false;

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">{t('categoryManagement')}</h1>
          <p className="text-muted-foreground mt-2">
            {t('manageCategoriesDescription')}
          </p>
        </div>
        {canCreate && (
          <Link href={`/${locale}/admin/categories/new`}>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {t('createCategory')}
            </Button>
          </Link>
        )}
      </div>

      {/* Categories Table */}
      <CategoriesTable
        categories={categoriesData}
        locale={locale}
      />
    </div>
  );
}
