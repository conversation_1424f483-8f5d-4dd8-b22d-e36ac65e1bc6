// app/[locale]/admin/layout.tsx
// Admin layout with RBAC protection - overrides root layout

import { redirect } from 'next/navigation';
import { AdminAuthWrapper } from '@/components/admin/AdminAuthWrapper';

const locales = ['en', 'fr', 'ar'];

interface LayoutProps {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}

export default async function AdminLayout({ children, params }: LayoutProps) {
  const { locale } = await params;

  // Validate locale
  if (!locales.includes(locale)) {
    redirect('/en/admin');
  }

  return (
    <AdminAuthWrapper locale={locale}>
      {children}
    </AdminAuthWrapper>
  );
}

