// app/[locale]/admin/customers/[customerId]/page.tsx
// Admin customer detail page with role management

import { notFound } from 'next/navigation';
import { getTranslations } from 'next-intl/server';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { RoleAssignmentForm } from '@/components/admin/RoleAssignmentForm';
import { getCustomerByIdAdmin, getAllRoles } from '@/lib/actions/admin/customer.actions';
import { formatDate, formatCurrency } from '@/lib/utils';
import { ORDER_STATUS_CONFIG } from '@/lib/constants';

interface PageProps {
  params: Promise<{ locale: string; customerId: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  return {
    title: t('customerDetails'),
  };
}

export default async function AdminCustomerDetailPage({ params }: PageProps) {
  const { locale, customerId } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  // Convert customerId to number
  const customerIdNum = parseInt(customerId, 10);

  if (isNaN(customerIdNum)) {
    notFound();
  }

  const [customer, roles] = await Promise.all([
    getCustomerByIdAdmin(customerIdNum),
    getAllRoles(),
  ]);

  if ('error' in customer) {
    notFound();
  }

  if ('error' in roles) {
    return (
      <div className="text-center py-12">
        <p className="text-destructive">{roles.error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <Link href={`/${locale}/admin/customers`}>
        <Button variant="ghost" size="sm">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Customers
        </Button>
      </Link>

      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold">{customer.full_name}</h1>
        <p className="text-muted-foreground mt-2">{customer.email}</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Customer Info & Orders */}
        <div className="lg:col-span-2 space-y-6">
          {/* Customer Info */}
          <Card>
            <CardHeader>
              <CardTitle>Customer Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Full Name</p>
                  <p className="font-medium">{customer.full_name}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Email</p>
                  <p className="font-medium">{customer.email}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Phone</p>
                  <p className="font-medium">{customer.phone || 'N/A'}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Preferred Currency</p>
                  <p className="font-medium">{customer.preferred_currency}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Member Since</p>
                  <p className="font-medium">{formatDate(customer.created)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Recent Orders */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Orders</CardTitle>
            </CardHeader>
            <CardContent>
              {customer.orders.length === 0 ? (
                <p className="text-center text-muted-foreground py-8">No orders yet</p>
              ) : (
                <div className="space-y-4">
                  {customer.orders.map((order) => (
                    <Link
                      key={order.id}
                      href={`/${locale}/admin/orders/${order.id}`}
                      className="block p-4 border rounded-lg hover:bg-accent transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">Order #{order.id.slice(0, 8)}</p>
                          <p className="text-sm text-muted-foreground">
                            {formatDate(order.created)}
                          </p>
                        </div>
                        <div className="text-right space-y-2">
                          <p className="font-bold">
                            {formatCurrency(Number(order.total_amount), order.currency)}
                          </p>
                          <Badge
                            variant={
                              order.status === 'unpaid'
                                ? 'secondary'
                                : order.status === 'paid'
                                ? 'default'
                                : order.status === 'processing'
                                ? 'default'
                                : 'outline'
                            }
                          >
                            {ORDER_STATUS_CONFIG[order.status as keyof typeof ORDER_STATUS_CONFIG]?.label || order.status}
                          </Badge>
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Role Management */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>{t('assignRoles')}</CardTitle>
            </CardHeader>
            <CardContent>
              <RoleAssignmentForm
                customerId={customer.id}
                currentRoles={customer.roles}
                availableRoles={roles}
                locale={locale}
              />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
