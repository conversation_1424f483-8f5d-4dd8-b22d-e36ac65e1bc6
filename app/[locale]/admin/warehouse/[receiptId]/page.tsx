// app/[locale]/admin/warehouse/[receiptId]/page.tsx
// Warehouse receipt detail page

import { notFound } from 'next/navigation';
import { getTranslations } from 'next-intl/server';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { TrackingInfoForm } from '@/components/admin/TrackingInfoForm';
import { getWarehouseReceiptById } from '@/lib/actions/admin/warehouse.actions';
import { formatDate } from '@/lib/utils';

interface PageProps {
  params: Promise<{ locale: string; receiptId: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  return {
    title: t('receiptDetails'),
  };
}

const STATUS_COLORS = {
  pending: 'bg-yellow-500',
  matched: 'bg-blue-500',
  shipped: 'bg-green-500',
};

export default async function WarehouseReceiptDetailPage({ params }: PageProps) {
  const { locale, receiptId } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  const receipt = await getWarehouseReceiptById(receiptId, locale);

  if ('error' in receipt) {
    notFound();
  }

  const statusColor = STATUS_COLORS[receipt.status];
  const statusKey = `status${receipt.status.charAt(0).toUpperCase() + receipt.status.slice(1)}` as 'statusPending' | 'statusMatched' | 'statusShipped';
  const statusLabel = t(statusKey);
  const productName = receipt.order_item.product.translations[0]?.name || receipt.order_item.product.original_name;
  const variantName = receipt.order_item.variant?.translations[0]?.variant_name || receipt.order_item.variant?.original_variant_name;

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <Link href={`/${locale}/admin/warehouse`}>
        <Button variant="ghost" size="sm">
          <ArrowLeft className="h-4 w-4 mr-2" />
          {t('backToWarehouse')}
        </Button>
      </Link>

      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">
            {t('receipt')} #{receipt.id.slice(0, 8)}
          </h1>
          <p className="text-muted-foreground mt-2">
            {t('received')}: {formatDate(receipt.received_at)}
          </p>
        </div>
        <Badge className={statusColor}>
          {statusLabel}
        </Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Receipt Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Package Info */}
          <Card>
            <CardHeader>
              <CardTitle>{t('packageInformation')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">{t('marketplaceOrderId')}</p>
                  <p className="font-medium">{receipt.marketplace_order_id}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">{t('packageWeight')}</p>
                  <p className="font-medium">
                    {Number(receipt.package_weight)} {receipt.package_weight_unit}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">{t('receivedAt')}</p>
                  <p className="font-medium">{formatDate(receipt.received_at)}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">{t('status')}</p>
                  <Badge className={statusColor}>
                    {statusLabel}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Order Item Info */}
          <Card>
            <CardHeader>
              <CardTitle>{t('orderItemDetails')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">{t('product')}</p>
                  <p className="font-medium">{productName}</p>
                  {variantName && (
                    <p className="text-sm text-muted-foreground">{variantName}</p>
                  )}
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">{t('quantity')}</p>
                  <p className="font-medium">{Number(receipt.order_item.quantity)}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">{t('orderId')}</p>
                  <Link
                    href={`/${locale}/admin/orders/${receipt.order_item.order.id}`}
                    className="font-medium text-primary hover:underline"
                  >
                    #{receipt.order_item.order.id.slice(0, 8)}
                  </Link>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">{t('customer')}</p>
                  <p className="font-medium">{receipt.order_item.order.customer.full_name}</p>
                  <p className="text-sm text-muted-foreground">{receipt.order_item.order.customer.email}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Tracking Info */}
          {receipt.status === 'shipped' && (
            <Card>
              <CardHeader>
                <CardTitle>{t('trackingInformation')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">{t('trackingNumber')}</p>
                    <p className="font-medium">{receipt.tracking_number || t('notAvailable')}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">{t('carrier')}</p>
                    <p className="font-medium">{receipt.carrier || t('notAvailable')}</p>
                  </div>
                  {receipt.shipping_label_url && (
                    <div className="col-span-2">
                      <p className="text-sm text-muted-foreground">{t('shippingLabel')}</p>
                      <a
                        href={receipt.shipping_label_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary hover:underline"
                      >
                        {t('viewLabel')}
                      </a>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Actions */}
        <div>
          {receipt.status === 'matched' && (
            <Card>
              <CardHeader>
                <CardTitle>{t('addTracking')}</CardTitle>
              </CardHeader>
              <CardContent>
                <TrackingInfoForm receiptId={receipt.id} locale={locale} />
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}

