// app/[locale]/admin/warehouse/new/page.tsx
// Create new warehouse receipt page

import { getTranslations } from 'next-intl/server';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { WarehouseReceiptForm } from '@/components/admin/WarehouseReceiptForm';

interface PageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  return {
    title: t('createReceipt'),
  };
}

export default async function NewWarehouseReceiptPage({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <Link href={`/${locale}/admin/warehouse`}>
        <Button variant="ghost" size="sm">
          <ArrowLeft className="h-4 w-4 mr-2" />
          {t('backToWarehouse')}
        </Button>
      </Link>

      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold">{t('createReceipt')}</h1>
        <p className="text-muted-foreground mt-2">
          {t('newReceiptDescription')}
        </p>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle>{t('receiptDetails')}</CardTitle>
        </CardHeader>
        <CardContent>
          <WarehouseReceiptForm locale={locale} />
        </CardContent>
      </Card>
    </div>
  );
}

