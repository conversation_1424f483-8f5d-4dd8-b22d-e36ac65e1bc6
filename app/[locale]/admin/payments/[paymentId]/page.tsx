// app/[locale]/admin/payments/[paymentId]/page.tsx
// Payment detail page

import { notFound } from 'next/navigation';
import { getTranslations } from 'next-intl/server';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { PaymentStatusForm } from '@/components/admin/PaymentStatusForm';
import { getPaymentById } from '@/lib/actions/admin/payment.actions';
import { formatCurrency, formatDate } from '@/lib/utils';

interface PageProps {
  params: Promise<{ locale: string; paymentId: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  return {
    title: t('paymentDetails'),
  };
}

const STATUS_COLORS = {
  pending: 'bg-yellow-500',
  succeeded: 'bg-green-500',
  failed: 'bg-red-500',
  refunded: 'bg-gray-500',
};

export default async function PaymentDetailPage({ params }: PageProps) {
  const { locale, paymentId } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  const payment = await getPaymentById(paymentId);

  if ('error' in payment) {
    notFound();
  }

  const statusColor = STATUS_COLORS[payment.status];
  const statusKey = `status${payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}` as 'statusPending' | 'statusSucceeded' | 'statusFailed' | 'statusRefunded';
  const statusLabel = t(statusKey);

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <Link href={`/${locale}/admin/payments`}>
        <Button variant="ghost" size="sm">
          <ArrowLeft className="h-4 w-4 mr-2" />
          {t('backToPayments')}
        </Button>
      </Link>

      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">
            {t('payment')} #{payment.id.slice(0, 8)}
          </h1>
          <p className="text-muted-foreground mt-2">
            {t('created')}: {formatDate(payment.created)}
          </p>
        </div>
        <Badge className={statusColor}>
          {statusLabel}
        </Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Payment Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Payment Info */}
          <Card>
            <CardHeader>
              <CardTitle>{t('paymentInformation')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">{t('amount')}</p>
                  <p className="font-bold text-2xl">
                    {formatCurrency(Number(payment.amount), payment.currency)}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">{t('status')}</p>
                  <Badge className={statusColor}>
                    {statusLabel}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">{t('paymentMethod')}</p>
                  <p className="font-medium">{payment.payment_method}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">{t('transactionId')}</p>
                  <p className="font-medium font-mono text-sm">{payment.transaction_id}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">{t('created')}</p>
                  <p className="font-medium">{formatDate(payment.created)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Order Info */}
          <Card>
            <CardHeader>
              <CardTitle>{t('orderInformation')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">{t('orderId')}</p>
                  <Link
                    href={`/${locale}/admin/orders/${payment.order.id}`}
                    className="font-medium text-primary hover:underline"
                  >
                    #{payment.order.id.slice(0, 8)}
                  </Link>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">{t('orderStatus')}</p>
                  <p className="font-medium capitalize">{payment.order.status}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">{t('orderTotal')}</p>
                  <p className="font-medium">
                    {formatCurrency(Number(payment.order.total_amount), payment.order.currency)}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">{t('customer')}</p>
                  <p className="font-medium">{payment.order.customer.full_name}</p>
                  <p className="text-sm text-muted-foreground">{payment.order.customer.email}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Actions */}
        <div>
          {payment.status === 'pending' && (
            <Card>
              <CardHeader>
                <CardTitle>{t('updateStatus')}</CardTitle>
              </CardHeader>
              <CardContent>
                <PaymentStatusForm paymentId={payment.id} locale={locale} />
              </CardContent>
            </Card>
          )}

          {payment.status === 'succeeded' && (
            <Card>
              <CardHeader>
                <CardTitle>{t('refund')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  {t('refundDescription')}
                </p>
                <PaymentStatusForm
                  paymentId={payment.id}
                  locale={locale}
                  isRefund={true}
                />
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}

