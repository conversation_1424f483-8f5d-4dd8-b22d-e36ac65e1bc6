// app/[locale]/admin/pricing/[ruleId]/preview/page.tsx
// Pricing rule preview page

export const dynamic = 'force-dynamic';

import { getTranslations } from 'next-intl/server';
import { getPricingRulePreview } from '@/lib/actions/admin/pricing.actions';
import { PricingRulePreview } from '@/components/admin/PricingRulePreview';

interface PageProps {
  params: Promise<{ locale: string; ruleId: string }>;
  searchParams: Promise<{ cursor?: string; limit?: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  return {
    title: t('pricingRulePreview'),
  };
}

export default async function PricingRulePreviewPage({ params, searchParams }: PageProps) {
  const { locale, ruleId } = await params;
  const { cursor, limit } = await searchParams;
  const t = await getTranslations({ locale, namespace: 'admin' });
  const tPricing = await getTranslations({ locale, namespace: 'pricing' });

  const limitNumber = limit ? parseInt(limit) : 50;
  const previewData = await getPricingRulePreview(ruleId, cursor, limitNumber);

  if ('error' in previewData) {
    return (
      <div className="text-center py-12">
        <p className="text-destructive">{previewData.error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold">{t('pricingRulePreview')}</h1>
        <p className="text-muted-foreground mt-2">
          {tPricing('previewDescription')}
        </p>
      </div>

      {/* Rule Info */}
      <div className="bg-muted p-4 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">{previewData.rule.rule_name}</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="font-medium">{tPricing('conditionType')}:</span>
            <p>{previewData.rule.condition_type}</p>
            {previewData.rule.condition_value && (
              <p className="text-muted-foreground">{previewData.rule.condition_value}</p>
            )}
          </div>
          <div>
            <span className="font-medium">{tPricing('markupType')}:</span>
            <p>{previewData.rule.markup_type}</p>
          </div>
          <div>
            <span className="font-medium">{tPricing('markupValue')}:</span>
            <p>
              {previewData.rule.markup_type === 'PERCENTAGE'
                ? `${Number(previewData.rule.markup_value)}%`
                : `$${Number(previewData.rule.markup_value)}`
              }
            </p>
          </div>
          <div>
            <span className="font-medium">{tPricing('priority')}:</span>
            <p>{previewData.rule.priority}</p>
          </div>
        </div>
      </div>

      {/* Preview Component */}
      <PricingRulePreview
        affectedProducts={previewData.affectedProducts}
        pagination={previewData.pagination}
        ruleId={ruleId}
        locale={locale}
      />
    </div>
  );
}