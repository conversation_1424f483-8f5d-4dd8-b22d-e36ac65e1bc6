// app/[locale]/admin/pricing/page.tsx
// Admin pricing management page

export const dynamic = 'force-dynamic';

import { getTranslations } from 'next-intl/server';
import { PricingRulesTable } from '@/components/admin/PricingRulesTable';
import { getAllPricingRules } from '@/lib/actions/admin/pricing.actions';

interface PageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  return {
    title: t('pricingManagement'),
  };
}

export default async function AdminPricingPage({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  const rules = await getAllPricingRules();

  if ('error' in rules) {
    return (
      <div className="text-center py-12">
        <p className="text-destructive">{rules.error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold">{t('pricingManagement')}</h1>
        <p className="text-muted-foreground mt-2">
          Manage pricing rules and markups
        </p>
      </div>

      {/* Pricing Rules Table */}
      <PricingRulesTable rules={rules} locale={locale} />
    </div>
  );
}
