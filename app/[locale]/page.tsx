// app/[locale]/page.tsx
// Homepage with personalized featured products pricing

// ISR (Incremental Static Regeneration) - revalidate every 1 hour
// This allows the page to be statically generated but updated periodically
export const revalidate = 3600; // 1 hour in seconds

// Static generation - fully static homepage with client-side pricing personalization
export async function generateStaticParams() {
  const { LOCALES } = await import('@/lib/constants');
  return LOCALES.map(locale => ({ locale }));
}

import { getTranslations } from 'next-intl/server';
import { HomePageContent } from '@/components/home/<USER>';
import { getFeaturedProducts, getCategories } from '@/lib/actions/product.actions';

interface PageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'home' });
  const { SITE_CONFIG, LOCALES } = await import('@/lib/constants');

  const title = t('seoTitle') || 'Chinese Marketplace - Premium Products from Taobao, Pinduoduo & Alibaba';
  const description = t('seoDescription') || 'Discover authentic Chinese products with fast shipping worldwide. Shop electronics, fashion, and more from trusted marketplaces like Taobao, Pinduoduo, and Alibaba.';
  const canonicalUrl = `${SITE_CONFIG.url}/${locale}`;

  return {
    title,
    description,
    keywords: ['Chinese marketplace', 'Taobao', 'Pinduoduo', 'Alibaba', 'e-commerce', 'shopping', 'China products', 'international shipping', 'African marketplace', 'shopping in Africa', 'products for Africa', 'African e-commerce', 'import from China to Africa', 'African customers'],
    alternates: {
      canonical: canonicalUrl,
      languages: LOCALES.reduce((acc, lang) => {
        acc[lang] = `${SITE_CONFIG.url}/${lang}`;
        return acc;
      }, {} as Record<string, string>),
    },
    openGraph: {
      title,
      description,
      type: 'website',
      url: canonicalUrl,
      locale: locale,
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
    },
  };
}

export default async function HomePage({ params }: PageProps) {
  const { locale } = await params;

  // Use default currency for ISR - pricing will be shown in XAF (Central African CFA Franc)
  const { DEFAULT_CURRENCY } = await import('@/lib/constants');

  // Fetch featured products and categories
  const [featuredProducts, categories] = await Promise.all([
    getFeaturedProducts(locale, 8, DEFAULT_CURRENCY),
    getCategories(locale),
  ]);

  return (
    <HomePageContent
      featuredProducts={featuredProducts}
      categories={categories}
      locale={locale}
    />
  );
}