'use client';

// app/[locale]/error.tsx
// Global error boundary with internationalization

import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface ErrorPageProps {
  error: Error;
  reset: () => void;
}

export default function ErrorPage({ error, reset }: ErrorPageProps) {
  const t = useTranslations('common');
  const router = useRouter();

  // Extract locale from pathname
  const locale = typeof window !== 'undefined'
    ? window.location.pathname.split('/')[1]
    : 'en';

  const handleRefresh = () => {
    window.location.reload();
  };

  const handleGoHome = () => {
    router.push(`/${locale}`);
  };

  return (
    <div className="min-h-[60vh] flex items-center justify-center p-4">
      <Card className="max-w-md w-full">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <AlertTriangle className="h-16 w-16 text-destructive" />
          </div>
          <CardTitle className="text-xl">
            {t('somethingWentWrong')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-muted-foreground text-center">
            {t('somethingWentWrongDesc')}
          </p>

          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button onClick={reset} className="w-full sm:w-auto">
              <RefreshCw className="mr-2 h-4 w-4" />
              {t('tryAgain')}
            </Button>

            <Button variant="outline" onClick={handleRefresh} className="w-full sm:w-auto">
              <RefreshCw className="mr-2 h-4 w-4" />
              {t('refreshPage')}
            </Button>
          </div>

          <div className="text-center">
            <Button variant="ghost" onClick={handleGoHome}>
              <Home className="mr-2 h-4 w-4" />
              {t('goHome')}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}