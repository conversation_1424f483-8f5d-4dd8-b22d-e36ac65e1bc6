// app/[locale]/account/profile/page.tsx
// Profile management page with client-side data fetching

import { getTranslations } from 'next-intl/server';
import { ProfileForm } from '@/components/account/ProfileForm';
import { AddressList } from '@/components/account/AddressList';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface PageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'account' });

  return {
    title: t('profile'),
  };
}

export default async function ProfilePage({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'account' });

  return (
    <div className="space-y-8 py-2">
      {/* Profile Information */}
      <div className="animate-in fade-in slide-in-from-top-2 duration-500">
        <Card className="border-0 shadow-sm hover:shadow-md transition-shadow duration-300">
          <CardHeader className="pb-4">
            <CardTitle className="text-2xl font-bold tracking-tight">{t('profileInformation')}</CardTitle>
            <p className="text-sm text-muted-foreground mt-1">{t('manageProfileDescription')}</p>
          </CardHeader>
          <CardContent>
            <ProfileForm locale={locale} />
          </CardContent>
        </Card>
      </div>

      {/* Addresses */}
      <div className="animate-in fade-in slide-in-from-top-2 duration-500 delay-100">
        <Card className="border-0 shadow-sm hover:shadow-md transition-shadow duration-300">
          <CardHeader className="pb-4">
            <CardTitle className="text-2xl font-bold tracking-tight">{t('savedAddresses')}</CardTitle>
            <p className="text-sm text-muted-foreground mt-1">Manage your delivery addresses</p>
          </CardHeader>
          <CardContent>
            <AddressList locale={locale} />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
