// app/[locale]/account/layout.tsx
// Account layout with sidebar navigation
// NOTE: Authentication is handled by middleware - no need for getCurrentUser() here

import { getTranslations } from 'next-intl/server';
import { AccountNav } from '@/components/account/AccountNav';

interface LayoutProps {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}

export default async function AccountLayout({ children, params }: LayoutProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'account' });

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">{t('myAccount')}</h1>
        <p className="text-muted-foreground mt-2">
          {t('welcomeBack', { name: 'User' })}
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Sidebar Navigation */}
        <aside className="lg:col-span-1">
          <AccountNav locale={locale} />
        </aside>

        {/* Main Content */}
        <main className="lg:col-span-3">{children}</main>
      </div>
    </div>
  );
}
