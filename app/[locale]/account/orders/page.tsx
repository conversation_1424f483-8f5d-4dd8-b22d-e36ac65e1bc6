// app/[locale]/account/orders/page.tsx
// Order history page with SWR + Tanstack Query optimizations

import { getTranslations } from 'next-intl/server';
import OrdersPageClient from './OrdersPageClient';

interface PageProps {
  params: Promise<{ locale: string }>;
  searchParams: Promise<{ cursor?: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'account' });

  return {
    title: t('orders'),
  };
}

export default async function OrdersPage({ params, searchParams }: PageProps) {
  const { locale } = await params;
  const search = await searchParams;

  return <OrdersPageClient locale={locale} initialCursor={search.cursor} />;
}
