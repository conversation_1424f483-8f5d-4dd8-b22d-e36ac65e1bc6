// app/[locale]/account/orders/[orderId]/page.tsx
// Order detail page with SWR + Tanstack Query optimizations

import { getTranslations } from 'next-intl/server';
import OrderDetailClient from './OrderDetailClient';

interface PageProps {
  params: Promise<{ locale: string; orderId: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale, orderId } = await params;
  const t = await getTranslations({ locale, namespace: 'order' });

  return {
    title: `${t('order')} #${orderId.slice(0, 8)}`,
  };
}

export default async function OrderDetailPage({ params }: PageProps) {
  const { locale, orderId } = await params;

  return <OrderDetailClient orderId={orderId} locale={locale} />;
}
