'use client';

import { notFound } from 'next/navigation';
import Link from 'next/link';
import { ImageWithFallback } from '@/components/ui/image-with-fallback';
import { ArrowLeft, RefreshCw } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useOrderDetails } from '@/hooks/queries/useOrderDetails';
import { formatCurrency, formatDate } from '@/lib/utils';
import { ORDER_STATUS_CONFIG } from '@/lib/constants';
import { useTranslations } from 'next-intl';
import type { Address } from '@/lib/types';
// Enhanced UX imports
import { usePrefetchProduct } from '@/hooks/queries/usePrefetchProduct';
import { usePrefetchCart } from '@/hooks/queries/usePrefetchCart';
import { useBatchCartOperations } from '@/hooks/queries/useBatchCartOperations';
import { measureAsync } from '@/lib/utils/performance-monitoring';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect } from 'react';

interface OrderDetailClientProps {
  orderId: string;
  locale: string;
}

export default function OrderDetailClient({ orderId, locale }: OrderDetailClientProps) {
  const t = useTranslations('order');
  const tProducts = useTranslations('products');
  const router = useRouter();

  const { data: order, error, isLoading, refetch, isRefetching } = useOrderDetails(orderId);

  // Enhanced UX hooks
  const { prefetchOnInteraction: prefetchProductOnInteraction } = usePrefetchProduct();
  const { prefetchOnInteraction: prefetchCartOnInteraction, prefetchCart } = usePrefetchCart();
  const batchCartOps = useBatchCartOperations();

  // Performance monitoring for order loading
  useEffect(() => {
    if (order) {
      measureAsync('order-details-load', async () => {
        // Measure the time from component mount to order data available
        return Promise.resolve();
      }, { orderId, locale });
    }
  }, [order, orderId, locale]);

  // Background refresh every 2 minutes for order details
  useEffect(() => {
    const interval = setInterval(() => {
      refetch();
    }, 2 * 60 * 1000); // 2 minutes

    return () => clearInterval(interval);
  }, [refetch]);

  // Batch add to cart functionality for all order items
  const handleBatchAddToCart = useCallback(async () => {
    if (!order?.order_items?.length) return;

    const operations = order.order_items.map(item => ({
      type: 'add' as const,
      productId: Number(item.product_id),
      quantity: Number(item.quantity),
      variantId: item.variant_id ? Number(item.variant_id) : undefined,
      imageUrl: item.image_url || undefined,
    }));

    await batchCartOps.batchOperationsAsync(operations);
  }, [order?.order_items, batchCartOps]);

  // Enhanced retry with performance tracking
  const handleRetry = useCallback(async () => {
    await measureAsync('order-details-retry', () => refetch(), { orderId });
  }, [refetch, orderId]);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-10 bg-muted rounded w-48"></div>
          <div className="h-8 bg-muted rounded w-96 mt-4"></div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
            <div className="lg:col-span-2">
              <div className="h-96 bg-muted rounded"></div>
            </div>
            <div className="space-y-4">
              <div className="h-48 bg-muted rounded"></div>
              <div className="h-48 bg-muted rounded"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <Link href={`/${locale}/account/orders`}>
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('backToOrders')}
          </Button>
        </Link>
        <Card>
          <CardContent className="text-center py-12">
            <p className="text-muted-foreground mb-4">{t('errorLoadingOrder')}</p>
            <div className="flex gap-2 justify-center">
              <Button onClick={handleRetry} disabled={isRefetching}>
                {isRefetching ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    {t('retry')}
                  </>
                ) : (
                  t('retry')
                )}
              </Button>
              <Button variant="outline" onClick={() => router.back()}>
                {t('goBack')}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!order) {
    notFound();
  }

  const statusConfig = ORDER_STATUS_CONFIG[order.status as keyof typeof ORDER_STATUS_CONFIG];
  const shippingAddress = order.shipping_address as unknown as Address;

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <div className="flex items-center justify-between">
        <Link href={`/${locale}/account/orders`}>
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('backToOrders')}
          </Button>
        </Link>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRetry}
            disabled={isRefetching}
          >
            {isRefetching ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
          </Button>
          <Button
            variant="default"
            size="sm"
            onClick={handleBatchAddToCart}
            disabled={batchCartOps.isPending}
            {...prefetchCartOnInteraction()}
          >
            {batchCartOps.isPending ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                {t('addingToCart')}
              </>
            ) : (
              t('reorderAll')
            )}
          </Button>
        </div>
      </div>

      {/* Order Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold">
            {t('order')} #{order.id.slice(0, 8)}
          </h1>
          <p className="text-muted-foreground mt-1">
            {t('placed')}: {formatDate(order.created)}
          </p>
        </div>
        <Badge variant={statusConfig?.color === 'green' ? 'default' : 'secondary'} className="text-base px-4 py-2">
          {statusConfig?.label || order.status}
        </Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Order Items */}
        <div className="lg:col-span-2 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t('orderItems')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {order.order_items.map((item) => {
                const translation = item.product?.translations[0];
                const productName = translation?.name || item.product?.original_name || 'Product';
                const imageUrl = item.image_url || item.product?.product_images[0]?.image_url;
                const variantTranslation = item.variant?.translations[0];
                const variantName = variantTranslation?.variant_name || item.variant?.original_variant_name;

                const productSlug = item.product?.translations?.[0]?.slug;
                const prefetchHandlers = productSlug ? prefetchProductOnInteraction(productSlug, locale) : {};

                return (
                  <div key={item.id} className="flex gap-4">
                    {/* Image */}
                    <div className="relative w-20 h-20 flex-shrink-0 rounded-md overflow-hidden bg-muted">
                      {imageUrl ? (
                        <Link
                          href={productSlug ? `/${locale}/products/${productSlug}` : '#'}
                          {...prefetchHandlers}
                        >
                          <ImageWithFallback
                            src={imageUrl}
                            alt={productName}
                            className="object-cover hover:opacity-80 transition-opacity"
                            sizes="80px"
                            fallbackText="Image unavailable"
                          />
                        </Link>
                      ) : (
                        <div className="flex h-full items-center justify-center text-xs text-muted-foreground">
                          {tProducts('noImage')}
                        </div>
                      )}
                    </div>

                    {/* Details */}
                    <div className="flex-1">
                      {productSlug ? (
                        <Link
                          href={`/${locale}/products/${productSlug}`}
                          className="hover:text-primary transition-colors"
                          {...prefetchHandlers}
                        >
                          <p className="font-semibold">{productName}</p>
                        </Link>
                      ) : (
                        <p className="font-semibold">{productName}</p>
                      )}
                      {variantName && (
                        <p className="text-sm text-muted-foreground">{variantName}</p>
                      )}
                      <p className="text-sm text-muted-foreground mt-1">
                        {t('quantity')}: {item.quantity.toString()}
                      </p>
                    </div>

                    {/* Price */}
                    <div className="text-right">
                      <p className="font-semibold">
                        {formatCurrency(Number(item.price_per_unit) * Number(item.quantity), order.currency)}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {formatCurrency(Number(item.price_per_unit), order.currency)} {t('each')}
                      </p>
                    </div>
                  </div>
                );
              })}
            </CardContent>
          </Card>
        </div>

        {/* Order Summary & Shipping */}
        <div className="lg:col-span-1 space-y-4">
          {/* Order Summary */}
          <Card>
            <CardHeader>
              <CardTitle>{t('orderSummary')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">{t('subtotal')}</span>
                <span>
                  {formatCurrency(
                    Number(order.total_amount) - Number(order.shipping_cost || 0),
                    order.currency
                  )}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">{t('shipping')}</span>
                <span>{formatCurrency(Number(order.shipping_cost || 0), order.currency)}</span>
              </div>
              <Separator />
              <div className="flex justify-between font-bold">
                <span>{t('total')}</span>
                <span>{formatCurrency(Number(order.total_amount), order.currency)}</span>
              </div>
            </CardContent>
          </Card>

          {/* Shipping Address */}
          <Card>
            <CardHeader>
              <CardTitle>{t('shippingAddress')}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {shippingAddress ? (
                  <div className="text-sm space-y-1">
                    <p className="font-semibold">{shippingAddress.fullName}</p>
                    <p>{shippingAddress.phone}</p>
                    {shippingAddress.isWhatsApp && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                        WhatsApp
                      </span>
                    )}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">{t('noAddress')}</p>
                )}

                {/* Shipping Method */}
                <div className="border-t pt-4">
                  <h4 className="font-semibold text-sm mb-2">{t('shippingMethod')}</h4>
                  <div className="flex items-center justify-between">
                    <span className="text-sm capitalize">{order.shipping_method}</span>
                    <Badge variant="outline" className="text-xs">
                      {order.shipping_method === 'boat' ? '~2 months' : '~2 weeks'}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Payment Status */}
          {order.payments && order.payments.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>{t('paymentStatus')}</CardTitle>
              </CardHeader>
              <CardContent>
                {order.payments.map((payment) => (
                  <div key={payment.id} className="text-sm space-y-1">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">{t('method')}</span>
                      <span className="font-medium">{payment.payment_method}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">{t('status')}</span>
                      <Badge variant="secondary">{payment.status}</Badge>
                    </div>
                    {payment.amount && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">{t('amount')}</span>
                        <span className="font-medium">
                          {formatCurrency(Number(payment.amount), order.currency)}
                        </span>
                      </div>
                    )}
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>{t('quickActions')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {order.status === 'unpaid' && (
                <Button
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                  onClick={() => router.push(`/${locale}/payment?orderId=${order.id}`)}
                >
                  {t('continuePayment')}
                </Button>
              )}
              <Button
                className="w-full"
                onClick={handleBatchAddToCart}
                disabled={batchCartOps.isPending}
                {...prefetchCartOnInteraction()}
              >
                {batchCartOps.isPending ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    {t('addingToCart')}
                  </>
                ) : (
                  t('reorderAll')
                )}
              </Button>
              <Button
                variant="outline"
                className="w-full"
                onClick={() => router.push(`/${locale}/products`)}
              >
                {t('continueShopping')}
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}