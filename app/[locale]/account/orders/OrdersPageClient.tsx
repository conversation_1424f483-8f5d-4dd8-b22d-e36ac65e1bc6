'use client';

import Link from 'next/link';
import { Package, RefreshCw } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useOrders } from '@/hooks/queries/useOrders';
import { formatCurrency, formatDate } from '@/lib/utils';
import { ORDER_STATUS_CONFIG } from '@/lib/constants';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { usePrefetchOrderDetails } from '@/hooks/queries';

interface OrdersPageClientProps {
  locale: string;
  initialCursor?: string;
}

export default function OrdersPageClient({ locale, initialCursor }: OrdersPageClientProps) {
  const t = useTranslations('account');
  const router = useRouter();

  // Use the orders query hook with Tanstack Query
  const {
    data,
    error,
    isLoading,
    isFetchingNextPage,
    fetchNextPage,
    hasNextPage,
    refetch,
    isRefetching,
  } = useOrders();

  // Prefetch order details on interaction
  const { prefetchOnInteraction } = usePrefetchOrderDetails();

  // Handle initial cursor-based pagination
  useEffect(() => {
    if (initialCursor && data?.pages.length === 1) {
      // If we have an initial cursor, we might need to fetch with that cursor
      // This is handled by the useOrders hook's queryFn
    }
  }, [initialCursor, data]);

  // Background refresh every 5 minutes
  useEffect(() => {
    const interval = setInterval(() => {
      refetch();
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, [refetch]);

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">{t('orderHistory')}</h2>
        </div>
        <Card>
          <CardContent className="text-center py-12">
            <Package className="h-12 w-12 mx-auto mb-4 opacity-50 text-muted-foreground" />
            <p className="text-muted-foreground mb-4">{t('errorLoadingOrders')}</p>
            <Button onClick={() => refetch()} disabled={isRefetching}>
              {isRefetching ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  {t('retry')}
                </>
              ) : (
                t('retry')
              )}
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">{t('orderHistory')}</h2>
        </div>
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                    <div className="space-y-2 flex-1">
                      <div className="h-4 bg-muted rounded w-1/4"></div>
                      <div className="h-3 bg-muted rounded w-1/3"></div>
                      <div className="h-3 bg-muted rounded w-1/2"></div>
                    </div>
                    <div className="h-8 bg-muted rounded w-20"></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  const allOrders = data?.pages.flatMap(page => page?.orders || []) || [];
  const pagination = data?.pages[data.pages.length - 1]?.pagination;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">{t('orderHistory')}</h2>
        <Button
          variant="outline"
          size="sm"
          onClick={() => refetch()}
          disabled={isRefetching}
        >
          {isRefetching ? (
            <RefreshCw className="h-4 w-4 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4" />
          )}
        </Button>
      </div>

      {allOrders.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <Package className="h-12 w-12 mx-auto mb-4 opacity-50 text-muted-foreground" />
            <p className="text-muted-foreground mb-4">{t('noOrders')}</p>
            <Link href={`/${locale}/products`}>
              <Button>{t('startShopping')}</Button>
            </Link>
          </CardContent>
        </Card>
      ) : (
        <>
          <div className="space-y-4">
            {allOrders.map((order) => {
              const statusConfig = ORDER_STATUS_CONFIG[order.status as keyof typeof ORDER_STATUS_CONFIG];
              const prefetchHandlers = prefetchOnInteraction(order.id, locale);

              return (
                <Card key={order.id}>
                  <CardContent className="p-6">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <p className="font-semibold">Order #{order.id.slice(0, 8)}</p>
                          <Badge variant={statusConfig?.color === 'green' ? 'default' : 'secondary'}>
                            {statusConfig?.label || order.status}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {t('placed')}: {formatDate(order.created)}
                        </p>
                        <p className="text-sm">
                          {order.order_items.length} {t('items')} •{' '}
                          <span className="font-semibold">
                            {formatCurrency(Number(order.total_amount), order.currency)}
                          </span>
                        </p>
                      </div>
                      <Link href={`/${locale}/account/orders/${order.id}`}>
                        <Button
                          variant="outline"
                          {...prefetchHandlers}
                        >
                          {t('viewDetails')}
                        </Button>
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Load More Button with prefetching */}
          <div className="flex items-center justify-center gap-2">
            {hasNextPage && (
              <Button
                variant="outline"
                onClick={() => fetchNextPage()}
                disabled={isFetchingNextPage}
              >
                {isFetchingNextPage ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    {t('loading')}
                  </>
                ) : (
                  t('loadMore')
                )}
              </Button>
            )}
          </div>
        </>
      )}
    </div>
  );
}