// app/[locale]/account/favorites/page.tsx
// Wishlist/Favorites page with ordering promotion features

import { getTranslations } from 'next-intl/server';
import { WishlistClient } from '@/components/account/WishlistClient';
import { cookies } from 'next/headers';
import { getWishlistItems, getPreviouslyBoughtItems } from '@/lib/actions/wishlist.actions';

interface PageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'account' });

  return {
    title: t('favorites'),
    description: t('favoritesDescription'),
  };
}

export default async function FavoritesPage({ params }: PageProps) {
  const { locale } = await params;

  // Get user's currency from cookies
  const cookieStore = await cookies();
  const userCurrency = cookieStore.get('preferred_currency')?.value || 'USD';

  // Pre-fetch wishlist and previously bought items on the server
  let initialWishlistItems = null;
  let initialPreviouslyBoughtItems = null;

  try {
    [initialWishlistItems, initialPreviouslyBoughtItems] = await Promise.all([
      getWishlistItems(userCurrency),
      getPreviouslyBoughtItems(userCurrency),
    ]);
  } catch (error) {
    console.error('Error pre-fetching wishlist data:', error);
    // Continue with null initial data - client will handle loading
  }

  return (
    <WishlistClient
      locale={locale}
      userCurrency={userCurrency}
      initialWishlistItems={initialWishlistItems || []}
      initialPreviouslyBoughtItems={initialPreviouslyBoughtItems || []}
    />
  );
}
