// app/[locale]/account/page.tsx
// A redesigned, responsive, and beautiful user dashboard screen.

'use client';

import { useTranslations } from 'next-intl';
import { useEffect, use } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import {
  Package,
  User,
  MapPin,
  Heart,
  ChevronRight,
  RefreshCw,
  AlertTriangle,
  ShoppingBag,
} from 'lucide-react';

// ShadCN UI Components
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';

// Project-specific Imports
import { formatCurrency, formatDate } from '@/lib/utils';
import { ORDER_STATUS_CONFIG } from '@/lib/constants';
import { useOrders } from '@/hooks/queries/useOrders';
import { useAddresses } from '@/hooks/queries/useAddresses';
import { useProfile } from '@/hooks/queries/useProfile';
import { useWishlist } from '@/hooks/queries/useWishlist';
import { UnpaidOrdersBanner } from '@/components/ui/unpaid-orders-banner';
import { useCurrency } from '@/components/providers/CurrencyProvider';
import { usePrefetchCart } from '@/hooks/queries/usePrefetchCart';
import { usePrefetchOrderDetails } from '@/hooks/queries/usePrefetchOrderDetails';

interface AccountDashboardPageProps {
  params: Promise<{ locale: string }>;
}

export default function AccountDashboardPage({ params }: AccountDashboardPageProps) {
  const router = useRouter();
  const t = useTranslations('account');
  const { currency } = useCurrency();

  // Await the params in client component
  const { locale } = use(params);

  // Prefetch hooks for improved UX
  const { prefetchCart } = usePrefetchCart();
  const { prefetchOrderDetails } = usePrefetchOrderDetails();

  // Optimized data fetching hooks
  const { data: ordersData, isLoading: ordersLoading, error: ordersError } = useOrders(true);
  const { data: addresses, isLoading: addressesLoading, error: addressesError } = useAddresses();
  const { data: profile, isLoading: profileLoading, error: profileError } = useProfile();
  const { wishlistItems, isLoading: wishlistLoading, error: wishlistError, backgroundRefresh } = useWishlist(currency);

  // Enable background refresh for real-time updates
  useEffect(() => {
    const cleanup = backgroundRefresh(true);
    return cleanup;
  }, [backgroundRefresh]);

  const recentOrders = ordersData?.pages?.flatMap(page => page?.orders || []).slice(0, 3) || [];
  const totalOrders = ordersData?.pages?.reduce((acc, page) => acc + (page?.orders?.length || 0), 0) || 0;

  // --- Loading State ---
  if (ordersLoading || addressesLoading || profileLoading || wishlistLoading) {
    return <DashboardSkeleton />;
  }

  // --- Error State ---
  if (ordersError || addressesError || profileError || wishlistError) {
    return (
      <Card className="mt-6">
        <CardContent className="pt-6 text-center">
          <div className="mx-auto bg-destructive/10 p-3 rounded-full w-fit">
            <AlertTriangle className="h-8 w-8 text-destructive" />
          </div>
          <h2 className="mt-4 text-xl font-semibold">{t('somethingWentWrong')}</h2>
          <p className="mt-2 text-muted-foreground">{t('dashboardDataError')}</p>
          <div className="text-destructive/80 text-sm mt-4">
            {ordersError && <p>Orders Error: {ordersError.message}</p>}
            {addressesError && <p>Address Error: {addressesError.message}</p>}
            {profileError && <p>Profile Error: {profileError.message}</p>}
            {wishlistError && <p>Wishlist Error: {wishlistError.message}</p>}
          </div>
          <Button onClick={() => window.location.reload()} variant="outline" className="mt-6">
            <RefreshCw className="h-4 w-4 mr-2" />
            {t('tryAgain')}
          </Button>
        </CardContent>
      </Card>
    );
  }

  // --- Main Dashboard UI ---
  return (
    <div className="space-y-8">
      {/* Unpaid Orders Reminder Banner */}
      <div className="sticky top-16 z-40">
        <UnpaidOrdersBanner locale={locale} />
      </div>

      {/* Welcome Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">
          {t('welcome')}, {profile?.fullName?.split(' ')[0] || 'User'}!
        </h1>
        <p className="text-muted-foreground">{t('dashboardOverview')}</p>
      </div>

      {/* Quick Stats Grid */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <StatCard icon={<Package className="h-6 w-6 text-primary" />} title={t('totalOrders')} value={totalOrders} />
        <StatCard icon={<Heart className="h-6 w-6 text-primary" />} title={t('wishlistItems')} value={wishlistItems?.length || 0} />
        <StatCard icon={<MapPin className="h-6 w-6 text-primary" />} title={t('savedAddresses')} value={addresses?.length || 0} />
        <StatCard icon={<User className="h-6 w-6 text-primary" />} title={t('accountStatus')} value={profile?.fullName ? 'Verified' : t('active')} />
      </div>

      {/* Main Content Grid (Recent Orders + Quick Actions) */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Orders Section (takes 2/3 width on large screens) */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>{t('recentOrders')}</CardTitle>
                <CardDescription>{t('recentOrdersDescription')}</CardDescription>
              </div>
              <Link href="/account/orders">
                <Button variant="outline" size="sm">
                  {t('viewAll')}
                </Button>
              </Link>
            </div>
          </CardHeader>
          <CardContent>
            {recentOrders.length === 0 ? (
              <div className="text-center py-12 text-muted-foreground">
                <ShoppingBag className="h-16 w-16 mx-auto mb-4 opacity-30" />
                <h3 className="text-lg font-semibold text-foreground">{t('noOrders')}</h3>
                <p className="mt-1">{t('noOrdersDescription')}</p>
                <Link href="/products">
                  <Button className="mt-6">{t('startShopping')}</Button>
                </Link>
              </div>
            ) : (
              <div className="space-y-4">
                {recentOrders.map((order) => {
                  const statusConfig = ORDER_STATUS_CONFIG[order.status as keyof typeof ORDER_STATUS_CONFIG] || { label: order.status, color: 'gray' };
                  return (
                    <Link
                      key={order.id}
                      href={`/account/orders/${order.id}`}
                      className="block p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                      onMouseEnter={() => prefetchOrderDetails(order.id, 'en')}
                    >
                      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                        <div className="flex items-center gap-4">
                          {/* Placeholder for Product Image */}
                          <div className="hidden sm:block bg-muted rounded-md h-12 w-12 flex items-center justify-center">
                            <Package className="h-6 w-6 text-muted-foreground" />
                          </div>
                          <div className="space-y-1">
                            <p className="font-semibold text-foreground">
                              Order #{order.id.slice(0, 8).toUpperCase()}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              {formatDate(order.created)} • {order.order_items.length} {t('items')}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-4 w-full sm:w-auto justify-between">
                           <div>
                              <p className="font-semibold text-right">{formatCurrency(Number(order.total_amount), order.currency)}</p>
                              <Badge variant={statusConfig.color === 'green' ? 'default' : 'secondary'}>
                                {statusConfig.label}
                              </Badge>
                           </div>
                          <ChevronRight className="h-5 w-5 text-muted-foreground" />
                        </div>
                      </div>
                    </Link>
                  );
                })}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Quick Actions Section (takes 1/3 width on large screens) */}
        <Card>
          <CardHeader>
            <CardTitle>{t('quickActions')}</CardTitle>
            <CardDescription>{t('quickActionsDescription')}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <ActionLink href="/account/profile" icon={<User className="h-5 w-5" />} title={t('manageProfile')} description={t('manageProfileDescription')} />
            <ActionLink href="/account/orders" icon={<Package className="h-5 w-5" />} title={t('viewOrders')} description={t('viewOrdersDescription')} />
            <ActionLink href="/account/wishlist" onMouseEnter={prefetchCart} icon={<Heart className="h-5 w-5" />} title={t('viewWishlist')} description={t('viewWishlistDescription')} />
            <ActionLink href="/account/addresses" icon={<MapPin className="h-5 w-5" />} title={t('manageAddresses')} description={t('manageAddressesDescription')} />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

// --- Reusable Sub-Components ---

const StatCard = ({ icon, title, value }: { icon: React.ReactNode; title: string; value: string | number }) => (
  <Card>
    <CardContent className="pt-6">
      <div className="flex flex-col items-center text-center space-y-3 sm:flex-row sm:items-center sm:text-left sm:space-y-0 sm:space-x-4">
        <div className="p-3 bg-primary/10 rounded-full">
          {icon}
        </div>
        <div>
          <p className="text-sm text-muted-foreground">{title}</p>
          <p className="text-2xl font-bold">{value}</p>
        </div>
      </div>
    </CardContent>
  </Card>
);

const ActionLink = ({ href, icon, title, description, onMouseEnter }: { href: string; icon: React.ReactNode; title: string; description: string; onMouseEnter?: () => void }) => (
  <Link
    href={href}
    onMouseEnter={onMouseEnter}
    className="flex items-center justify-between p-3 -mx-3 rounded-lg hover:bg-muted/50 transition-colors group"
  >
    <div className="flex items-center gap-4">
      <div className="text-muted-foreground">{icon}</div>
      <div>
        <p className="font-semibold text-foreground">{title}</p>
        <p className="text-sm text-muted-foreground hidden md:block lg:hidden xl:block">{description}</p>
      </div>
    </div>
    <ChevronRight className="h-5 w-5 text-muted-foreground transition-transform group-hover:translate-x-1" />
  </Link>
);


// --- Skeleton Component for Initial Load ---

const DashboardSkeleton = () => (
  <div className="space-y-8 animate-pulse">
    {/* Welcome Header Skeleton */}
    <div>
      <Skeleton className="h-9 w-64 mb-2" />
      <Skeleton className="h-4 w-80" />
    </div>

    {/* Quick Stats Skeleton */}
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      {[...Array(4)].map((_, i) => (
        <Card key={i}>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-4">
              <Skeleton className="h-12 w-12 rounded-full" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-8 w-16" />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>

    {/* Main Content Grid Skeleton */}
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Recent Orders Skeleton */}
      <Card className="lg:col-span-2">
        <CardHeader>
           <Skeleton className="h-6 w-40 mb-2" />
           <Skeleton className="h-4 w-56" />
        </CardHeader>
        <CardContent className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="flex items-center justify-between p-4 border rounded-lg">
               <div className="flex items-center gap-4">
                  <Skeleton className="h-12 w-12 rounded-md" />
                  <div className="space-y-2">
                     <Skeleton className="h-4 w-32" />
                     <Skeleton className="h-3 w-40" />
                  </div>
               </div>
               <Skeleton className="h-8 w-24" />
            </div>
          ))}
        </CardContent>
      </Card>
      
      {/* Quick Actions Skeleton */}
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-32 mb-2" />
          <Skeleton className="h-4 w-48" />
        </CardHeader>
        <CardContent className="space-y-4 pt-2">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="flex items-center gap-4 p-2">
               <Skeleton className="h-8 w-8 rounded" />
               <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-3 w-full" />
               </div>
            </div>
          ))}
        </CardContent>
      </Card>
    </div>
  </div>
);