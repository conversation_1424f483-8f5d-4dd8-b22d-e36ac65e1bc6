// app/[locale]/(auth)/register/page.tsx
// Registration page

import { getTranslations } from 'next-intl/server';
import { RegisterForm } from '@/components/auth/RegisterForm';
import Link from 'next/link';

interface PageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'auth' });

  return {
    title: t('register'),
  };
}

export default async function RegisterPage({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'auth' });

  return (
    <div className="container mx-auto px-4 py-16">
      <div className="max-w-md mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-2">{t('register')}</h1>
          <p className="text-muted-foreground">{t('registerSubtitle')}</p>
        </div>

        <RegisterForm locale={locale} />

        <div className="mt-6 text-center text-sm">
          <span className="text-muted-foreground">{t('haveAccount')} </span>
          <Link
            href={`/${locale}/login`}
            className="font-medium text-primary hover:underline"
          >
            {t('login')}
          </Link>
        </div>
      </div>
    </div>
  );
}
