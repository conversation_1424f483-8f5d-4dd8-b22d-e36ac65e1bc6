// app/[locale]/(auth)/login/page.tsx
// Login page

import { getTranslations } from 'next-intl/server';
import { LoginForm } from '@/components/auth/LoginForm';
import Link from 'next/link';

interface PageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'auth' });

  return {
    title: t('login'),
  };
}

export default async function LoginPage({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'auth' });

  return (
    <div className="container mx-auto px-4 py-16">
      <div className="max-w-md mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-2">{t('login')}</h1>
          <p className="text-muted-foreground">{t('loginSubtitle')}</p>
        </div>

        <LoginForm locale={locale} />

        <div className="mt-6 text-center text-sm">
          <span className="text-muted-foreground">{t('noAccount')} </span>
          <Link
            href={`/${locale}/register`}
            className="font-medium text-primary hover:underline"
          >
            {t('register')}
          </Link>
        </div>
      </div>
    </div>
  );
}
