// app/[locale]/not-found.tsx
// 404 Not Found page with internationalization

import Link from 'next/link';
import { getLocale, getTranslations } from 'next-intl/server';
import { ArrowLeft, Home, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default async function NotFoundPage() {
  const locale = await getLocale();
  const t = await getTranslations({ locale, namespace: 'common' });

  return (
    <div className="min-h-[60vh] flex items-center justify-center p-4">
      <Card className="max-w-md w-full">
        <CardHeader className="text-center">
          <CardTitle className="text-6xl font-bold text-muted-foreground mb-2">
            404
          </CardTitle>
          <CardTitle className="text-xl">
            {t('pageNotFound') || 'Page Not Found'}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-muted-foreground text-center">
            {t('pageNotFoundDesc') || 'The page you are looking for does not exist or has been moved.'}
          </p>

          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Link href={`/${locale}`}>
              <Button className="w-full sm:w-auto">
                <Home className="mr-2 h-4 w-4" />
                {t('goHome') || 'Go Home'}
              </Button>
            </Link>

            <Link href={`/${locale}/products`}>
              <Button variant="outline" className="w-full sm:w-auto">
                <Search className="mr-2 h-4 w-4" />
                {t('browseProducts') || 'Browse Products'}
              </Button>
            </Link>
          </div>

          <div className="text-center">
            <Link href={`/${locale}`}>
              <Button variant="ghost" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                {t('back') || 'Back'}
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export async function generateMetadata() {
  const locale = await getLocale();
  const t = await getTranslations({ locale, namespace: 'common' });

  return {
    title: t('pageNotFound') || '404 - Page Not Found',
    description: t('pageNotFoundDesc') || 'The page you are looking for does not exist.',
  };
}