// app/[locale]/(marketing)/products/page.tsx
// Product listing page with filters and search

import { Suspense } from 'react';
import { getTranslations } from 'next-intl/server';
import { getProducts, getCategories } from '@/lib/actions/product.actions';
import { ProductListingSkeleton } from '@/components/products/ProductListingSkeleton';
import { ProductListingClient } from '@/components/products/ProductListingClient';

// ISR (Incremental Static Regeneration) - revalidate every 30 minutes
// This allows the page to be statically generated but updated periodically
export const revalidate = 1800; // 30 minutes in seconds

// Static generation with dynamic pricing - pre-generate pages, personalize prices client-side
export const dynamicParams = true;

export async function generateStaticParams() {
  const { LOCALES } = await import('@/lib/constants');

  // Generate static pages for popular categories and base listing
  const params = [];

  for (const locale of LOCALES) {
    // Base product listing page
    params.push({ locale });

    // Could add popular categories here in the future
    // const popularCategories = await getPopularCategories(locale);
    // for (const category of popularCategories.slice(0, 5)) {
    //   params.push({ locale, searchParams: { category: category.id.toString() } });
    // }
  }

  return params;
}

interface PageProps {
  params: Promise<{ locale: string }>;
  searchParams: Promise<{
    search?: string;
    category?: string;
    marketplace?: string;
    minPrice?: string;
    maxPrice?: string;
    sortBy?: string;
    page?: string;
  }>;
}

export async function generateMetadata({ params, searchParams }: PageProps) {
  const { locale } = await params;
  const search = await searchParams;
  const t = await getTranslations({ locale, namespace: 'products' });
  const { SITE_CONFIG, LOCALES } = await import('@/lib/constants');

  // Build dynamic title and description based on filters
  let title = t('title');
  let description = t('seoDescription') || 'Browse our extensive collection of authentic Chinese products from trusted marketplaces. Find electronics, fashion, home goods and more with worldwide shipping.';

  if (search.search) {
    title = `${search.search} - ${t('title')}`;
    description = `Search results for "${search.search}". ${description}`;
  } else if (search.category) {
    // Would need to fetch category name, but for now use generic
    title = `Category Products - ${t('title')}`;
    description = `Browse products in this category. ${description}`;
  }

  const urlSearchParams = new URLSearchParams();
  if (search.search) urlSearchParams.set('search', search.search);
  if (search.category) urlSearchParams.set('category', search.category);
  if (search.marketplace) urlSearchParams.set('marketplace', search.marketplace);
  if (search.minPrice) urlSearchParams.set('minPrice', search.minPrice);
  if (search.maxPrice) urlSearchParams.set('maxPrice', search.maxPrice);
  if (search.sortBy) urlSearchParams.set('sortBy', search.sortBy);
  if (search.page) urlSearchParams.set('page', search.page);

  const queryString = urlSearchParams.toString();
  const canonicalUrl = `${SITE_CONFIG.url}/${locale}/products${queryString ? '?' + queryString : ''}`;

  return {
    title,
    description,
    keywords: ['Chinese products', 'Taobao', 'Pinduoduo', 'Alibaba', 'shopping', 'e-commerce', 'international shipping', 'authentic products'],
    alternates: {
      canonical: canonicalUrl,
      languages: LOCALES.reduce((acc, lang) => {
        acc[lang] = `${SITE_CONFIG.url}/${lang}/products${queryString ? '?' + queryString : ''}`;
        return acc;
      }, {} as Record<string, string>),
    },
    openGraph: {
      title,
      description,
      type: 'website',
      url: canonicalUrl,
    },
    twitter: {
      card: 'summary',
      title,
      description,
    },
  };
}

export default async function ProductsPage({ params, searchParams }: PageProps) {
  const { locale } = await params;
  const search = await searchParams;

  const t = await getTranslations({ locale, namespace: 'products' });

  // Use default currency for ISR - prices shown in XAF (Central African CFA Franc)
  const { DEFAULT_CURRENCY } = await import('@/lib/constants');

  // Parse search params
  const filters = {
    search: search.search,
    categoryId: search.category ? parseInt(search.category) : undefined,
    marketplace: search.marketplace,
    minPrice: search.minPrice ? parseFloat(search.minPrice) : undefined,
    maxPrice: search.maxPrice ? parseFloat(search.maxPrice) : undefined,
    sortBy: (search.sortBy as 'newest' | 'price_asc' | 'price_desc' | 'popular') || 'newest',
    page: search.page ? parseInt(search.page) : 1,
  };

  // Fetch data in parallel
  const [productsData, categories] = await Promise.all([
    getProducts(filters, locale, undefined, undefined, DEFAULT_CURRENCY),
    getCategories(locale),
  ]);

  return (
    <div className="container mx-auto px-4 lg:py-8 py-1">
      <Suspense fallback={<ProductListingSkeleton />}>
        <ProductListingClient
          initialProducts={productsData.data}
          initialPagination={productsData.pagination}
          categories={categories}
          locale={locale}
          initialFilters={filters}
        />
      </Suspense>
    </div>
  );
}
