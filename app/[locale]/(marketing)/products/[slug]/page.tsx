// app/[locale]/(marketing)/products/[slug]/page.tsx
// Product detail page with ISR for instant loading - no database queries at request time

// ISR Configuration - revalidate every hour
export const revalidate = 3600; // 1 hour in seconds

// Enable dynamic params for products not yet statically generated
export const dynamicParams = true;

// Generate static params for popular products only (for faster builds)
export async function generateStaticParams() {
  const { LOCALES } = await import('@/lib/constants');
  const { getAllProductSlugsForStaticGeneration } = await import('@/lib/utils/static-generation');

  const params = [];

  for (const locale of LOCALES) {
    // Only pre-generate top 1000 products to keep build times reasonable
    const slugs = await getAllProductSlugsForStaticGeneration(locale, 1000);
    for (const slug of slugs) {
      params.push({ locale, slug });
    }
  }

  return params;
}

import { notFound } from 'next/navigation';
import { RelatedProductsISRSection } from '@/components/products/RelatedProductsISRSection';
import { getProductBySlugStatic } from '@/lib/actions/product-static.actions';
import { ProductDetailISRClient } from '@/components/products/ProductDetailISRClient';

interface PageProps {
  params: Promise<{ locale: string; slug: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale, slug } = await params;
  const product = await getProductBySlugStatic(slug, locale);
  const { SITE_CONFIG, LOCALES, MARKETPLACES } = await import('@/lib/constants');

  if (!product) {
    return {
      title: 'Product Not Found',
    };
  }

  const translation = product.translations[0];
  const productName = translation?.name || product.original_name;

  // Build rich description from product attributes
  const attributes = product.product_attributes.map((attr: { translations: Array<{ attr_key?: string; attr_value?: string }>; original_attr_key: string; original_attr_value: string }) => {
    const attrTranslation = attr.translations[0];
    const key = attrTranslation?.attr_key || attr.original_attr_key;
    const value = attrTranslation?.attr_value || attr.original_attr_value;
    return `${key}: ${value}`;
  }).slice(0, 3).join(', '); // Limit to first 3 attributes

  const marketplace = MARKETPLACES[product.marketplace as keyof typeof MARKETPLACES];

  // Use currency-neutral description for SEO - users will see localized prices on page load
  const description = attributes
    ? `${productName} - ${attributes}. Available from ${marketplace?.name || product.marketplace}. Authentic Chinese product with worldwide shipping.`
    : `${productName} from ${marketplace?.name || product.marketplace}. Authentic Chinese product with worldwide shipping.`;

  const canonicalUrl = `${SITE_CONFIG.url}/${locale}/products/${slug}`;

  return {
    title: `${productName} - ${marketplace?.name || product.marketplace} | MaoMao`,
    description,
    keywords: [
      productName,
      marketplace?.name || product.marketplace,
      'Chinese products',
      'e-commerce',
      'shopping',
      'international shipping',
      ...product.product_attributes.map((attr: { translations: Array<{ attr_value?: string }>; original_attr_value: string }) => attr.translations[0]?.attr_value || attr.original_attr_value).slice(0, 5)
    ].filter(Boolean),
    alternates: {
      canonical: canonicalUrl,
      languages: LOCALES.reduce((acc, lang) => {
        acc[lang] = `${SITE_CONFIG.url}/${lang}/products/${slug}`;
        return acc;
      }, {} as Record<string, string>),
    },
    openGraph: {
      title: `${productName} - ${marketplace?.name || product.marketplace}`,
      description,
      url: canonicalUrl,
      images: product.product_images.slice(0, 4).map((img: { image_url: string; image_type?: string }) => ({
        url: img.image_url,
        alt: `${productName} - ${img.image_type}`,
      })),
    },
    twitter: {
      card: 'summary_large_image',
      title: `${productName} - ${marketplace?.name || product.marketplace}`,
      description,
      images: product.product_images.slice(0, 1).map((img: { image_url: string }) => img.image_url),
    },
  };
}

export default async function ProductDetailPage({ params }: PageProps) {
  const { locale, slug } = await params;

  // Get static product data (no database queries at request time for personalization)
  const product = await getProductBySlugStatic(slug, locale);

  if (!product) {
    notFound();
  }

  return (
    <div className="md:container md:mx-auto md:px-4 md:py-8">
      {/* Main product details - loads instantly with static data */}
      <ProductDetailISRClient product={product} locale={locale} />

      {/* Related Products - loaded client-side to avoid blocking */}
      <RelatedProductsISRSection productSlug={slug} locale={locale} />
    </div>
  );
}
